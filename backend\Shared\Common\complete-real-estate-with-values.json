{"operationType": 5, "includeFeatures": true, "includeObjects": true, "includeMetadata": true, "productData": [{"name": "Real Estate Inventory Management System", "description": "Complete real estate project management system with hierarchical structure matching projectplan.json format", "version": "2.0.0", "isActive": true, "features": [{"name": "Property Management", "description": "Comprehensive property management with full hierarchy support", "isDefault": true, "isActive": true, "metadata": [{"metadataId": "feature-priority-level", "isUnique": false, "isActive": true}, {"metadataId": "feature-complexity-score", "isUnique": false, "isActive": true}], "values": [{"metadataKey": "feature-priority-level", "value": "Critical", "refId": "feature-ref-001"}, {"metadataKey": "feature-complexity-score", "value": "9.5", "refId": "feature-ref-001"}], "objects": [{"name": "Project", "description": "Top-level real estate project - matches projectplan.json projects structure", "isActive": true, "metadata": [{"metadataId": "project-code", "isUnique": true, "isActive": true}, {"metadataId": "project-location", "isUnique": false, "isActive": true}, {"metadataId": "project-total-area", "isUnique": false, "isActive": true}, {"metadataId": "project-estimated-budget", "isUnique": false, "isActive": true}, {"metadataId": "project-status", "isUnique": false, "isActive": true}, {"metadataId": "project-manager", "isUnique": false, "isActive": true}, {"metadataId": "project-developer", "isUnique": false, "isActive": true}], "values": [{"metadataKey": "project-code", "value": "GVR001", "refId": "proj_001"}, {"metadataKey": "project-location", "value": "Sector 45, Gurgaon", "refId": "proj_001"}, {"metadataKey": "project-total-area", "value": "15.5", "refId": "proj_001"}, {"metadataKey": "project-estimated-budget", "value": "50000000000", "refId": "proj_001"}, {"metadataKey": "project-status", "value": "Under Construction", "refId": "proj_001"}, {"metadataKey": "project-manager", "value": "<PERSON><PERSON>", "refId": "proj_001"}, {"metadataKey": "project-developer", "value": "ABC Developers Ltd", "refId": "proj_001"}]}, {"name": "Phase", "description": "Project development phase - matches projectplan.json phases structure", "isActive": true, "metadata": [{"metadataId": "phase-number", "isUnique": false, "isActive": true}, {"metadataId": "phase-start-date", "isUnique": false, "isActive": true}, {"metadataId": "phase-end-date", "isUnique": false, "isActive": true}, {"metadataId": "phase-budget", "isUnique": false, "isActive": true}, {"metadataId": "phase-unit-count", "isUnique": false, "isActive": true}, {"metadataId": "phase-manager", "isUnique": false, "isActive": true}], "values": [{"metadataKey": "phase-number", "value": "1", "refId": "phase_001", "parentObjectValueId": "proj_001"}, {"metadataKey": "phase-start-date", "value": "2023-01-15T00:00:00Z", "refId": "phase_001", "parentObjectValueId": "proj_001"}, {"metadataKey": "phase-end-date", "value": "2025-06-30T00:00:00Z", "refId": "phase_001", "parentObjectValueId": "proj_001"}, {"metadataKey": "phase-budget", "value": "25000000000", "refId": "phase_001", "parentObjectValueId": "proj_001"}, {"metadataKey": "phase-unit-count", "value": "400", "refId": "phase_001", "parentObjectValueId": "proj_001"}, {"metadataKey": "phase-manager", "value": "<PERSON><PERSON>", "refId": "phase_001", "parentObjectValueId": "proj_001"}]}, {"name": "Block", "description": "Construction block within a phase - matches projectplan.json blocks structure", "isActive": true, "metadata": [{"metadataId": "block-number", "isUnique": false, "isActive": true}, {"metadataId": "block-area", "isUnique": false, "isActive": true}, {"metadataId": "block-unit-count", "isUnique": false, "isActive": true}, {"metadataId": "block-completion-percentage", "isUnique": false, "isActive": true}, {"metadataId": "block-budget", "isUnique": false, "isActive": true}], "values": [{"metadataKey": "block-number", "value": "1", "refId": "block_001", "parentObjectValueId": "phase_001"}, {"metadataKey": "block-area", "value": "2.0", "refId": "block_001", "parentObjectValueId": "phase_001"}, {"metadataKey": "block-unit-count", "value": "100", "refId": "block_001", "parentObjectValueId": "phase_001"}, {"metadataKey": "block-completion-percentage", "value": "65.0", "refId": "block_001", "parentObjectValueId": "phase_001"}, {"metadataKey": "block-budget", "value": "6250000000", "refId": "block_001", "parentObjectValueId": "phase_001"}]}, {"name": "Property", "description": "Individual property within a block - matches projectplan.json properties structure", "isActive": true, "metadata": [{"metadataId": "property-code", "isUnique": true, "isActive": true}, {"metadataId": "property-type", "isUnique": false, "isActive": true}, {"metadataId": "property-plot-area", "isUnique": false, "isActive": true}, {"metadataId": "property-base-price", "isUnique": false, "isActive": true}, {"metadataId": "property-inventory-status", "isUnique": false, "isActive": true}, {"metadataId": "property-possession-date", "isUnique": false, "isActive": true}], "values": [{"metadataKey": "property-code", "value": "GVA001", "refId": "prop_001", "parentObjectValueId": "block_001"}, {"metadataKey": "property-type", "value": "Residential", "refId": "prop_001", "parentObjectValueId": "block_001"}, {"metadataKey": "property-plot-area", "value": "2000.0", "refId": "prop_001", "parentObjectValueId": "block_001"}, {"metadataKey": "property-base-price", "value": "8000000", "refId": "prop_001", "parentObjectValueId": "block_001"}, {"metadataKey": "property-inventory-status", "value": "Available", "refId": "prop_001", "parentObjectValueId": "block_001"}, {"metadataKey": "property-possession-date", "value": "2025-06-30T00:00:00Z", "refId": "prop_001", "parentObjectValueId": "block_001"}]}, {"name": "Tower", "description": "Building tower within a property - matches projectplan.json towers structure", "isActive": true, "metadata": [{"metadataId": "tower-code", "isUnique": false, "isActive": true}, {"metadataId": "tower-floor-count", "isUnique": false, "isActive": true}, {"metadataId": "tower-unit-count", "isUnique": false, "isActive": true}, {"metadataId": "tower-height", "isUnique": false, "isActive": true}, {"metadataId": "tower-elevator-count", "isUnique": false, "isActive": true}, {"metadataId": "tower-construction-status", "isUnique": false, "isActive": true}], "values": [{"metadataKey": "tower-code", "value": "TA1", "refId": "tower_001", "parentObjectValueId": "prop_001"}, {"metadataKey": "tower-floor-count", "value": "20", "refId": "tower_001", "parentObjectValueId": "prop_001"}, {"metadataKey": "tower-unit-count", "value": "100", "refId": "tower_001", "parentObjectValueId": "prop_001"}, {"metadataKey": "tower-height", "value": "65.0", "refId": "tower_001", "parentObjectValueId": "prop_001"}, {"metadataKey": "tower-elevator-count", "value": "3", "refId": "tower_001", "parentObjectValueId": "prop_001"}, {"metadataKey": "tower-construction-status", "value": "Under Construction", "refId": "tower_001", "parentObjectValueId": "prop_001"}]}, {"name": "Floor", "description": "Floor within a tower - matches projectplan.json floors structure", "isActive": true, "metadata": [{"metadataId": "floor-number", "isUnique": false, "isActive": true}, {"metadataId": "floor-name", "isUnique": false, "isActive": true}, {"metadataId": "floor-plan", "isUnique": false, "isActive": true}, {"metadataId": "floor-total-area", "isUnique": false, "isActive": true}, {"metadataId": "floor-unit-count", "isUnique": false, "isActive": true}, {"metadataId": "floor-height", "isUnique": false, "isActive": true}, {"metadataId": "floor-construction-status", "isUnique": false, "isActive": true}, {"metadataId": "floor-special-features", "isUnique": false, "isActive": true}], "values": [{"metadataKey": "floor-number", "value": "1", "refId": "floor_001", "parentObjectValueId": "tower_001"}, {"metadataKey": "floor-name", "value": "Ground Floor", "refId": "floor_001", "parentObjectValueId": "tower_001"}, {"metadataKey": "floor-plan", "value": "Ground Floor Plan A", "refId": "floor_001", "parentObjectValueId": "tower_001"}, {"metadataKey": "floor-total-area", "value": "400.0", "refId": "floor_001", "parentObjectValueId": "tower_001"}, {"metadataKey": "floor-unit-count", "value": "5", "refId": "floor_001", "parentObjectValueId": "tower_001"}, {"metadataKey": "floor-height", "value": "3.2", "refId": "floor_001", "parentObjectValueId": "tower_001"}, {"metadataKey": "floor-construction-status", "value": "Completed", "refId": "floor_001", "parentObjectValueId": "tower_001"}, {"metadataKey": "floor-special-features", "value": "Garden facing, Premium flooring", "refId": "floor_001", "parentObjectValueId": "tower_001"}]}, {"name": "Unit", "description": "Individual unit within a floor - matches projectplan.json units structure", "isActive": true, "metadata": [{"metadataId": "unit-number", "isUnique": true, "isActive": true}, {"metadataId": "unit-type", "isUnique": false, "isActive": true}, {"metadataId": "unit-category", "isUnique": false, "isActive": true}, {"metadataId": "unit-bedrooms", "isUnique": false, "isActive": true}, {"metadataId": "unit-bathrooms", "isUnique": false, "isActive": true}, {"metadataId": "unit-carpet-area", "isUnique": false, "isActive": true}, {"metadataId": "unit-built-up-area", "isUnique": false, "isActive": true}, {"metadataId": "unit-facing-direction", "isUnique": false, "isActive": true}, {"metadataId": "unit-view", "isUnique": false, "isActive": true}, {"metadataId": "unit-base-price", "isUnique": false, "isActive": true}, {"metadataId": "unit-total-price", "isUnique": false, "isActive": true}, {"metadataId": "unit-inventory-status", "isUnique": false, "isActive": true}, {"metadataId": "unit-construction-status", "isUnique": false, "isActive": true}, {"metadataId": "unit-furnishing-status", "isUnique": false, "isActive": true}, {"metadataId": "unit-flooring-type", "isUnique": false, "isActive": true}, {"metadataId": "unit-parking-allotment", "isUnique": false, "isActive": true}], "values": [{"metadataKey": "unit-number", "value": "A1-G-001", "refId": "unit_001", "parentObjectValueId": "floor_001"}, {"metadataKey": "unit-type", "value": "Apartment", "refId": "unit_001", "parentObjectValueId": "floor_001"}, {"metadataKey": "unit-category", "value": "Premium", "refId": "unit_001", "parentObjectValueId": "floor_001"}, {"metadataKey": "unit-bedrooms", "value": "3", "refId": "unit_001", "parentObjectValueId": "floor_001"}, {"metadataKey": "unit-bathrooms", "value": "3", "refId": "unit_001", "parentObjectValueId": "floor_001"}, {"metadataKey": "unit-carpet-area", "value": "1200.0", "refId": "unit_001", "parentObjectValueId": "floor_001"}, {"metadataKey": "unit-built-up-area", "value": "1400.0", "refId": "unit_001", "parentObjectValueId": "floor_001"}, {"metadataKey": "unit-facing-direction", "value": "North", "refId": "unit_001", "parentObjectValueId": "floor_001"}, {"metadataKey": "unit-view", "value": "Garden View", "refId": "unit_001", "parentObjectValueId": "floor_001"}, {"metadataKey": "unit-base-price", "value": "8000000", "refId": "unit_001", "parentObjectValueId": "floor_001"}, {"metadataKey": "unit-total-price", "value": "8600000", "refId": "unit_001", "parentObjectValueId": "floor_001"}, {"metadataKey": "unit-inventory-status", "value": "Available", "refId": "unit_001", "parentObjectValueId": "floor_001"}, {"metadataKey": "unit-construction-status", "value": "Ready to Move", "refId": "unit_001", "parentObjectValueId": "floor_001"}, {"metadataKey": "unit-furnishing-status", "value": "Semi-Furnished", "refId": "unit_001", "parentObjectValueId": "floor_001"}, {"metadataKey": "unit-flooring-type", "value": "Vitrified Tiles", "refId": "unit_001", "parentObjectValueId": "floor_001"}, {"metadataKey": "unit-parking-allotment", "value": "P-001", "refId": "unit_001", "parentObjectValueId": "floor_001"}]}]}], "metadata": [{"metadataId": "product-license-key", "isUnique": true, "isActive": true}, {"metadataId": "product-deployment-environment", "isUnique": false, "isActive": true}, {"metadataId": "product-version-info", "isUnique": false, "isActive": true}], "values": [{"metadataKey": "product-license-key", "value": "REIMS-2024-ENTERPRISE-001", "refId": "product-001"}, {"metadataKey": "product-deployment-environment", "value": "Production", "refId": "product-001"}, {"metadataKey": "product-version-info", "value": "v2.0.0-stable", "refId": "product-001"}]}]}