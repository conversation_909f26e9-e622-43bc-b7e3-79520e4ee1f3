using Application.ObjectValues.DTOs;
using Application.ObjectValues.Specifications;
using Abstraction.Database.Repositories;
using Abstraction.Common;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.ObjectValues.Commands;

/// <summary>
/// Create ObjectValue command handler
/// </summary>
public class CreateObjectValueCommandHandler : IRequestHandler<CreateObjectValueCommand, Result<ObjectValueDto>>
{
    private readonly IRepository<Domain.Entities.ObjectValue> _repository;
    private readonly IRepository<Domain.Entities.ObjectMetadata> _objectMetadataRepository;
    private readonly ICurrentUser _currentUser;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateObjectValueCommandHandler(
        IRepository<Domain.Entities.ObjectValue> repository,
        IRepository<Domain.Entities.ObjectMetadata> objectMetadataRepository,
        ICurrentUser currentUser)
    {
        _repository = repository;
        _objectMetadataRepository = objectMetadataRepository;
        _currentUser = currentUser;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<ObjectValueDto>> Handle(CreateObjectValueCommand request, CancellationToken cancellationToken)
    {
        // Validate ObjectMetadata exists
        var objectMetadata = await _objectMetadataRepository.GetByIdAsync(request.ObjectMetadataId, cancellationToken);
        if (objectMetadata == null)
        {
            return Result<ObjectValueDto>.Failure($"ObjectMetadata with ID '{request.ObjectMetadataId}' not found.");
        }

        // Create ObjectValue
        var objectValue = new Domain.Entities.ObjectValue
        {
            ObjectMetadataId = request.ObjectMetadataId,
            RefId = request.RefId,
            ParentObjectValueId = request.ParentObjectValueId,
            Value = request.Value
        };

        await _repository.AddAsync(objectValue, cancellationToken);

        var dto = new ObjectValueDto
        {
            Id = objectValue.Id,
            ObjectMetadataId = objectValue.ObjectMetadataId,
            RefId = objectValue.RefId,
            ParentObjectValueId = objectValue.ParentObjectValueId,
            Value = objectValue.Value,
            ChildValuesCount = 0,
            CreatedAt = objectValue.CreatedAt,
            CreatedBy = objectValue.CreatedBy ?? Guid.Empty,
            ModifiedAt = objectValue.ModifiedAt,
            ModifiedBy = objectValue.ModifiedBy
        };

        return Result<ObjectValueDto>.Success(dto);
    }
}
