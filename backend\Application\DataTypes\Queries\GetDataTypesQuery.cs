using Application.DataTypes.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.DataTypes.Queries;

/// <summary>
/// Get DataTypes query
/// </summary>
public class GetDataTypesQuery : IRequest<PaginatedResult<DataTypeDto>>
{
    /// <summary>
    /// Page number
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Search term
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Category filter
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Active status filter
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Order by field
    /// </summary>
    public string? OrderBy { get; set; }
}
