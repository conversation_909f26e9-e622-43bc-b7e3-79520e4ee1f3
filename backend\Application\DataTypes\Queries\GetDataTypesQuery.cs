using Application.DataTypes.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.DataTypes.Queries;

/// <summary>
/// Query to get paginated list of data types with filtering
/// </summary>
public class GetDataTypesQuery : IRequest<PaginatedResult<DataTypeDto>>
{
    /// <summary>
    /// Page number (default: 1)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size (default: 10)
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Search term for filtering by name or display name
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Filter by category
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Filter by active status
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Order by field (name, displayName, category, createdAt, etc.)
    /// </summary>
    public string? OrderBy { get; set; }
}
