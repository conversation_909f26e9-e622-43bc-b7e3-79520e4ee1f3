using Application.RoleMetadata.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.RoleMetadata.Commands;

/// <summary>
/// Create RoleMetadata command
/// </summary>
public class CreateRoleMetadataCommand : IRequest<Result<RoleMetadataDto>>
{
    /// <summary>
    /// Role ID
    /// </summary>
    public Guid RoleId { get; set; }

    /// <summary>
    /// Metadata ID
    /// </summary>
    public Guid MetadataId { get; set; }

    /// <summary>
    /// Whether this metadata is unique for the role
    /// </summary>
    public bool IsUnique { get; set; } = false;

    /// <summary>
    /// Whether this metadata link is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
