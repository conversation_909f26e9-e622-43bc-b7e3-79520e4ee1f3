using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for Feature entity
/// </summary>
public class FeatureConfig : IEntityTypeConfiguration<Feature>
{
    public void Configure(EntityTypeBuilder<Feature> builder)
    {
        builder.ToTable("Features", "Genp");

        // Properties
        builder.Property(e => e.ProductId)
            .IsRequired();

        builder.Property(e => e.Name)
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(e => e.Description)
            .HasColumnType("TEXT");

        builder.Property(e => e.IsDefault)
            .HasDefaultValue(false);

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        // Indexes
        builder.HasIndex(e => e.ProductId)
            .HasDatabaseName("IX_Features_ProductId");

        builder.HasIndex(e => e.IsDefault)
            .HasDatabaseName("IX_Features_IsDefault");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_Features_IsActive")
            .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

        // Unique constraint
        builder.HasIndex(e => new { e.ProductId, e.Name })
            .IsUnique()
            .HasDatabaseName("IX_Features_ProductId_Name");

        // Multi-tenancy
        builder.IsMultiTenant();

        // Relationships
        builder.HasOne(e => e.Product)
            .WithMany(e => e.Features)
            .HasForeignKey(e => e.ProductId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.Objects)
            .WithOne(e => e.Feature)
            .HasForeignKey(e => e.FeatureId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.FeatureMetadata)
            .WithOne(e => e.Feature)
            .HasForeignKey(e => e.FeatureId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
