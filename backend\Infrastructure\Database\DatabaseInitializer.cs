using Finbuckle.MultiTenant;
using Domain.MultiTenancy;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Infrastructure.MultiTenancy;
using Newtonsoft.Json;
using Shared.MultiTenancy;

namespace Infrastructure.Database;

/// <summary>
/// Database initializer interface
/// </summary>
public interface IDatabaseInitializer
{
    /// <summary>
    /// Initialize the database
    /// </summary>
    Task InitializeDatabasesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Initialize the application database for a tenant
    /// </summary>
    Task InitializeApplicationDbForTenantAsync(AppTenantInfo tenant, CancellationToken cancellationToken = default);
}

/// <summary>
/// Database initializer implementation
/// </summary>
public class DatabaseInitializer : IDatabaseInitializer
{
    private readonly TenantDbContext _tenantDbContext;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<DatabaseInitializer> _logger;

    public DatabaseInitializer(TenantDbContext tenantDbContext, IServiceProvider serviceProvider, ILogger<DatabaseInitializer> logger)
    {
        _tenantDbContext = tenantDbContext;
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public async Task InitializeDatabasesAsync(CancellationToken cancellationToken)
    {
        try
        {
            await InitializeTenantDbAsync(cancellationToken);

            foreach (var tenant in await _tenantDbContext.TenantInfo.ToListAsync(cancellationToken))
            {
                await InitializeApplicationDbForTenantAsync(tenant, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogInformation("DatabaseInitializer -> InitializeDatabasesAsync() : Error  {error}", JsonConvert.SerializeObject(ex, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
            // ignore
        }

    }

    public async Task InitializeApplicationDbForTenantAsync(AppTenantInfo tenant, CancellationToken cancellationToken)
    {
        // First create a new scope
        using var scope = _serviceProvider.CreateScope();

        // Use the scoped service provider
        var scopedProvider = scope.ServiceProvider;

        // Set the current tenant so the right connection string is used
        scopedProvider.GetRequiredService<IMultiTenantContextAccessor<AppTenantInfo>>()
            .MultiTenantContext = new MultiTenantContext<AppTenantInfo>()
            {
                TenantInfo = tenant
            };

        // Run the initialization within the new scope
        await scopedProvider.GetRequiredService<ApplicationDbInitializer>()
            .InitializeAsync(cancellationToken);
    }


    private async Task InitializeTenantDbAsync(CancellationToken cancellationToken)
    {
        var pendingMigrations = _tenantDbContext.Database.GetPendingMigrations();
        if (pendingMigrations.Any())
        {
            try
            {
                _logger.LogInformation("Applying Root Migrations.");
                await _tenantDbContext.Database.MigrateAsync(cancellationToken);
            }
            catch (Exception ex)
            {
            }
        }
        await SeedRootTenantAsync(cancellationToken);
    }

    private async Task SeedRootTenantAsync(CancellationToken cancellationToken)
    {
        if (await _tenantDbContext.TenantInfo.FindAsync(new object?[] { MultitenancyConstants.Root.Id }, cancellationToken: cancellationToken) is null)
        {
            var rootTenant = new AppTenantInfo(
                MultitenancyConstants.Root.Id,
                MultitenancyConstants.Root.Name,
                string.Empty,
                MultitenancyConstants.Root.EmailAddress);

            _tenantDbContext.TenantInfo.Add(rootTenant);
            await _tenantDbContext.SaveChangesAsync(cancellationToken);
        }
    }
}
