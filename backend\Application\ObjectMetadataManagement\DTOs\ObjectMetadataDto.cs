namespace Application.ObjectMetadataManagement.DTOs;

/// <summary>
/// ObjectMetadata DTO
/// </summary>
public class ObjectMetadataDto
{
    /// <summary>
    /// ObjectMetadata ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Object ID
    /// </summary>
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Object name
    /// </summary>
    public string? ObjectName { get; set; }

    /// <summary>
    /// Metadata ID
    /// </summary>
    public Guid MetadataId { get; set; }

    /// <summary>
    /// Metadata key
    /// </summary>
    public string? MetadataKey { get; set; }

    /// <summary>
    /// Metadata display label
    /// </summary>
    public string? MetadataDisplayLabel { get; set; }

    /// <summary>
    /// Whether this metadata is unique for the object
    /// </summary>
    public bool IsUnique { get; set; }

    /// <summary>
    /// Whether this metadata link is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Number of values for this metadata
    /// </summary>
    public int ValuesCount { get; set; }

    /// <summary>
    /// Should be visible in list view
    /// </summary>
    public bool ShouldVisibleInList { get; set; }

    /// <summary>
    /// Should be visible in edit view
    /// </summary>
    public bool ShouldVisibleInEdit { get; set; }

    /// <summary>
    /// Should be visible in create view
    /// </summary>
    public bool ShouldVisibleInCreate { get; set; }

    /// <summary>
    /// Should be visible in view
    /// </summary>
    public bool ShouldVisibleInView { get; set; }

    /// <summary>
    /// Is calculated field
    /// </summary>
    public bool IsCalculate { get; set; }

    /// <summary>
    /// Data type ID
    /// </summary>
    public Guid DataTypeId { get; set; }

    /// <summary>
    /// Data type name
    /// </summary>
    public string? DataTypeName { get; set; }

    /// <summary>
    /// Created at
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by
    /// </summary>
    public Guid CreatedBy { get; set; }

    /// <summary>
    /// Modified at
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Modified by
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}
