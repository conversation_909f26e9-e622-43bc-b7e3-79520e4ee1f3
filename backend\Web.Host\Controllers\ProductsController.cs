using Application.Products.Commands;
using Application.Products.DTOs;
using Application.Products.Queries;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;

namespace Web.Host.Controllers;

/// <summary>
/// Products controller
/// </summary>
[Route("api/[controller]")]
public class ProductsController : BaseApiController
{
    /// <summary>
    /// Get all products with pagination
    /// </summary>
    [HttpGet]
    [TenantIdHeader]
    public async Task<ActionResult<PaginatedResult<ProductDto>>> GetProducts([FromQuery] GetProductsQuery query)
    {
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get product by ID
    /// </summary>
    [HttpGet("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ProductDto>>> GetProductById(Guid id)
    {
        return Ok(await Mediator.Send(new GetProductByIdQuery(id)));
    }

    /// <summary>
    /// Create a new product
    /// </summary>
    [HttpPost]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ProductDto>>> CreateProduct(CreateProductCommand command)
    {
        return Ok(await Mediator.Send(command));
    }

    /// <summary>
    /// Update an existing product
    /// </summary>
    [HttpPut("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ProductDto>>> UpdateProduct(Guid id, UpdateProductCommand command)
    {
        command.Id = id;
        return Ok(await Mediator.Send(command));
    }

    /// <summary>
    /// Create a new product with subscription in a single transaction
    /// </summary>
    [HttpPost("with-subscription")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ProductWithSubscriptionDto>>> CreateProductWithSubscription(CreateProductWithSubscriptionCommand command)
    {
        return Ok(await Mediator.Send(command));
    }
}
