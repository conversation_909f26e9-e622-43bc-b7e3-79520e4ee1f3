using Application.RoleMetadata.DTOs;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.RoleMetadata.Commands;

/// <summary>
/// Create RoleMetadata command handler
/// </summary>
public class CreateRoleMetadataCommandHandler : IRequestHandler<CreateRoleMetadataCommand, Result<RoleMetadataDto>>
{
    private readonly IRepository<Domain.Entities.RoleMetadata> _repository;
    private readonly IRepository<Role> _roleRepository;
    private readonly IRepository<Domain.Entities.Metadata> _metadataRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateRoleMetadataCommandHandler(
        IRepository<Domain.Entities.RoleMetadata> repository,
        IRepository<Role> roleRepository,
        IRepository<Domain.Entities.Metadata> metadataRepository)
    {
        _repository = repository;
        _roleRepository = roleRepository;
        _metadataRepository = metadataRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<RoleMetadataDto>> Handle(CreateRoleMetadataCommand request, CancellationToken cancellationToken)
    {
        // Validate Role exists
        var role = await _roleRepository.GetByIdAsync(request.RoleId, cancellationToken);
        if (role == null)
        {
            return Result<RoleMetadataDto>.Failure($"Role with ID '{request.RoleId}' not found.");
        }

        // Validate Metadata exists
        var metadata = await _metadataRepository.GetByIdAsync(request.MetadataId, cancellationToken);
        if (metadata == null)
        {
            return Result<RoleMetadataDto>.Failure($"Metadata with ID '{request.MetadataId}' not found.");
        }

        // Create RoleMetadata
        var roleMetadata = new Domain.Entities.RoleMetadata
        {
            RoleId = request.RoleId,
            MetadataId = request.MetadataId,
            IsUnique = request.IsUnique,
            IsActive = request.IsActive
        };

        await _repository.AddAsync(roleMetadata, cancellationToken);

        var dto = new RoleMetadataDto
        {
            Id = roleMetadata.Id,
            RoleId = roleMetadata.RoleId,
            RoleName = role.Name,
            MetadataId = roleMetadata.MetadataId,
            MetadataKey = metadata.MetadataKey,
            IsUnique = roleMetadata.IsUnique,
            IsActive = roleMetadata.IsActive,
            CreatedAt = roleMetadata.CreatedAt,
            CreatedBy = roleMetadata.CreatedBy ?? Guid.Empty,
            ModifiedAt = roleMetadata.ModifiedAt,
            ModifiedBy = roleMetadata.ModifiedBy
        };

        return Result<RoleMetadataDto>.Success(dto);
    }
}
