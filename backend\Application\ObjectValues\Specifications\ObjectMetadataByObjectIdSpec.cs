using Ardalis.Specification;
using Domain.Entities;

namespace Application.ObjectValues.Specifications;

/// <summary>
/// Specification to get ObjectMetadata by ObjectId
/// </summary>
public class ObjectMetadataByObjectIdSpec : Specification<ObjectMetadata>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ObjectMetadataByObjectIdSpec(Guid objectId, bool onlyActive = true)
    {
        Query.Where(om => om.ObjectId == objectId);

        // Include related data
        Query.Include(om => om.Metadata)
             .ThenInclude(m => m.DataType);

        Query.Include(om => om.Object);

        // Active filters
        if (onlyActive)
        {
            Query.Where(om => om.IsActive && om.Object.IsActive);
        }

        // Order by field order and metadata key
        Query.OrderBy(om => om.Metadata.FieldOrder ?? int.MaxValue)
             .ThenBy(om => om.Metadata.MetadataKey);
    }
}
