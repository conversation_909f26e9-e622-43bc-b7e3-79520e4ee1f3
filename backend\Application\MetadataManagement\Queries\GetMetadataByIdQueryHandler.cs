using Application.MetadataManagement.DTOs;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.MetadataManagement.Queries;

/// <summary>
/// Get Metadata by ID query handler
/// </summary>
public class GetMetadataByIdQueryHandler : IRequestHandler<GetMetadataByIdQuery, Result<MetadataDto>>
{
    private readonly IRepository<Domain.Entities.Metadata> _repository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetMetadataByIdQueryHandler(IRepository<Domain.Entities.Metadata> repository)
    {
        _repository = repository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<MetadataDto>> Handle(GetMetadataByIdQuery request, CancellationToken cancellationToken)
    {
        var metadata = await _repository.GetByIdAsync(request.Id, cancellationToken);
        if (metadata == null)
        {
            return Result<MetadataDto>.Failure($"Metadata with ID '{request.Id}' not found.");
        }

        var dto = new MetadataDto
        {
            Id = metadata.Id,
            MetadataKey = metadata.MetadataKey,
            DataTypeId = metadata.DataTypeId,
            DataTypeName = metadata.DataType?.Name,
            CustomValidationPattern = metadata.CustomValidationPattern,
            CustomMinLength = metadata.CustomMinLength,
            CustomMaxLength = metadata.CustomMaxLength,
            CustomMinValue = metadata.CustomMinValue,
            CustomMaxValue = metadata.CustomMaxValue,
            CustomIsRequired = metadata.CustomIsRequired,
            CustomPlaceholder = metadata.CustomPlaceholder,
            CustomOptions = metadata.CustomOptions,
            CustomMaxSelections = metadata.CustomMaxSelections,
            CustomAllowedFileTypes = metadata.CustomAllowedFileTypes,
            CustomMaxFileSize = metadata.CustomMaxFileSize,
            CustomErrorMessage = metadata.CustomErrorMessage,
            DisplayLabel = metadata.DisplayLabel,
            HelpText = metadata.HelpText,
            FieldOrder = metadata.FieldOrder,
            IsVisible = metadata.IsVisible,
            IsReadonly = metadata.IsReadonly,
            CreatedAt = metadata.CreatedAt,
            CreatedBy = metadata.CreatedBy ?? Guid.Empty,
            ModifiedAt = metadata.ModifiedAt,
            ModifiedBy = metadata.ModifiedBy
        };

        return Result<MetadataDto>.Success(dto);
    }
}
