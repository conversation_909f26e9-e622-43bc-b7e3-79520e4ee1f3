using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Actual tenant information data (e.g., "Prestige Jindal City")
/// </summary>
public class TenantInfoValue : AuditableEntity, IAggregateRoot
{

    /// <summary>
    /// Tenant info metadata ID
    /// </summary>
    public Guid TenantInfoMetadataId { get; set; }

    /// <summary>
    /// Reference ID for grouping related values
    /// </summary>
    public Guid? RefId { get; set; }

    /// <summary>
    /// The actual value as text
    /// </summary>
    public string? Value { get; set; }

    // Navigation properties
    /// <summary>
    /// Tenant info metadata link
    /// </summary>
    public virtual TenantInfoMetadata TenantInfoMetadata { get; set; } = null!;
}
