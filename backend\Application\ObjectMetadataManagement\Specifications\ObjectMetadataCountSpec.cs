using Ardalis.Specification;
using Domain.Entities;

namespace Application.ObjectMetadataManagement.Specifications;

/// <summary>
/// Specification to count ObjectMetadata with filters
/// </summary>
public class ObjectMetadataCountSpec : Specification<ObjectMetadata>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ObjectMetadataCountSpec(
        Guid objectId,
        string? searchTerm = null,
        Guid? metadataId = null,
        bool? isActive = null)
    {
        Query.Where(om => om.ObjectId == objectId && !om.IsDeleted);

        if (isActive.HasValue)
        {
            Query.Where(om => om.IsActive == isActive.Value);
        }

        // Include related data for filtering
        Query.Include(om => om.Metadata);

        // Apply search filter if provided
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            Query.Where(om => om.Metadata.MetadataKey.Contains(searchTerm) ||
                             (om.Metadata.DisplayLabel != null && om.Metadata.DisplayLabel.Contains(searchTerm)));
        }

        // Apply metadata ID filter if provided
        if (metadataId.HasValue)
        {
            Query.Where(om => om.MetadataId == metadataId.Value);
        }
    }
}
