using Ardalis.Specification;
using Domain.Entities;

namespace Application.Features.Specifications;

/// <summary>
/// Specification for getting a feature by ID
/// </summary>
public class FeatureByIdSpec : Specification<Feature>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public FeatureByIdSpec(Guid id)
    {
        Query.Where(f => f.Id == id);
    }
}

/// <summary>
/// Specification for getting a feature by blueprint feature ID and product ID
/// </summary>
public class FeatureByBlueprintIdAndProductIdSpec : Specification<Feature>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public FeatureByBlueprintIdAndProductIdSpec(Guid blueprintFeatureId, Guid productId)
    {
        Query.Where(f => f.Name == blueprintFeatureId.ToString() && f.ProductId == productId);
    }
}

/// <summary>
/// Specification for getting features with filters
/// </summary>
public class FeaturesWithFiltersSpec : Specification<Feature>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public FeaturesWithFiltersSpec(string? searchTerm = null, bool? isActive = null, Guid? productId = null, int skip = 0, int take = 10)
    {
        Query.Where(f => true); // Base query

        if (!string.IsNullOrEmpty(searchTerm))
        {
            Query.Where(f => f.Name.Contains(searchTerm) ||
                           (f.Description != null && f.Description.Contains(searchTerm)));
        }

        if (isActive.HasValue)
        {
            Query.Where(f => f.IsActive == isActive.Value);
        }

        if (productId.HasValue)
        {
            Query.Where(f => f.ProductId == productId.Value);
        }

        Query.OrderBy(f => f.Name)
             .Skip(skip)
             .Take(take);
    }
}

/// <summary>
/// Specification for counting features with filters
/// </summary>
public class FeaturesCountSpec : Specification<Feature>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public FeaturesCountSpec(string? searchTerm = null, bool? isActive = null, Guid? productId = null)
    {
        Query.Where(f => true); // Base query

        if (!string.IsNullOrEmpty(searchTerm))
        {
            Query.Where(f => f.Name.Contains(searchTerm) ||
                           (f.Description != null && f.Description.Contains(searchTerm)));
        }

        if (isActive.HasValue)
        {
            Query.Where(f => f.IsActive == isActive.Value);
        }

        if (productId.HasValue)
        {
            Query.Where(f => f.ProductId == productId.Value);
        }
    }
}
