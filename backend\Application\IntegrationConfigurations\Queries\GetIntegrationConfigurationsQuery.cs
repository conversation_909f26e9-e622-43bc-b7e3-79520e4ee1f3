using Application.IntegrationConfigurations.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.IntegrationConfigurations.Queries;

/// <summary>
/// Get integration configurations query
/// </summary>
public class GetIntegrationConfigurationsQuery : IRequest<PaginatedResult<ViewIntegrationConfigurationDto>>
{
    /// <summary>
    /// Page number
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Search term
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Filter by active status
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Filter by integration ID
    /// </summary>
    public Guid? IntegrationId { get; set; }

    /// <summary>
    /// Filter by integration API ID
    /// </summary>
    public Guid? IntegrationApiId { get; set; }

    /// <summary>
    /// Filter by object ID
    /// </summary>
    public Guid? ObjectId { get; set; }

    /// <summary>
    /// Filter by direction
    /// </summary>
    public string? Direction { get; set; }
}
