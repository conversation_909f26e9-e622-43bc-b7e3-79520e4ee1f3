using Application.Objects.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Objects.Queries;

/// <summary>
/// Get Objects with metadata query
/// </summary>
public class GetObjectsWithMetadataQuery : IRequest<PaginatedResult<ObjectWithMetadataDto>>
{
    /// <summary>
    /// Page number
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Search term
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Feature ID filter
    /// </summary>
    public Guid? FeatureId { get; set; }

    /// <summary>
    /// Is active filter
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Order by
    /// </summary>
    public string? OrderBy { get; set; }
}
