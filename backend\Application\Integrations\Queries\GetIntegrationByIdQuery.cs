using Application.Integrations.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Integrations.Queries;

/// <summary>
/// Get integration by ID query
/// </summary>
public class GetIntegrationByIdQuery : IRequest<Result<IntegrationDto>>
{
    /// <summary>
    /// Integration ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public GetIntegrationByIdQuery(Guid id)
    {
        Id = id;
    }
}
