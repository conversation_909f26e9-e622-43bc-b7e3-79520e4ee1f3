namespace Application.DataTypes.DTOs;

/// <summary>
/// Data Transfer Object for DataType
/// </summary>
public class DataTypeDto
{
    /// <summary>
    /// Unique identifier for the data type
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Name of the data type (unique identifier)
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Display name for the data type
    /// </summary>
    public string? DisplayName { get; set; }

    /// <summary>
    /// Category of the data type
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// UI component type for rendering
    /// </summary>
    public string? UiComponent { get; set; }

    /// <summary>
    /// Validation pattern (regex)
    /// </summary>
    public string? ValidationPattern { get; set; }

    /// <summary>
    /// Minimum length for string values
    /// </summary>
    public int? MinLength { get; set; }

    /// <summary>
    /// Maximum length for string values
    /// </summary>
    public int? MaxLength { get; set; }

    /// <summary>
    /// Minimum value for numeric types
    /// </summary>
    public decimal? MinValue { get; set; }

    /// <summary>
    /// Maximum value for numeric types
    /// </summary>
    public decimal? MaxValue { get; set; }

    /// <summary>
    /// Number of decimal places for numeric types
    /// </summary>
    public int? DecimalPlaces { get; set; }

    /// <summary>
    /// Default value for the data type
    /// </summary>
    public string? DefaultValue { get; set; }

    /// <summary>
    /// Whether this data type is required
    /// </summary>
    public bool IsRequired { get; set; }

    /// <summary>
    /// Whether this data type is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Whether multiple values are allowed
    /// </summary>
    public bool AllowMultiple { get; set; }

    /// <summary>
    /// Sort order for display
    /// </summary>
    public int SortOrder { get; set; }

    /// <summary>
    /// Description of the data type
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// When the data type was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// When the data type was last modified
    /// </summary>
    public DateTime ModifiedAt { get; set; }

    /// <summary>
    /// Who created the data type
    /// </summary>
    public Guid? CreatedBy { get; set; }

    /// <summary>
    /// Who last modified the data type
    /// </summary>
    public Guid? ModifiedBy { get; set; }

    /// <summary>
    /// Tenant ID for multi-tenancy
    /// </summary>
    public string? TenantId { get; set; }
}
