using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for SubscriptionValue entity
/// </summary>
public class SubscriptionValueConfig : IEntityTypeConfiguration<SubscriptionValue>
{
    public void Configure(EntityTypeBuilder<SubscriptionValue> builder)
    {
        builder.ToTable("SubscriptionValues", "Genp");

        // Primary key
        builder.HasKey(e => e.Id);

        // Properties
        builder.Property(e => e.SubscriptionMetadataId)
            .IsRequired();

        builder.Property(e => e.RefId);

        builder.Property(e => e.Value)
            .HasColumnType("TEXT");

        builder.Property(e => e.CreatedAt)
            .HasDefaultValueSql("NOW()")
            .IsRequired();

        builder.Property(e => e.CreatedBy);

        builder.Property(e => e.ModifiedAt)
            .HasDefaultValueSql("NOW()")
            .IsRequired();

        builder.Property(e => e.ModifiedBy);

        // Indexes
        builder.HasIndex(e => e.SubscriptionMetadataId)
            .HasDatabaseName("IX_SubscriptionValues_SubscriptionMetadataId");

        builder.HasIndex(e => e.RefId)
            .HasDatabaseName("IX_SubscriptionValues_RefId");

        builder.HasIndex(e => e.CreatedAt)
            .HasDatabaseName("IX_SubscriptionValues_CreatedAt");

        // Multi-tenancy
        builder.IsMultiTenant();

        // Relationships
        builder.HasOne(e => e.SubscriptionMetadata)
            .WithMany(e => e.SubscriptionValues)
            .HasForeignKey(e => e.SubscriptionMetadataId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
