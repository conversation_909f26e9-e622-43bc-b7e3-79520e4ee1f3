using Application.SyncHistories.Commands;
using Application.SyncHistories.DTOs;
// using Application.SyncHistories.Queries;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;

namespace Web.Host.Controllers;

/// <summary>
/// Sync Histories controller
/// </summary>
[Route("api/[controller]")]
public class SyncHistoriesController : BaseApiController
{
    // TODO: Implement CRUD operations for SyncHistory
    // Temporarily commented out until Query and Command classes are implemented

    /*
    /// <summary>
    /// Get all sync histories with pagination
    /// </summary>
    [HttpGet]
    [TenantIdHeader]
    public async Task<ActionResult<PaginatedResult<ViewSyncHistoryDto>>> GetSyncHistories([FromQuery] GetSyncHistoriesQuery query)
    {
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get sync history by ID
    /// </summary>
    [HttpGet("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ViewSyncHistoryDto>>> GetSyncHistoryById(Guid id)
    {
        return Ok(await Mediator.Send(new GetSyncHistoryByIdQuery(id)));
    }

    /// <summary>
    /// Create a new sync history
    /// </summary>
    [HttpPost]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ViewSyncHistoryDto>>> CreateSyncHistory(CreateSyncHistoryCommand command)
    {
        return Ok(await Mediator.Send(command));
    }

    /// <summary>
    /// Update an existing sync history
    /// </summary>
    [HttpPut("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ViewSyncHistoryDto>>> UpdateSyncHistory(Guid id, UpdateSyncHistoryCommand command)
    {
        command.Id = id;
        return Ok(await Mediator.Send(command));
    }

    /// <summary>
    /// Delete a sync history
    /// </summary>
    [HttpDelete("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<bool>>> DeleteSyncHistory(Guid id)
    {
        return Ok(await Mediator.Send(new DeleteSyncHistoryCommand(id)));
    }

    /// <summary>
    /// Create multiple sync histories in bulk
    /// </summary>
    [HttpPost("bulk")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<List<ViewSyncHistoryDto>>>> CreateSyncHistories(CreateSyncHistoriesCommand command)
    {
        return Ok(await Mediator.Send(command));
    }
    */
}
