using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for DataType entity
/// </summary>
public class DataTypeConfig : IEntityTypeConfiguration<DataType>
{
    public void Configure(EntityTypeBuilder<DataType> builder)
    {

        builder.ToTable("DataTypes", "Genp");

        // Properties
        builder.Property(e => e.Name)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.DisplayName)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.Category)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.UiComponent)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.ValidationPattern)
            .HasMaxLength(500);

        builder.Property(e => e.InputType)
            .HasMaxLength(50);

        builder.Property(e => e.InputMask)
            .HasMaxLength(100);

        builder.Property(e => e.Placeholder)
            .HasMaxLength(255);

        builder.Property(e => e.HtmlAttributes)
            .HasMaxLength(500);

        builder.Property(e => e.DefaultOptions)
            .HasColumnType("TEXT");

        builder.Property(e => e.AllowedFileTypes)
            .HasMaxLength(255);

        builder.Property(e => e.RequiredErrorMessage)
            .HasMaxLength(255);

        builder.Property(e => e.PatternErrorMessage)
            .HasMaxLength(255);

        builder.Property(e => e.MinLengthErrorMessage)
            .HasMaxLength(255);

        builder.Property(e => e.MaxLengthErrorMessage)
            .HasMaxLength(255);

        builder.Property(e => e.MinValueErrorMessage)
            .HasMaxLength(255);

        builder.Property(e => e.MaxValueErrorMessage)
            .HasMaxLength(255);

        builder.Property(e => e.FileTypeErrorMessage)
            .HasMaxLength(255);

        builder.Property(e => e.FileSizeErrorMessage)
            .HasMaxLength(255);

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        // Indexes
        builder.HasIndex(e => e.Name)
            .IsUnique()
            .HasDatabaseName("IX_DataTypes_Name");

        builder.HasIndex(e => e.Category)
            .HasDatabaseName("IX_DataTypes_Category");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_DataTypes_IsActive")
            .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

        // Relationships
        builder.HasMany(e => e.Metadata)
            .WithOne(e => e.DataType)
            .HasForeignKey(e => e.DataTypeId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
