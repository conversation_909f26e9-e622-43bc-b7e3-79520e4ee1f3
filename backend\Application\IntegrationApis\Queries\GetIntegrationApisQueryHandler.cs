using Application.IntegrationApis.DTOs;
using Application.IntegrationApis.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.IntegrationApis.Queries;

/// <summary>
/// Get integration APIs query handler
/// </summary>
public class GetIntegrationApisQueryHandler : IRequestHandler<GetIntegrationApisQuery, PaginatedResult<ViewIntegrationApiDto>>
{
    private readonly IReadRepository<IntegrationApi> _integrationApiRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetIntegrationApisQueryHandler(IReadRepository<IntegrationApi> integrationApiRepository)
    {
        _integrationApiRepository = integrationApiRepository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<PaginatedResult<ViewIntegrationApiDto>> Handle(GetIntegrationApisQuery request, CancellationToken cancellationToken)
    {
        try
        {
            // Calculate pagination
            var skip = (request.PageNumber - 1) * request.PageSize;

            // Create specifications for data and count
            var dataSpec = new IntegrationApisWithFiltersSpec(
                request.SearchTerm,
                request.IsActive,
                request.ProductId,
                skip,
                request.PageSize);

            var countSpec = new IntegrationApisCountSpec(
                request.SearchTerm,
                request.IsActive,
                request.ProductId);

            // Get data and count
            var integrationApis = await _integrationApiRepository.ListAsync(dataSpec, cancellationToken);
            var totalCount = await _integrationApiRepository.CountAsync(countSpec, cancellationToken);

            // Map to DTOs
            var integrationApiDtos = integrationApis.Select(ia =>
            {
                var dto = ia.Adapt<ViewIntegrationApiDto>();
                dto.ProductName = ia.Product?.Name ?? string.Empty;
                return dto;
            }).ToList();

            return new PaginatedResult<ViewIntegrationApiDto>(
                integrationApiDtos,
                request.PageNumber,
                request.PageSize,
                totalCount);
        }
        catch (Exception ex)
        {
            return PaginatedResult<ViewIntegrationApiDto>.Failure($"Failed to get integration APIs: {ex.Message}");
        }
    }
}

/// <summary>
/// Get integration API by ID query handler
/// </summary>
public class GetIntegrationApiByIdQueryHandler : IRequestHandler<GetIntegrationApiByIdQuery, Result<ViewIntegrationApiDto>>
{
    private readonly IReadRepository<IntegrationApi> _integrationApiRepository;
    private readonly IReadRepository<Product> _productRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetIntegrationApiByIdQueryHandler(
        IReadRepository<IntegrationApi> integrationApiRepository,
        IReadRepository<Product> productRepository)
    {
        _integrationApiRepository = integrationApiRepository;
        _productRepository = productRepository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<ViewIntegrationApiDto>> Handle(GetIntegrationApiByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var integrationApi = await _integrationApiRepository.GetByIdAsync(request.Id, cancellationToken);

            if (integrationApi == null || integrationApi.IsDeleted)
            {
                return Result<ViewIntegrationApiDto>.Failure($"Integration API with ID {request.Id} not found.");
            }

            var product = await _productRepository.GetByIdAsync(integrationApi.ProductId, cancellationToken);
            var integrationApiDto = integrationApi.Adapt<ViewIntegrationApiDto>();
            integrationApiDto.ProductName = product?.Name ?? string.Empty;

            return Result<ViewIntegrationApiDto>.Success(integrationApiDto);
        }
        catch (Exception ex)
        {
            return Result<ViewIntegrationApiDto>.Failure($"Failed to get integration API: {ex.Message}");
        }
    }
}
