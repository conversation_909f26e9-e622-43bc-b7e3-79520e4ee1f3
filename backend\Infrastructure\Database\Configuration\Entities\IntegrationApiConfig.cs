using Domain.Entities;
using Infrastructure.Database.Configuration;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Finbuckle.MultiTenant;
using Finbuckle.MultiTenant.EntityFrameworkCore;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for IntegrationApi entity
/// </summary>
public class IntegrationApiConfig : IEntityTypeConfiguration<IntegrationApi>
{
    public void Configure(EntityTypeBuilder<IntegrationApi> builder)
    {

        builder.ToTable("IntegrationApi", "Genp");

        builder.IsMultiTenant();

        // Properties
        builder.Property(e => e.ProductId)
            .IsRequired();

        builder.Property(e => e.Name)
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(e => e.EndpointUrl)
            .HasMaxLength(2000)
            .IsRequired();

        builder.Property(e => e.Schema)
            .HasColumnType("TEXT");

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        // Indexes
        builder.HasIndex(e => e.ProductId)
            .HasDatabaseName("IX_IntegrationApi_ProductId");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_IntegrationApi_IsActive")
            .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

        // Unique constraint
        builder.HasIndex(e => new { e.ProductId, e.Name })
            .IsUnique()
            .HasDatabaseName("IX_IntegrationApi_ProductId_Name");

        // Relationships
        builder.HasOne(e => e.Product)
            .WithMany(e => e.IntegrationApis)
            .HasForeignKey(e => e.ProductId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.IntegrationConfigurations)
            .WithOne(e => e.IntegrationApi)
            .HasForeignKey(e => e.IntegrationApiId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
