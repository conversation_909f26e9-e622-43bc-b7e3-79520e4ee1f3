using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.IntegrationConfigurations.Commands;

/// <summary>
/// Delete integration configuration command handler
/// </summary>
public class DeleteIntegrationConfigurationCommandHandler : I<PERSON>equestHandler<DeleteIntegrationConfigurationCommand, Result<bool>>
{
    private readonly IRepository<IntegrationConfiguration> _configurationRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteIntegrationConfigurationCommandHandler(IRepository<IntegrationConfiguration> configurationRepository)
    {
        _configurationRepository = configurationRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<bool>> Handle(DeleteIntegrationConfigurationCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Get existing configuration
            var configuration = await _configurationRepository.GetByIdAsync(request.Id, cancellationToken);
            if (configuration == null)
            {
                return Result<bool>.Failure("Integration configuration not found.");
            }

            // Soft delete the configuration
            await _configurationRepository.DeleteAsync(configuration, cancellationToken);

            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            return Result<bool>.Failure($"Failed to delete integration configuration: {ex.Message}");
        }
    }
}
