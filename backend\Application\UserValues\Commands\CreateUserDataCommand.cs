using Application.UserValues.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.UserValues.Commands;

/// <summary>
/// Command to create comprehensive user data with metadata and values
/// </summary>
public class CreateUserDataCommand : IRequest<Result<UserDataResponseDto>>
{
    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// User name
    /// </summary>
    public string? UserName { get; set; }

    /// <summary>
    /// First name
    /// </summary>
    public string? FirstName { get; set; }

    /// <summary>
    /// Last name
    /// </summary>
    public string? LastName { get; set; }

    /// <summary>
    /// Is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Email confirmed
    /// </summary>
    public bool EmailConfirmed { get; set; } = false;

    /// <summary>
    /// Image URL
    /// </summary>
    public string? ImageUrl { get; set; }

    /// <summary>
    /// Alternative phone number
    /// </summary>
    public string? AltPhoneNumber { get; set; }

    /// <summary>
    /// Address
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// Email
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Alternative email
    /// </summary>
    public string? AltEmail { get; set; }

    /// <summary>
    /// Blood group
    /// </summary>
    public int? BloodGroup { get; set; }

    /// <summary>
    /// Gender
    /// </summary>
    public int? Gender { get; set; }

    /// <summary>
    /// Permanent address
    /// </summary>
    public string? PermanentAddress { get; set; }

    /// <summary>
    /// Phone number
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Profile completion percentage
    /// </summary>
    public int? ProfileCompletion { get; set; }

    /// <summary>
    /// Employee number
    /// </summary>
    public string? EmpNo { get; set; }

    /// <summary>
    /// Office name
    /// </summary>
    public string? OfficeName { get; set; }

    /// <summary>
    /// Office address
    /// </summary>
    public string? OfficeAddress { get; set; }

    /// <summary>
    /// Reports to user ID
    /// </summary>
    public Guid? ReportsTo { get; set; }

    /// <summary>
    /// General manager user ID
    /// </summary>
    public Guid? GeneralManager { get; set; }

    /// <summary>
    /// Department information
    /// </summary>
    public DepartmentDto? Department { get; set; }

    /// <summary>
    /// Designation information
    /// </summary>
    public DesignationDto? Designation { get; set; }

    /// <summary>
    /// Description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Documents (JSON)
    /// </summary>
    public string? Documents { get; set; }

    /// <summary>
    /// Lead count
    /// </summary>
    public int? LeadCount { get; set; }

    /// <summary>
    /// User roles
    /// </summary>
    public List<UserDataRoleDto>? UserRoles { get; set; }

    /// <summary>
    /// Role permissions
    /// </summary>
    public List<string>? RolePermission { get; set; }

    /// <summary>
    /// Is automation enabled
    /// </summary>
    public bool? IsAutomationEnabled { get; set; }

    /// <summary>
    /// Time zone information
    /// </summary>
    public TimeZoneInfoDto? TimeZoneInfo { get; set; }

    /// <summary>
    /// Should show time zone
    /// </summary>
    public bool? ShouldShowTimeZone { get; set; }

    /// <summary>
    /// License number
    /// </summary>
    public string? LicenseNo { get; set; }
}

/// <summary>
/// Department DTO for user data
/// </summary>
public class DepartmentDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
}

/// <summary>
/// Designation DTO for user data
/// </summary>
public class DesignationDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
}

/// <summary>
/// User role DTO for user data
/// </summary>
public class UserDataRoleDto
{
    public Guid RoleId { get; set; }
    public string Name { get; set; } = string.Empty;
}

/// <summary>
/// Time zone info DTO for user data
/// </summary>
public class TimeZoneInfoDto
{
    public string? TimeZoneId { get; set; }
    public string? TimeZoneDisplay { get; set; }
    public string? TimeZoneName { get; set; }
    public string? BaseUTcOffset { get; set; }
}
