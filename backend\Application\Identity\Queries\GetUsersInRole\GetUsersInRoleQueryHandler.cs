using Application.Identity.DTOs;
using Abstraction.Identity;
using Abstraction.Identity.Dtos;
using MediatR;
using Shared.Common.Response;
using Mapster;

namespace Application.Identity.Queries.GetUsersInRole;

/// <summary>
/// Handler for GetUsersInRoleQuery
/// </summary>
public class GetUsersInRoleQueryHandler : IRequestHandler<GetUsersInRoleQuery, ApiResponse<List<UserDto>>>
{
    private readonly IIdentityService _identityService;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetUsersInRoleQueryHandler(IIdentityService identityService)
    {
        _identityService = identityService;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<ApiResponse<List<UserDto>>> Handle(GetUsersInRoleQuery request, CancellationToken cancellationToken)
    {
        try
        {
            // Validate the request
            if (string.IsNullOrWhiteSpace(request.RoleName))
            {
                return new ApiResponse<List<UserDto>>(false, "Role name is required.", new List<UserDto>());
            }

            // Get users in role
            var userDetails = await _identityService.GetUsersInRoleAsync(request.RoleName);

            // Convert UserDetailsDto to UserDto
            var users = userDetails.Adapt<List<UserDto>>();

            return new ApiResponse<List<UserDto>>(true, "Users in role retrieved successfully.", users);
        }
        catch (Exception ex)
        {
            return new ApiResponse<List<UserDto>>(false, $"Error retrieving users in role: {ex.Message}", new List<UserDto>());
        }
    }
}
