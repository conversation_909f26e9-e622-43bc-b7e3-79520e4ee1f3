using Application.IntegrationConfigurations.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.IntegrationConfigurations.Commands;

/// <summary>
/// Create multiple integration configurations command
/// </summary>
public class CreateIntegrationConfigurationsCommand : IRequest<Result<List<ViewIntegrationConfigurationDto>>>
{
    /// <summary>
    /// List of integration configurations to create
    /// </summary>
    public List<CreateIntegrationConfigurationRequest> Configurations { get; set; } = new();
}

/// <summary>
/// Create integration configuration request for bulk operation
/// </summary>
public class CreateIntegrationConfigurationRequest
{
    /// <summary>
    /// Integration ID this configuration belongs to
    /// </summary>
    public Guid IntegrationId { get; set; }

    /// <summary>
    /// Integration API ID this configuration uses
    /// </summary>
    public Guid IntegrationApiId { get; set; }

    /// <summary>
    /// Object ID this configuration applies to
    /// </summary>
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Data flow direction (e.g., "In", "Out", "Both")
    /// </summary>
    public string? Direction { get; set; }

    /// <summary>
    /// Whether the configuration is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
