using Domain.Entities;
using Infrastructure.Database.Configuration;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Finbuckle.MultiTenant;
using Finbuckle.MultiTenant.EntityFrameworkCore;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for SyncHistory entity
/// </summary>
public class SyncHistoryConfig : IEntityTypeConfiguration<SyncHistory>
{
    public void Configure(EntityTypeBuilder<SyncHistory> builder)
    {

        builder.ToTable("SyncHistory", "Genp");

        // Properties
        builder.Property(e => e.IntegrationId)
            .IsRequired();

        builder.Property(e => e.ObjectId)
            .IsRequired();

        builder.Property(e => e.SyncType)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.Direction)
            .HasMaxLength(10)
            .IsRequired();

        builder.Property(e => e.Status)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.RecordsProcessed)
            .HasDefaultValue(0);

        builder.Property(e => e.RecordsSucceeded)
            .HasDefaultValue(0);

        builder.Property(e => e.RecordsFailed)
            .HasDefaultValue(0);

        builder.Property(e => e.StartedAt)
            .IsRequired();

        builder.Property(e => e.CompletedAt);

        builder.Property(e => e.ErrorMessage)
            .HasColumnType("TEXT");

        builder.Property(e => e.SyncDetails)
            .HasColumnType("TEXT");

        builder.IsMultiTenant();

        // Indexes
        builder.HasIndex(e => e.IntegrationId)
            .HasDatabaseName("IX_SyncHistory_IntegrationId");

        builder.HasIndex(e => e.ObjectId)
            .HasDatabaseName("IX_SyncHistory_ObjectId");

        builder.HasIndex(e => e.SyncType)
            .HasDatabaseName("IX_SyncHistory_SyncType");

        builder.HasIndex(e => e.Direction)
            .HasDatabaseName("IX_SyncHistory_Direction");

        builder.HasIndex(e => e.Status)
            .HasDatabaseName("IX_SyncHistory_Status");

        builder.HasIndex(e => e.StartedAt)
            .HasDatabaseName("IX_SyncHistory_StartedAt");

        builder.HasIndex(e => e.CompletedAt)
            .HasDatabaseName("IX_SyncHistory_CompletedAt");

        // Relationships
        builder.HasOne(e => e.Integration)
            .WithMany(e => e.SyncHistories)
            .HasForeignKey(e => e.IntegrationId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.Object)
            .WithMany(e => e.SyncHistories)
            .HasForeignKey(e => e.ObjectId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
