using Ardalis.Specification;
using Domain.Entities;

namespace Application.ObjectValues.Specifications;

/// <summary>
/// Specification to get DataType by Name
/// </summary>
public class DataTypeByNameSpec : Specification<DataType>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public DataTypeByNameSpec(string name)
    {
        Query.Where(dt => dt.Name == name);

        // Take only one record
        Query.Take(1);
    }
}
