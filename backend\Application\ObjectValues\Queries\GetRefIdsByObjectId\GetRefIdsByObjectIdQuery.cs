using Application.ObjectValues.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.ObjectValues.Queries.GetRefIdsByObjectId;

/// <summary>
/// Query to get all RefIds for a specific ObjectId with pagination
/// </summary>
public class GetRefIdsByObjectIdQuery : IRequest<Result<PaginatedResult<ObjectValuesRefIdSummaryDto>>>
{
    /// <summary>
    /// Object ID to filter by
    /// </summary>
    public Guid ObjectId { get; set; }
    
    /// <summary>
    /// Search term for values
    /// </summary>
    public string? SearchTerm { get; set; }
    
    /// <summary>
    /// Include only active records
    /// </summary>
    public bool OnlyActive { get; set; } = true;
    
    /// <summary>
    /// Page number for pagination
    /// </summary>
    public int PageNumber { get; set; } = 1;
    
    /// <summary>
    /// Page size for pagination
    /// </summary>
    public int PageSize { get; set; } = 50;
    
    /// <summary>
    /// Order by field
    /// </summary>
    public string? OrderBy { get; set; }
    
    /// <summary>
    /// Order direction (asc/desc)
    /// </summary>
    public string OrderDirection { get; set; } = "desc";
    
    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="objectId">Object ID</param>
    /// <param name="searchTerm">Search term</param>
    /// <param name="onlyActive">Only active records</param>
    /// <param name="pageNumber">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="orderBy">Order by field</param>
    /// <param name="orderDirection">Order direction</param>
    public GetRefIdsByObjectIdQuery(
        Guid objectId,
        string? searchTerm = null,
        bool onlyActive = true,
        int pageNumber = 1,
        int pageSize = 50,
        string? orderBy = null,
        string orderDirection = "desc")
    {
        ObjectId = objectId;
        SearchTerm = searchTerm;
        OnlyActive = onlyActive;
        PageNumber = pageNumber;
        PageSize = pageSize;
        OrderBy = orderBy;
        OrderDirection = orderDirection;
    }
}
