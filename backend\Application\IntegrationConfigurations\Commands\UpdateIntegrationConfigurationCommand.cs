using Application.IntegrationConfigurations.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.IntegrationConfigurations.Commands;

/// <summary>
/// Update integration configuration command
/// </summary>
public class UpdateIntegrationConfigurationCommand : IRequest<Result<ViewIntegrationConfigurationDto>>
{
    /// <summary>
    /// Integration Configuration ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Integration API ID this configuration uses
    /// </summary>
    public Guid IntegrationApiId { get; set; }

    /// <summary>
    /// Object ID this configuration applies to
    /// </summary>
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Data flow direction (e.g., "In", "Out", "Both")
    /// </summary>
    public string? Direction { get; set; }

    /// <summary>
    /// Whether the configuration is active
    /// </summary>
    public bool IsActive { get; set; }
}
