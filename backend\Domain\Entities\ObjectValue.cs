using Domain.Common.Contracts;
using Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure;

namespace Domain.Entities;

/// <summary>
/// Actual object instance data (e.g., "Tower A", "Unit A101")
/// </summary>
public class ObjectValue : AuditableEntity, IAggregateRoot
{

    /// <summary>
    /// Object metadata ID
    /// </summary>
    public Guid ObjectMetadataId { get; set; }

    /// <summary>
    /// Reference ID for grouping related values
    /// </summary>
    public Guid? RefId { get; set; }

    /// <summary>
    /// Parent object value ID for hierarchical structure
    /// </summary>
    public Guid? ParentObjectValueId { get; set; }

    /// <summary>
    /// The actual value as text
    /// </summary>
    public string? Value { get; set; }
    public bool? IsRefId { get; set; }
    public bool? IsUserId { get; set; }

    // Navigation properties
    /// <summary>
    /// Object metadata link
    /// </summary>
    public virtual ObjectMetadata ObjectMetadata { get; set; } = null!;

    /// <summary>
    /// Parent object value
    /// </summary>
    public virtual ObjectValue? ParentObjectValue { get; set; }

    /// <summary>
    /// Child object values
    /// </summary>
    public virtual ICollection<ObjectValue> ChildObjectValues { get; set; } = new List<ObjectValue>();
}
