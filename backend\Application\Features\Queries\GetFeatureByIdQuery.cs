using Application.Features.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Features.Queries;

/// <summary>
/// Get feature by ID query
/// </summary>
public class GetFeatureByIdQuery : IRequest<Result<FeatureDto>>
{
    /// <summary>
    /// Feature ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public GetFeatureByIdQuery(Guid id)
    {
        Id = id;
    }
}
