﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Migrators.Migrations.Application
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "Genp");

            migrationBuilder.CreateTable(
                name: "DataTypes",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    DisplayName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Category = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    UiComponent = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ValidationPattern = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    MinLength = table.Column<int>(type: "integer", nullable: true),
                    MaxLength = table.Column<int>(type: "integer", nullable: true),
                    MinValue = table.Column<decimal>(type: "numeric", nullable: true),
                    MaxValue = table.Column<decimal>(type: "numeric", nullable: true),
                    DecimalPlaces = table.Column<int>(type: "integer", nullable: true),
                    StepValue = table.Column<decimal>(type: "numeric", nullable: true),
                    IsRequired = table.Column<bool>(type: "boolean", nullable: false),
                    InputType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    InputMask = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Placeholder = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    HtmlAttributes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    DefaultOptions = table.Column<string>(type: "TEXT", nullable: true),
                    AllowsMultiple = table.Column<bool>(type: "boolean", nullable: false),
                    AllowsCustomOptions = table.Column<bool>(type: "boolean", nullable: false),
                    MaxSelections = table.Column<int>(type: "integer", nullable: true),
                    AllowedFileTypes = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    MaxFileSizeBytes = table.Column<long>(type: "bigint", nullable: true),
                    RequiredErrorMessage = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    PatternErrorMessage = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    MinLengthErrorMessage = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    MaxLengthErrorMessage = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    MinValueErrorMessage = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    MaxValueErrorMessage = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    FileTypeErrorMessage = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    FileSizeErrorMessage = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DataTypes", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Products",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Description = table.Column<string>(type: "TEXT", nullable: true),
                    Version = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "1.0.0"),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    IsUserImported = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    IsRoleAssigned = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    ApiKey = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    IsOnboardCompleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    ApplicationUrl = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    Icon = table.Column<string>(type: "text", nullable: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Products", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Users",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    FirstName = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: true),
                    LastName = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: true),
                    ExternalUserId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    LastLoginAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RefreshToken = table.Column<string>(type: "text", nullable: true),
                    RefreshTokenExpiryTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    UserName = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    NormalizedUserName = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    Email = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    NormalizedEmail = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    EmailConfirmed = table.Column<bool>(type: "boolean", nullable: false),
                    PasswordHash = table.Column<string>(type: "text", nullable: true),
                    SecurityStamp = table.Column<string>(type: "text", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "text", nullable: true),
                    PhoneNumber = table.Column<string>(type: "text", nullable: true),
                    PhoneNumberConfirmed = table.Column<bool>(type: "boolean", nullable: false),
                    TwoFactorEnabled = table.Column<bool>(type: "boolean", nullable: false),
                    LockoutEnd = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    LockoutEnabled = table.Column<bool>(type: "boolean", nullable: false),
                    AccessFailedCount = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Users", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Metadata",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    MetadataKey = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    DataTypeId = table.Column<Guid>(type: "uuid", nullable: false),
                    CustomValidationPattern = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CustomMinLength = table.Column<int>(type: "integer", nullable: true),
                    CustomMaxLength = table.Column<int>(type: "integer", nullable: true),
                    CustomMinValue = table.Column<decimal>(type: "numeric", nullable: true),
                    CustomMaxValue = table.Column<decimal>(type: "numeric", nullable: true),
                    CustomIsRequired = table.Column<bool>(type: "boolean", nullable: true),
                    CustomPlaceholder = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    CustomOptions = table.Column<string>(type: "TEXT", nullable: true),
                    CustomMaxSelections = table.Column<int>(type: "integer", nullable: true),
                    CustomAllowedFileTypes = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    CustomMaxFileSize = table.Column<long>(type: "bigint", nullable: true),
                    CustomErrorMessage = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    DisplayLabel = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    HelpText = table.Column<string>(type: "TEXT", nullable: true),
                    FieldOrder = table.Column<int>(type: "integer", nullable: true),
                    IsVisible = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    IsReadonly = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Metadata", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Metadata_DataTypes_DataTypeId",
                        column: x => x.DataTypeId,
                        principalSchema: "Genp",
                        principalTable: "DataTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Features",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ProductId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Description = table.Column<string>(type: "TEXT", nullable: true),
                    IsDefault = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Features", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Features_Products_ProductId",
                        column: x => x.ProductId,
                        principalSchema: "Genp",
                        principalTable: "Products",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "IntegrationApi",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ProductId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    EndpointUrl = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false),
                    Schema = table.Column<string>(type: "TEXT", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_IntegrationApi", x => x.Id);
                    table.ForeignKey(
                        name: "FK_IntegrationApi_Products_ProductId",
                        column: x => x.ProductId,
                        principalSchema: "Genp",
                        principalTable: "Products",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Integrations",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ProductId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    AuthType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    AuthConfig = table.Column<string>(type: "TEXT", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    SyncFrequency = table.Column<TimeSpan>(type: "interval", nullable: true),
                    LastSyncAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Integrations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Integrations_Products_ProductId",
                        column: x => x.ProductId,
                        principalSchema: "Genp",
                        principalTable: "Products",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Roles",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ProductId = table.Column<Guid>(type: "uuid", nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    IsSystemRole = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    Permissions = table.Column<string>(type: "TEXT", nullable: false, defaultValue: "{}"),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    Name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    NormalizedName = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Roles", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Roles_Products_ProductId",
                        column: x => x.ProductId,
                        principalSchema: "Genp",
                        principalTable: "Products",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Subscriptions",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ProductId = table.Column<Guid>(type: "uuid", nullable: false),
                    SubscriptionType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "standard"),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "active"),
                    StartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    EndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    AutoRenew = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    PricingTier = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Subscriptions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Subscriptions_Products_ProductId",
                        column: x => x.ProductId,
                        principalSchema: "Genp",
                        principalTable: "Products",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "UserClaims",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    ClaimType = table.Column<string>(type: "text", nullable: true),
                    ClaimValue = table.Column<string>(type: "text", nullable: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserClaims", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UserClaims_Users_UserId",
                        column: x => x.UserId,
                        principalSchema: "Genp",
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "UserLogins",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    LoginProvider = table.Column<string>(type: "text", nullable: false),
                    ProviderKey = table.Column<string>(type: "text", nullable: false),
                    ProviderDisplayName = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserLogins", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UserLogins_Users_UserId",
                        column: x => x.UserId,
                        principalSchema: "Genp",
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "UserTokens",
                schema: "Genp",
                columns: table => new
                {
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    LoginProvider = table.Column<string>(type: "text", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Value = table.Column<string>(type: "text", nullable: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserTokens", x => new { x.UserId, x.LoginProvider, x.Name });
                    table.ForeignKey(
                        name: "FK_UserTokens_Users_UserId",
                        column: x => x.UserId,
                        principalSchema: "Genp",
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ProductMetadata",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ProductId = table.Column<Guid>(type: "uuid", nullable: false),
                    MetadataId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsUnique = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    ShouldVisibleInList = table.Column<bool>(type: "boolean", nullable: false),
                    ShouldVisibleInEdit = table.Column<bool>(type: "boolean", nullable: false),
                    ShouldVisibleInCreate = table.Column<bool>(type: "boolean", nullable: false),
                    ShouldVisibleInView = table.Column<bool>(type: "boolean", nullable: false),
                    IsCalculate = table.Column<bool>(type: "boolean", nullable: false),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProductMetadata", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ProductMetadata_Metadata_MetadataId",
                        column: x => x.MetadataId,
                        principalSchema: "Genp",
                        principalTable: "Metadata",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ProductMetadata_Products_ProductId",
                        column: x => x.ProductId,
                        principalSchema: "Genp",
                        principalTable: "Products",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TenantInfoMetadata",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    MetadataId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsUnique = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    ShouldVisibleInList = table.Column<bool>(type: "boolean", nullable: false),
                    ShouldVisibleInEdit = table.Column<bool>(type: "boolean", nullable: false),
                    ShouldVisibleInCreate = table.Column<bool>(type: "boolean", nullable: false),
                    ShouldVisibleInView = table.Column<bool>(type: "boolean", nullable: false),
                    IsCalculate = table.Column<bool>(type: "boolean", nullable: false),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TenantInfoMetadata", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TenantInfoMetadata_Metadata_MetadataId",
                        column: x => x.MetadataId,
                        principalSchema: "Genp",
                        principalTable: "Metadata",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "UserMetadata",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    MetadataId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsUnique = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    ShouldVisibleInList = table.Column<bool>(type: "boolean", nullable: false),
                    ShouldVisibleInEdit = table.Column<bool>(type: "boolean", nullable: false),
                    ShouldVisibleInCreate = table.Column<bool>(type: "boolean", nullable: false),
                    ShouldVisibleInView = table.Column<bool>(type: "boolean", nullable: false),
                    IsCalculate = table.Column<bool>(type: "boolean", nullable: false),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserMetadata", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UserMetadata_Metadata_MetadataId",
                        column: x => x.MetadataId,
                        principalSchema: "Genp",
                        principalTable: "Metadata",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_UserMetadata_Users_UserId",
                        column: x => x.UserId,
                        principalSchema: "Genp",
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "FeatureMetadata",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    FeatureId = table.Column<Guid>(type: "uuid", nullable: false),
                    MetadataId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsUnique = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    ShouldVisibleInList = table.Column<bool>(type: "boolean", nullable: false),
                    ShouldVisibleInEdit = table.Column<bool>(type: "boolean", nullable: false),
                    ShouldVisibleInCreate = table.Column<bool>(type: "boolean", nullable: false),
                    ShouldVisibleInView = table.Column<bool>(type: "boolean", nullable: false),
                    IsCalculate = table.Column<bool>(type: "boolean", nullable: false),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FeatureMetadata", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FeatureMetadata_Features_FeatureId",
                        column: x => x.FeatureId,
                        principalSchema: "Genp",
                        principalTable: "Features",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_FeatureMetadata_Metadata_MetadataId",
                        column: x => x.MetadataId,
                        principalSchema: "Genp",
                        principalTable: "Metadata",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Objects",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    FeatureId = table.Column<Guid>(type: "uuid", nullable: false),
                    ParentObjectId = table.Column<Guid>(type: "uuid", nullable: true),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Description = table.Column<string>(type: "TEXT", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Objects", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Objects_Features_FeatureId",
                        column: x => x.FeatureId,
                        principalSchema: "Genp",
                        principalTable: "Features",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Objects_Objects_ParentObjectId",
                        column: x => x.ParentObjectId,
                        principalSchema: "Genp",
                        principalTable: "Objects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "AspNetRoleClaims",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    RoleId = table.Column<Guid>(type: "uuid", nullable: false),
                    ClaimType = table.Column<string>(type: "text", nullable: true),
                    ClaimValue = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetRoleClaims", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AspNetRoleClaims_Roles_RoleId",
                        column: x => x.RoleId,
                        principalSchema: "Genp",
                        principalTable: "Roles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "RoleMetadata",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    RoleId = table.Column<Guid>(type: "uuid", nullable: false),
                    MetadataId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsUnique = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    ShouldVisibleInList = table.Column<bool>(type: "boolean", nullable: false),
                    ShouldVisibleInEdit = table.Column<bool>(type: "boolean", nullable: false),
                    ShouldVisibleInCreate = table.Column<bool>(type: "boolean", nullable: false),
                    ShouldVisibleInView = table.Column<bool>(type: "boolean", nullable: false),
                    IsCalculate = table.Column<bool>(type: "boolean", nullable: false),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RoleMetadata", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RoleMetadata_Metadata_MetadataId",
                        column: x => x.MetadataId,
                        principalSchema: "Genp",
                        principalTable: "Metadata",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RoleMetadata_Roles_RoleId",
                        column: x => x.RoleId,
                        principalSchema: "Genp",
                        principalTable: "Roles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "UserRoles",
                schema: "Genp",
                columns: table => new
                {
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    RoleId = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserRoles", x => new { x.UserId, x.RoleId });
                    table.ForeignKey(
                        name: "FK_UserRoles_Roles_RoleId",
                        column: x => x.RoleId,
                        principalSchema: "Genp",
                        principalTable: "Roles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_UserRoles_Users_UserId",
                        column: x => x.UserId,
                        principalSchema: "Genp",
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "SubscriptionMetadata",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    SubscriptionId = table.Column<Guid>(type: "uuid", nullable: false),
                    MetadataId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsUnique = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    ShouldVisibleInList = table.Column<bool>(type: "boolean", nullable: false),
                    ShouldVisibleInEdit = table.Column<bool>(type: "boolean", nullable: false),
                    ShouldVisibleInCreate = table.Column<bool>(type: "boolean", nullable: false),
                    ShouldVisibleInView = table.Column<bool>(type: "boolean", nullable: false),
                    IsCalculate = table.Column<bool>(type: "boolean", nullable: false),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SubscriptionMetadata", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SubscriptionMetadata_Metadata_MetadataId",
                        column: x => x.MetadataId,
                        principalSchema: "Genp",
                        principalTable: "Metadata",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_SubscriptionMetadata_Subscriptions_SubscriptionId",
                        column: x => x.SubscriptionId,
                        principalSchema: "Genp",
                        principalTable: "Subscriptions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ProductValues",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ProductMetadataId = table.Column<Guid>(type: "uuid", nullable: false),
                    RefId = table.Column<Guid>(type: "uuid", nullable: true),
                    Value = table.Column<string>(type: "TEXT", nullable: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProductValues", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ProductValues_ProductMetadata_ProductMetadataId",
                        column: x => x.ProductMetadataId,
                        principalSchema: "Genp",
                        principalTable: "ProductMetadata",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TenantInfoValues",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantInfoMetadataId = table.Column<Guid>(type: "uuid", nullable: false),
                    RefId = table.Column<Guid>(type: "uuid", nullable: true),
                    Value = table.Column<string>(type: "TEXT", nullable: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TenantInfoValues", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TenantInfoValues_TenantInfoMetadata_TenantInfoMetadataId",
                        column: x => x.TenantInfoMetadataId,
                        principalSchema: "Genp",
                        principalTable: "TenantInfoMetadata",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "UserValues",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    UserMetadataId = table.Column<Guid>(type: "uuid", nullable: false),
                    RefId = table.Column<Guid>(type: "uuid", nullable: true),
                    Value = table.Column<string>(type: "TEXT", nullable: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserValues", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UserValues_UserMetadata_UserMetadataId",
                        column: x => x.UserMetadataId,
                        principalSchema: "Genp",
                        principalTable: "UserMetadata",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "FeatureValues",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    FeatureMetadataId = table.Column<Guid>(type: "uuid", nullable: false),
                    RefId = table.Column<Guid>(type: "uuid", nullable: true),
                    Value = table.Column<string>(type: "TEXT", nullable: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FeatureValues", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FeatureValues_FeatureMetadata_FeatureMetadataId",
                        column: x => x.FeatureMetadataId,
                        principalSchema: "Genp",
                        principalTable: "FeatureMetadata",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ConflictResolution",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    IntegrationId = table.Column<Guid>(type: "uuid", nullable: false),
                    ObjectId = table.Column<Guid>(type: "uuid", nullable: false),
                    FieldName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    ConflictType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ResolutionStrategy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    SourceValue = table.Column<string>(type: "TEXT", nullable: true),
                    TargetValue = table.Column<string>(type: "TEXT", nullable: true),
                    ResolvedValue = table.Column<string>(type: "TEXT", nullable: true),
                    ResolvedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ResolvedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsResolved = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    MergeRules = table.Column<string>(type: "text", nullable: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ConflictResolution", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ConflictResolution_Integrations_IntegrationId",
                        column: x => x.IntegrationId,
                        principalSchema: "Genp",
                        principalTable: "Integrations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ConflictResolution_Objects_ObjectId",
                        column: x => x.ObjectId,
                        principalSchema: "Genp",
                        principalTable: "Objects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ConflictResolution_Users_ResolvedBy",
                        column: x => x.ResolvedBy,
                        principalSchema: "Genp",
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "IntegrationConfiguration",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    IntegrationId = table.Column<Guid>(type: "uuid", nullable: false),
                    IntegrationApiId = table.Column<Guid>(type: "uuid", nullable: false),
                    ObjectId = table.Column<Guid>(type: "uuid", nullable: false),
                    Direction = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_IntegrationConfiguration", x => x.Id);
                    table.ForeignKey(
                        name: "FK_IntegrationConfiguration_IntegrationApi_IntegrationApiId",
                        column: x => x.IntegrationApiId,
                        principalSchema: "Genp",
                        principalTable: "IntegrationApi",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_IntegrationConfiguration_Integrations_IntegrationId",
                        column: x => x.IntegrationId,
                        principalSchema: "Genp",
                        principalTable: "Integrations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_IntegrationConfiguration_Objects_ObjectId",
                        column: x => x.ObjectId,
                        principalSchema: "Genp",
                        principalTable: "Objects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ObjectMetadata",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ObjectId = table.Column<Guid>(type: "uuid", nullable: false),
                    MetadataId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsUnique = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    ShouldVisibleInList = table.Column<bool>(type: "boolean", nullable: false),
                    ShouldVisibleInEdit = table.Column<bool>(type: "boolean", nullable: false),
                    ShouldVisibleInCreate = table.Column<bool>(type: "boolean", nullable: false),
                    ShouldVisibleInView = table.Column<bool>(type: "boolean", nullable: false),
                    IsCalculate = table.Column<bool>(type: "boolean", nullable: false),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ObjectMetadata", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ObjectMetadata_Metadata_MetadataId",
                        column: x => x.MetadataId,
                        principalSchema: "Genp",
                        principalTable: "Metadata",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ObjectMetadata_Objects_ObjectId",
                        column: x => x.ObjectId,
                        principalSchema: "Genp",
                        principalTable: "Objects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "SyncHistory",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    IntegrationId = table.Column<Guid>(type: "uuid", nullable: false),
                    ObjectId = table.Column<Guid>(type: "uuid", nullable: false),
                    SyncType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Direction = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    RecordsProcessed = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    RecordsSucceeded = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    RecordsFailed = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    StartedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CompletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ErrorMessage = table.Column<string>(type: "TEXT", nullable: true),
                    SyncDetails = table.Column<string>(type: "TEXT", nullable: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SyncHistory", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SyncHistory_Integrations_IntegrationId",
                        column: x => x.IntegrationId,
                        principalSchema: "Genp",
                        principalTable: "Integrations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_SyncHistory_Objects_ObjectId",
                        column: x => x.ObjectId,
                        principalSchema: "Genp",
                        principalTable: "Objects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "RoleValues",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    RoleMetadataId = table.Column<Guid>(type: "uuid", nullable: false),
                    RefId = table.Column<Guid>(type: "uuid", nullable: true),
                    Value = table.Column<string>(type: "TEXT", nullable: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RoleValues", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RoleValues_RoleMetadata_RoleMetadataId",
                        column: x => x.RoleMetadataId,
                        principalSchema: "Genp",
                        principalTable: "RoleMetadata",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "SubscriptionValues",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    SubscriptionMetadataId = table.Column<Guid>(type: "uuid", nullable: false),
                    RefId = table.Column<Guid>(type: "uuid", nullable: true),
                    Value = table.Column<string>(type: "TEXT", nullable: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SubscriptionValues", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SubscriptionValues_SubscriptionMetadata_SubscriptionMetadat~",
                        column: x => x.SubscriptionMetadataId,
                        principalSchema: "Genp",
                        principalTable: "SubscriptionMetadata",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "FieldMapping",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    IntegrationId = table.Column<Guid>(type: "uuid", nullable: false),
                    ApiName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    SourceField = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    SourceType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ObjectMetadataId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: true),
                    RoleId = table.Column<Guid>(type: "uuid", nullable: true),
                    TargetObjectName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    TransformationRules = table.Column<string>(type: "TEXT", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FieldMapping", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FieldMapping_Integrations_IntegrationId",
                        column: x => x.IntegrationId,
                        principalSchema: "Genp",
                        principalTable: "Integrations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_FieldMapping_ObjectMetadata_ObjectMetadataId",
                        column: x => x.ObjectMetadataId,
                        principalSchema: "Genp",
                        principalTable: "ObjectMetadata",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_FieldMapping_Roles_RoleId",
                        column: x => x.RoleId,
                        principalSchema: "Genp",
                        principalTable: "Roles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_FieldMapping_Users_UserId",
                        column: x => x.UserId,
                        principalSchema: "Genp",
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "ObjectValues",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ObjectMetadataId = table.Column<Guid>(type: "uuid", nullable: false),
                    RefId = table.Column<Guid>(type: "uuid", nullable: true),
                    ParentObjectValueId = table.Column<Guid>(type: "uuid", nullable: true),
                    Value = table.Column<string>(type: "TEXT", nullable: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ObjectValues", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ObjectValues_ObjectMetadata_ObjectMetadataId",
                        column: x => x.ObjectMetadataId,
                        principalSchema: "Genp",
                        principalTable: "ObjectMetadata",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ObjectValues_ObjectValues_ParentObjectValueId",
                        column: x => x.ParentObjectValueId,
                        principalSchema: "Genp",
                        principalTable: "ObjectValues",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AspNetRoleClaims_RoleId",
                table: "AspNetRoleClaims",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_ConflictResolution_ConflictType",
                schema: "Genp",
                table: "ConflictResolution",
                column: "ConflictType");

            migrationBuilder.CreateIndex(
                name: "IX_ConflictResolution_FieldName",
                schema: "Genp",
                table: "ConflictResolution",
                column: "FieldName");

            migrationBuilder.CreateIndex(
                name: "IX_ConflictResolution_IntegrationId",
                schema: "Genp",
                table: "ConflictResolution",
                column: "IntegrationId");

            migrationBuilder.CreateIndex(
                name: "IX_ConflictResolution_IsResolved",
                schema: "Genp",
                table: "ConflictResolution",
                column: "IsResolved");

            migrationBuilder.CreateIndex(
                name: "IX_ConflictResolution_ObjectId",
                schema: "Genp",
                table: "ConflictResolution",
                column: "ObjectId");

            migrationBuilder.CreateIndex(
                name: "IX_ConflictResolution_ResolutionStrategy",
                schema: "Genp",
                table: "ConflictResolution",
                column: "ResolutionStrategy");

            migrationBuilder.CreateIndex(
                name: "IX_ConflictResolution_ResolvedAt",
                schema: "Genp",
                table: "ConflictResolution",
                column: "ResolvedAt");

            migrationBuilder.CreateIndex(
                name: "IX_ConflictResolution_ResolvedBy",
                schema: "Genp",
                table: "ConflictResolution",
                column: "ResolvedBy");

            migrationBuilder.CreateIndex(
                name: "IX_DataTypes_Category",
                schema: "Genp",
                table: "DataTypes",
                column: "Category");

            migrationBuilder.CreateIndex(
                name: "IX_DataTypes_IsActive",
                schema: "Genp",
                table: "DataTypes",
                column: "IsActive",
                filter: "\"IsActive\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_DataTypes_Name",
                schema: "Genp",
                table: "DataTypes",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_FeatureMetadata_FeatureId",
                schema: "Genp",
                table: "FeatureMetadata",
                column: "FeatureId");

            migrationBuilder.CreateIndex(
                name: "IX_FeatureMetadata_FeatureId_MetadataId",
                schema: "Genp",
                table: "FeatureMetadata",
                columns: new[] { "FeatureId", "MetadataId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_FeatureMetadata_IsActive",
                schema: "Genp",
                table: "FeatureMetadata",
                column: "IsActive",
                filter: "\"IsActive\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_FeatureMetadata_IsUnique",
                schema: "Genp",
                table: "FeatureMetadata",
                column: "IsUnique");

            migrationBuilder.CreateIndex(
                name: "IX_FeatureMetadata_MetadataId",
                schema: "Genp",
                table: "FeatureMetadata",
                column: "MetadataId");

            migrationBuilder.CreateIndex(
                name: "IX_Features_IsActive",
                schema: "Genp",
                table: "Features",
                column: "IsActive",
                filter: "\"IsActive\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_Features_IsDefault",
                schema: "Genp",
                table: "Features",
                column: "IsDefault");

            migrationBuilder.CreateIndex(
                name: "IX_Features_ProductId",
                schema: "Genp",
                table: "Features",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_Features_ProductId_Name",
                schema: "Genp",
                table: "Features",
                columns: new[] { "ProductId", "Name" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_FeatureValues_CreatedAt",
                schema: "Genp",
                table: "FeatureValues",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_FeatureValues_FeatureMetadataId",
                schema: "Genp",
                table: "FeatureValues",
                column: "FeatureMetadataId");

            migrationBuilder.CreateIndex(
                name: "IX_FeatureValues_RefId",
                schema: "Genp",
                table: "FeatureValues",
                column: "RefId");

            migrationBuilder.CreateIndex(
                name: "IX_FieldMapping_ApiName",
                schema: "Genp",
                table: "FieldMapping",
                column: "ApiName");

            migrationBuilder.CreateIndex(
                name: "IX_FieldMapping_IntegrationId",
                schema: "Genp",
                table: "FieldMapping",
                column: "IntegrationId");

            migrationBuilder.CreateIndex(
                name: "IX_FieldMapping_IsActive",
                schema: "Genp",
                table: "FieldMapping",
                column: "IsActive",
                filter: "\"IsActive\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_FieldMapping_ObjectMetadataId",
                schema: "Genp",
                table: "FieldMapping",
                column: "ObjectMetadataId");

            migrationBuilder.CreateIndex(
                name: "IX_FieldMapping_RoleId",
                schema: "Genp",
                table: "FieldMapping",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_FieldMapping_SourceField",
                schema: "Genp",
                table: "FieldMapping",
                column: "SourceField");

            migrationBuilder.CreateIndex(
                name: "IX_FieldMapping_SourceType",
                schema: "Genp",
                table: "FieldMapping",
                column: "SourceType");

            migrationBuilder.CreateIndex(
                name: "IX_FieldMapping_UserId",
                schema: "Genp",
                table: "FieldMapping",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_IntegrationApi_IsActive",
                schema: "Genp",
                table: "IntegrationApi",
                column: "IsActive",
                filter: "\"IsActive\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_IntegrationApi_ProductId",
                schema: "Genp",
                table: "IntegrationApi",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_IntegrationApi_ProductId_Name",
                schema: "Genp",
                table: "IntegrationApi",
                columns: new[] { "ProductId", "Name" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_IntegrationConfiguration_Direction",
                schema: "Genp",
                table: "IntegrationConfiguration",
                column: "Direction");

            migrationBuilder.CreateIndex(
                name: "IX_IntegrationConfiguration_IntegrationApiId",
                schema: "Genp",
                table: "IntegrationConfiguration",
                column: "IntegrationApiId");

            migrationBuilder.CreateIndex(
                name: "IX_IntegrationConfiguration_IntegrationId",
                schema: "Genp",
                table: "IntegrationConfiguration",
                column: "IntegrationId");

            migrationBuilder.CreateIndex(
                name: "IX_IntegrationConfiguration_IntegrationId_IntegrationApiId_ObjectId",
                schema: "Genp",
                table: "IntegrationConfiguration",
                columns: new[] { "IntegrationId", "IntegrationApiId", "ObjectId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_IntegrationConfiguration_IsActive",
                schema: "Genp",
                table: "IntegrationConfiguration",
                column: "IsActive",
                filter: "\"IsActive\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_IntegrationConfiguration_ObjectId",
                schema: "Genp",
                table: "IntegrationConfiguration",
                column: "ObjectId");

            migrationBuilder.CreateIndex(
                name: "IX_Integration_AuthType",
                schema: "Genp",
                table: "Integrations",
                column: "AuthType");

            migrationBuilder.CreateIndex(
                name: "IX_Integration_IsActive",
                schema: "Genp",
                table: "Integrations",
                column: "IsActive",
                filter: "\"IsActive\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_Integration_ProductId",
                schema: "Genp",
                table: "Integrations",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_Integration_ProductId_Name",
                schema: "Genp",
                table: "Integrations",
                columns: new[] { "ProductId", "Name" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Metadata_DataTypeId",
                schema: "Genp",
                table: "Metadata",
                column: "DataTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_Metadata_IsVisible",
                schema: "Genp",
                table: "Metadata",
                column: "IsVisible",
                filter: "\"IsVisible\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_Metadata_MetadataKey",
                schema: "Genp",
                table: "Metadata",
                column: "MetadataKey",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ObjectMetadata_IsActive",
                schema: "Genp",
                table: "ObjectMetadata",
                column: "IsActive",
                filter: "\"IsActive\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_ObjectMetadata_IsUnique",
                schema: "Genp",
                table: "ObjectMetadata",
                column: "IsUnique");

            migrationBuilder.CreateIndex(
                name: "IX_ObjectMetadata_MetadataId",
                schema: "Genp",
                table: "ObjectMetadata",
                column: "MetadataId");

            migrationBuilder.CreateIndex(
                name: "IX_ObjectMetadata_ObjectId",
                schema: "Genp",
                table: "ObjectMetadata",
                column: "ObjectId");

            migrationBuilder.CreateIndex(
                name: "IX_ObjectMetadata_ObjectId_MetadataId",
                schema: "Genp",
                table: "ObjectMetadata",
                columns: new[] { "ObjectId", "MetadataId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Objects_FeatureId",
                schema: "Genp",
                table: "Objects",
                column: "FeatureId");

            migrationBuilder.CreateIndex(
                name: "IX_Objects_FeatureId_Name",
                schema: "Genp",
                table: "Objects",
                columns: new[] { "FeatureId", "Name" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Objects_IsActive",
                schema: "Genp",
                table: "Objects",
                column: "IsActive",
                filter: "\"IsActive\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_Objects_ParentObjectId",
                schema: "Genp",
                table: "Objects",
                column: "ParentObjectId");

            migrationBuilder.CreateIndex(
                name: "IX_ObjectValues_CreatedAt",
                schema: "Genp",
                table: "ObjectValues",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_ObjectValues_ObjectMetadataId",
                schema: "Genp",
                table: "ObjectValues",
                column: "ObjectMetadataId");

            migrationBuilder.CreateIndex(
                name: "IX_ObjectValues_ParentObjectValueId",
                schema: "Genp",
                table: "ObjectValues",
                column: "ParentObjectValueId");

            migrationBuilder.CreateIndex(
                name: "IX_ObjectValues_RefId",
                schema: "Genp",
                table: "ObjectValues",
                column: "RefId");

            migrationBuilder.CreateIndex(
                name: "IX_ProductMetadata_IsActive",
                schema: "Genp",
                table: "ProductMetadata",
                column: "IsActive",
                filter: "\"IsActive\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_ProductMetadata_IsUnique",
                schema: "Genp",
                table: "ProductMetadata",
                column: "IsUnique");

            migrationBuilder.CreateIndex(
                name: "IX_ProductMetadata_MetadataId",
                schema: "Genp",
                table: "ProductMetadata",
                column: "MetadataId");

            migrationBuilder.CreateIndex(
                name: "IX_ProductMetadata_ProductId",
                schema: "Genp",
                table: "ProductMetadata",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_ProductMetadata_ProductId_MetadataId",
                schema: "Genp",
                table: "ProductMetadata",
                columns: new[] { "ProductId", "MetadataId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Products_IsActive",
                schema: "Genp",
                table: "Products",
                column: "IsActive",
                filter: "\"IsActive\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_ProductValues_CreatedAt",
                schema: "Genp",
                table: "ProductValues",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_ProductValues_ProductMetadataId",
                schema: "Genp",
                table: "ProductValues",
                column: "ProductMetadataId");

            migrationBuilder.CreateIndex(
                name: "IX_ProductValues_RefId",
                schema: "Genp",
                table: "ProductValues",
                column: "RefId");

            migrationBuilder.CreateIndex(
                name: "IX_RoleMetadata_IsActive",
                schema: "Genp",
                table: "RoleMetadata",
                column: "IsActive",
                filter: "\"IsActive\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_RoleMetadata_IsUnique",
                schema: "Genp",
                table: "RoleMetadata",
                column: "IsUnique");

            migrationBuilder.CreateIndex(
                name: "IX_RoleMetadata_MetadataId",
                schema: "Genp",
                table: "RoleMetadata",
                column: "MetadataId");

            migrationBuilder.CreateIndex(
                name: "IX_RoleMetadata_RoleId",
                schema: "Genp",
                table: "RoleMetadata",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_RoleMetadata_RoleId_MetadataId",
                schema: "Genp",
                table: "RoleMetadata",
                columns: new[] { "RoleId", "MetadataId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Roles_IsActive",
                schema: "Genp",
                table: "Roles",
                column: "IsActive",
                filter: "\"IsActive\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_Roles_IsSystemRole",
                schema: "Genp",
                table: "Roles",
                column: "IsSystemRole");

            migrationBuilder.CreateIndex(
                name: "IX_Roles_ProductId",
                schema: "Genp",
                table: "Roles",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "RoleNameIndex",
                schema: "Genp",
                table: "Roles",
                columns: new[] { "NormalizedName", "TenantId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_RoleValues_CreatedAt",
                schema: "Genp",
                table: "RoleValues",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_RoleValues_RefId",
                schema: "Genp",
                table: "RoleValues",
                column: "RefId");

            migrationBuilder.CreateIndex(
                name: "IX_RoleValues_RoleMetadataId",
                schema: "Genp",
                table: "RoleValues",
                column: "RoleMetadataId");

            migrationBuilder.CreateIndex(
                name: "IX_SubscriptionMetadata_IsActive",
                schema: "Genp",
                table: "SubscriptionMetadata",
                column: "IsActive",
                filter: "\"IsActive\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_SubscriptionMetadata_IsUnique",
                schema: "Genp",
                table: "SubscriptionMetadata",
                column: "IsUnique");

            migrationBuilder.CreateIndex(
                name: "IX_SubscriptionMetadata_MetadataId",
                schema: "Genp",
                table: "SubscriptionMetadata",
                column: "MetadataId");

            migrationBuilder.CreateIndex(
                name: "IX_SubscriptionMetadata_SubscriptionId",
                schema: "Genp",
                table: "SubscriptionMetadata",
                column: "SubscriptionId");

            migrationBuilder.CreateIndex(
                name: "IX_SubscriptionMetadata_SubscriptionId_MetadataId",
                schema: "Genp",
                table: "SubscriptionMetadata",
                columns: new[] { "SubscriptionId", "MetadataId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Subscriptions_EndDate",
                schema: "Genp",
                table: "Subscriptions",
                column: "EndDate");

            migrationBuilder.CreateIndex(
                name: "IX_Subscriptions_IsActive",
                schema: "Genp",
                table: "Subscriptions",
                column: "IsActive",
                filter: "\"IsActive\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_Subscriptions_ProductId",
                schema: "Genp",
                table: "Subscriptions",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_Subscriptions_StartDate",
                schema: "Genp",
                table: "Subscriptions",
                column: "StartDate");

            migrationBuilder.CreateIndex(
                name: "IX_Subscriptions_Status",
                schema: "Genp",
                table: "Subscriptions",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Subscriptions_SubscriptionType",
                schema: "Genp",
                table: "Subscriptions",
                column: "SubscriptionType");

            migrationBuilder.CreateIndex(
                name: "IX_SubscriptionValues_CreatedAt",
                schema: "Genp",
                table: "SubscriptionValues",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_SubscriptionValues_RefId",
                schema: "Genp",
                table: "SubscriptionValues",
                column: "RefId");

            migrationBuilder.CreateIndex(
                name: "IX_SubscriptionValues_SubscriptionMetadataId",
                schema: "Genp",
                table: "SubscriptionValues",
                column: "SubscriptionMetadataId");

            migrationBuilder.CreateIndex(
                name: "IX_SyncHistory_CompletedAt",
                schema: "Genp",
                table: "SyncHistory",
                column: "CompletedAt");

            migrationBuilder.CreateIndex(
                name: "IX_SyncHistory_Direction",
                schema: "Genp",
                table: "SyncHistory",
                column: "Direction");

            migrationBuilder.CreateIndex(
                name: "IX_SyncHistory_IntegrationId",
                schema: "Genp",
                table: "SyncHistory",
                column: "IntegrationId");

            migrationBuilder.CreateIndex(
                name: "IX_SyncHistory_ObjectId",
                schema: "Genp",
                table: "SyncHistory",
                column: "ObjectId");

            migrationBuilder.CreateIndex(
                name: "IX_SyncHistory_StartedAt",
                schema: "Genp",
                table: "SyncHistory",
                column: "StartedAt");

            migrationBuilder.CreateIndex(
                name: "IX_SyncHistory_Status",
                schema: "Genp",
                table: "SyncHistory",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_SyncHistory_SyncType",
                schema: "Genp",
                table: "SyncHistory",
                column: "SyncType");

            migrationBuilder.CreateIndex(
                name: "IX_TenantInfoMetadata_IsActive",
                schema: "Genp",
                table: "TenantInfoMetadata",
                column: "IsActive",
                filter: "\"IsActive\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_TenantInfoMetadata_IsUnique",
                schema: "Genp",
                table: "TenantInfoMetadata",
                column: "IsUnique");

            migrationBuilder.CreateIndex(
                name: "IX_TenantInfoMetadata_MetadataId",
                schema: "Genp",
                table: "TenantInfoMetadata",
                column: "MetadataId");

            migrationBuilder.CreateIndex(
                name: "IX_TenantInfoValues_CreatedAt",
                schema: "Genp",
                table: "TenantInfoValues",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_TenantInfoValues_RefId",
                schema: "Genp",
                table: "TenantInfoValues",
                column: "RefId");

            migrationBuilder.CreateIndex(
                name: "IX_TenantInfoValues_TenantInfoMetadataId",
                schema: "Genp",
                table: "TenantInfoValues",
                column: "TenantInfoMetadataId");

            migrationBuilder.CreateIndex(
                name: "IX_UserClaims_UserId",
                schema: "Genp",
                table: "UserClaims",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_UserLogins_LoginProvider_ProviderKey_TenantId",
                schema: "Genp",
                table: "UserLogins",
                columns: new[] { "LoginProvider", "ProviderKey", "TenantId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_UserLogins_UserId",
                schema: "Genp",
                table: "UserLogins",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_UserMetadata_IsActive",
                schema: "Genp",
                table: "UserMetadata",
                column: "IsActive",
                filter: "\"IsActive\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_UserMetadata_IsUnique",
                schema: "Genp",
                table: "UserMetadata",
                column: "IsUnique");

            migrationBuilder.CreateIndex(
                name: "IX_UserMetadata_MetadataId",
                schema: "Genp",
                table: "UserMetadata",
                column: "MetadataId");

            migrationBuilder.CreateIndex(
                name: "IX_UserMetadata_UserId",
                schema: "Genp",
                table: "UserMetadata",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_UserMetadata_UserId_MetadataId",
                schema: "Genp",
                table: "UserMetadata",
                columns: new[] { "UserId", "MetadataId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_UserRoles_RoleId",
                schema: "Genp",
                table: "UserRoles",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_UserRoles_UserId",
                schema: "Genp",
                table: "UserRoles",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "EmailIndex",
                schema: "Genp",
                table: "Users",
                column: "NormalizedEmail");

            migrationBuilder.CreateIndex(
                name: "IX_Users_ExternalUserId",
                schema: "Genp",
                table: "Users",
                column: "ExternalUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Users_IsActive",
                schema: "Genp",
                table: "Users",
                column: "IsActive",
                filter: "\"IsActive\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_Users_LastLoginAt",
                schema: "Genp",
                table: "Users",
                column: "LastLoginAt");

            migrationBuilder.CreateIndex(
                name: "UserNameIndex",
                schema: "Genp",
                table: "Users",
                columns: new[] { "NormalizedUserName", "TenantId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_UserValues_CreatedAt",
                schema: "Genp",
                table: "UserValues",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_UserValues_RefId",
                schema: "Genp",
                table: "UserValues",
                column: "RefId");

            migrationBuilder.CreateIndex(
                name: "IX_UserValues_UserMetadataId",
                schema: "Genp",
                table: "UserValues",
                column: "UserMetadataId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AspNetRoleClaims");

            migrationBuilder.DropTable(
                name: "ConflictResolution",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "FeatureValues",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "FieldMapping",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "IntegrationConfiguration",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "ObjectValues",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "ProductValues",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "RoleValues",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "SubscriptionValues",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "SyncHistory",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "TenantInfoValues",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "UserClaims",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "UserLogins",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "UserRoles",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "UserTokens",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "UserValues",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "FeatureMetadata",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "IntegrationApi",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "ObjectMetadata",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "ProductMetadata",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "RoleMetadata",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "SubscriptionMetadata",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "Integrations",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "TenantInfoMetadata",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "UserMetadata",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "Objects",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "Roles",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "Subscriptions",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "Metadata",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "Users",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "Features",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "DataTypes",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "Products",
                schema: "Genp");
        }
    }
}
