using Application.Integrations.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Integrations.Queries;

/// <summary>
/// Get integrations query
/// </summary>
public class GetIntegrationsQuery : IRequest<PaginatedResult<IntegrationDto>>
{
    /// <summary>
    /// Page number
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Search term
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Filter by active status
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Filter by product ID
    /// </summary>
    public Guid? ProductId { get; set; }

    /// <summary>
    /// Filter by authentication type
    /// </summary>
    public string? AuthType { get; set; }
}
