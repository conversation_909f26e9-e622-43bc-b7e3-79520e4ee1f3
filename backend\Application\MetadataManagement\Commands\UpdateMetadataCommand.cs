using Application.MetadataManagement.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.MetadataManagement.Commands;

/// <summary>
/// Update Metadata command
/// </summary>
public class UpdateMetadataCommand : IRequest<Result<MetadataDto>>
{
    /// <summary>
    /// Metadata ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Metadata key
    /// </summary>
    public string MetadataKey { get; set; } = string.Empty;

    /// <summary>
    /// Data type ID
    /// </summary>
    public Guid DataTypeId { get; set; }

    /// <summary>
    /// Custom validation pattern
    /// </summary>
    public string? CustomValidationPattern { get; set; }

    /// <summary>
    /// Custom minimum length
    /// </summary>
    public int? CustomMinLength { get; set; }

    /// <summary>
    /// Custom maximum length
    /// </summary>
    public int? CustomMaxLength { get; set; }

    /// <summary>
    /// Custom minimum value
    /// </summary>
    public decimal? CustomMinValue { get; set; }

    /// <summary>
    /// Custom maximum value
    /// </summary>
    public decimal? CustomMaxValue { get; set; }

    /// <summary>
    /// Custom is required
    /// </summary>
    public bool? CustomIsRequired { get; set; }

    /// <summary>
    /// Custom placeholder
    /// </summary>
    public string? CustomPlaceholder { get; set; }

    /// <summary>
    /// Custom options
    /// </summary>
    public string? CustomOptions { get; set; }

    /// <summary>
    /// Custom max selections
    /// </summary>
    public int? CustomMaxSelections { get; set; }

    /// <summary>
    /// Custom allowed file types
    /// </summary>
    public string? CustomAllowedFileTypes { get; set; }

    /// <summary>
    /// Custom max file size
    /// </summary>
    public long? CustomMaxFileSize { get; set; }

    /// <summary>
    /// Custom error message
    /// </summary>
    public string? CustomErrorMessage { get; set; }

    /// <summary>
    /// Display label
    /// </summary>
    public string? DisplayLabel { get; set; }

    /// <summary>
    /// Help text
    /// </summary>
    public string? HelpText { get; set; }

    /// <summary>
    /// Field order
    /// </summary>
    public int? FieldOrder { get; set; }

    /// <summary>
    /// Is visible
    /// </summary>
    public bool IsVisible { get; set; } = true;

    /// <summary>
    /// Is readonly
    /// </summary>
    public bool IsReadonly { get; set; } = false;
}
