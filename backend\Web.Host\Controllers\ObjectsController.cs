using Application.Objects.Commands;
using Application.Objects.DTOs;
using Application.Objects.Queries;
using Application.ObjectMetadataManagement.DTOs;
using Application.ObjectMetadataManagement.Queries;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;

namespace Web.Host.Controllers;

/// <summary>
/// Objects controller
/// </summary>
[Route("api/[controller]")]
public class ObjectsController : BaseApiController
{
    /// <summary>
    /// Get all objects with pagination and filtering
    /// </summary>
    [HttpGet]
    [TenantIdHeader]
    public async Task<ActionResult<PaginatedResult<ObjectDto>>> GetObjects([FromQuery] GetObjectsQuery query)
    {
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get all objects with metadata information
    /// </summary>
    [HttpGet("with-metadata")]
    [TenantIdHeader]
    public async Task<ActionResult<PaginatedResult<ObjectWithMetadataDto>>> GetObjectsWithMetadata([FromQuery] GetObjectsWithMetadataQuery query)
    {
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get object by ID
    /// </summary>
    [HttpGet("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ObjectDto>>> GetObjectById(Guid id)
    {
        var query = new GetObjectByIdQuery { Id = id };
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get object by ID with metadata information
    /// </summary>
    [HttpGet("{id}/with-metadata")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ObjectWithMetadataDto>>> GetObjectByIdWithMetadata(Guid id)
    {
        var query = new GetObjectByIdWithMetadataQuery(id);
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get metadata for a specific object
    /// </summary>
    [HttpGet("{id}/metadata")]
    [TenantIdHeader]
    public async Task<ActionResult<PaginatedResult<ObjectMetadataDto>>> GetObjectMetadata(
        Guid id,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 50,
        [FromQuery] bool? isActive = true)
    {
        var query = new GetObjectMetadataQuery
        {
            ObjectId = id,
            PageNumber = pageNumber,
            PageSize = pageSize,
            IsActive = isActive
        };
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Create a new object
    /// </summary>
    [HttpPost]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ObjectDto>>> CreateObject(CreateObjectCommand command)
    {
        return Ok(await Mediator.Send(command));
    }
}
