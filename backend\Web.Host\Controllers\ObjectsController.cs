using Application.Objects.Commands;
using Application.Objects.DTOs;
using Application.Objects.Queries;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;
using Swashbuckle.AspNetCore.Annotations;

namespace Web.Host.Controllers;

/// <summary>
/// Objects controller
/// </summary>
[Route("api/[controller]")]
public class ObjectsController : BaseApiController
{
    /// <summary>
    /// Get all objects with pagination
    /// </summary>
    [HttpGet]
    [TenantIdHeader]
    [SwaggerOperation(OperationId = "GetObjects")]
    public async Task<ActionResult<PaginatedResult<ObjectDto>>> GetObjectsAsync([FromQuery] GetObjectsQuery query)
    {
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get object by ID
    /// </summary>
    [HttpGet("{id}")]
    [TenantIdHeader]
    [SwaggerOperation(OperationId = "GetObjectById")]
    public async Task<ActionResult<Result<ObjectDto>>> GetObjectByIdAsync(Guid id)
    {
        return Ok(await Mediator.Send(new GetObjectByIdQuery(id)));
    }

    /// <summary>
    /// Create a new object
    /// </summary>
    [HttpPost]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ObjectDto>>> CreateObject(CreateObjectCommand command)
    {
        return Ok(await Mediator.Send(command));
    }
}
