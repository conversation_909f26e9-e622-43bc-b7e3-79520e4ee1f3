using Application.Objects.Commands;
using Application.Objects.DTOs;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;

namespace Web.Host.Controllers;

/// <summary>
/// Objects controller
/// </summary>
[Route("api/[controller]")]
public class ObjectsController : BaseApiController
{
    /// <summary>
    /// Create a new object
    /// </summary>
    [HttpPost]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ObjectDto>>> CreateObject(CreateObjectCommand command)
    {
        return Ok(await Mediator.Send(command));
    }
}
