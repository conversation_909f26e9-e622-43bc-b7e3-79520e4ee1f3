using Ardalis.Specification;
using Domain.Entities;

namespace Application.Integrations.Specifications;

/// <summary>
/// Specification to get integration by name and product ID
/// </summary>
public class IntegrationByNameAndProductSpec : Specification<Integration>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public IntegrationByNameAndProductSpec(string name, Guid productId)
    {
        Query.Where(i => i.Name == name && i.ProductId == productId);
    }
}
