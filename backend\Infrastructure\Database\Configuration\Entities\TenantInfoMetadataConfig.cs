using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for TenantInfoMetadata entity
/// </summary>
public class TenantInfoMetadataConfig : IEntityTypeConfiguration<TenantInfoMetadata>
{
    public void Configure(EntityTypeBuilder<TenantInfoMetadata> builder)
    {
        builder.ToTable("TenantInfoMetadata", "Genp");

        // Properties
        builder.Property(e => e.MetadataId)
            .IsRequired();

        builder.Property(e => e.IsUnique)
            .HasDefaultValue(false);

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        // Indexes
        builder.HasIndex(e => e.MetadataId)
            .HasDatabaseName("IX_TenantInfoMetadata_MetadataId");

        builder.HasIndex(e => e.IsUnique)
            .HasDatabaseName("IX_TenantInfoMetadata_IsUnique");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_TenantInfoMetadata_IsActive")
            .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

        // Multi-tenancy
        builder.IsMultiTenant();

        // Relationships
        builder.HasOne(e => e.Metadata)
            .WithMany(e => e.TenantInfoMetadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.TenantInfoValues)
            .WithOne(e => e.TenantInfoMetadata)
            .HasForeignKey(e => e.TenantInfoMetadataId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
