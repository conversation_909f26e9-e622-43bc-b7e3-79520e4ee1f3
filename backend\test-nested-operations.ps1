# Test script for nested features and objects
$baseUrl = "http://localhost:5018"
$headers = @{
    "Content-Type" = "application/json"
    "tenant" = "black"
    "Accept" = "*/*"
}

Write-Host "Testing Nested Features and Objects..." -ForegroundColor Green

# Test with your original JSON data
$nestedData = @{
    operationType = 5  # BulkUpsert
    includeFeatures = $true
    includeObjects = $true
    includeMetadata = $true
    productData = @(
        @{
            name = "Apartment Inventory System"
            description = "Complete apartment management"
            version = "1.0.0"
            isActive = $true
            features = @(
                @{
                    name = "Building Management"
                    description = "Manage building information"
                    isDefault = $true
                    isActive = $true
                    objects = @(
                        @{
                            name = "Tower"
                            description = "Building tower"
                            isActive = $true
                        },
                        @{
                            name = "Floor"
                            description = "Building floor"
                            isActive = $true
                        },
                        @{
                            name = "Unit"
                            description = "Apartment unit"
                            isActive = $true
                        }
                    )
                }
            )
            metadata = @(
                @{
                    metadataId = "meta-001"
                    isUnique = $true
                    isActive = $true
                }
            )
        }
    )
} | ConvertTo-Json -Depth 5

Write-Host "Sending request with nested features and objects..." -ForegroundColor Yellow
Write-Host "JSON Data:" -ForegroundColor Cyan
Write-Host $nestedData -ForegroundColor White

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/dynamicoperations/products" -Method POST -Headers $headers -Body $nestedData
    Write-Host "✅ Nested operation successful!" -ForegroundColor Green
    Write-Host "Response:" -ForegroundColor Cyan
    Write-Host ($response | ConvertTo-Json -Depth 3) -ForegroundColor White
    
    Write-Host "`nSummary:" -ForegroundColor Yellow
    Write-Host "- Success: $($response.Success)" -ForegroundColor $(if($response.Success) { "Green" } else { "Red" })
    Write-Host "- Processed Count: $($response.ProcessedCount)" -ForegroundColor Cyan
    Write-Host "- Inserted Count: $($response.InsertedCount)" -ForegroundColor Cyan
    Write-Host "- Created IDs: $($response.CreatedIds.Count) entities" -ForegroundColor Cyan
    Write-Host "- Execution Time: $($response.ExecutionTimeMs)ms" -ForegroundColor Cyan
    
    if ($response.CreatedIds.Count -gt 1) {
        Write-Host "🎉 Multiple entities created - Features and Objects were processed!" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Only one entity created - Features and Objects may not have been processed" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "❌ Nested operation failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorBody = $reader.ReadToEnd()
        Write-Host "Error details: $errorBody" -ForegroundColor Red
    }
}

Write-Host "`n🔍 Check the application logs for detailed processing information" -ForegroundColor Yellow
