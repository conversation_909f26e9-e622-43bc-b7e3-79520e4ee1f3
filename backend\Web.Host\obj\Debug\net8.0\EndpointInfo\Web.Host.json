{"openapi": "3.0.4", "info": {"title": "API Management - Development", "description": "A comprehensive system API (Development Environment)", "contact": {"name": "<PERSON><PERSON>", "url": "https://app.com/support", "email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}, "version": "v1"}, "paths": {"/api/comprehensive-entity": {"get": {"tags": ["ComprehensiveEntityData"], "parameters": [{"name": "productId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "featureId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "objectId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "isActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "onlyVisibleMetadata", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "onlyActiveMetadata", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 50}}, {"name": "orderBy", "in": "query", "schema": {"type": "string"}}, {"name": "orderDirection", "in": "query", "schema": {"type": "string", "default": "asc"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}}}}}}, "/api/comprehensive-entity/products/{productId}": {"get": {"tags": ["ComprehensiveEntityData"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "onlyVisibleMetadata", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "onlyActiveMetadata", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1000}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}}}}}}, "/api/comprehensive-entity/features/{featureId}": {"get": {"tags": ["ComprehensiveEntityData"], "parameters": [{"name": "featureId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "onlyVisibleMetadata", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "onlyActiveMetadata", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1000}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}}}}}}, "/api/comprehensive-entity/objects/{objectId}": {"get": {"tags": ["ComprehensiveEntityData"], "parameters": [{"name": "objectId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "onlyVisibleMetadata", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "onlyActiveMetadata", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1000}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}}}}}}, "/api/comprehensive-entity/search": {"get": {"tags": ["ComprehensiveEntityData"], "parameters": [{"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "isActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "onlyVisibleMetadata", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "onlyActiveMetadata", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 50}}, {"name": "orderBy", "in": "query", "schema": {"type": "string"}}, {"name": "orderDirection", "in": "query", "schema": {"type": "string", "default": "asc"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}}}}}}, "/api/datatypes": {"get": {"tags": ["DataTypes"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "Category", "in": "query", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "OrderBy", "in": "query", "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DataTypeDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DataTypeDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataTypeDtoPaginatedResult"}}}}}}, "post": {"tags": ["DataTypes"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDataTypeCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateDataTypeCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateDataTypeCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DataTypeDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DataTypeDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataTypeDtoResult"}}}}}}}, "/api/datatypes/{id}": {"get": {"tags": ["DataTypes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DataTypeDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DataTypeDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataTypeDtoResult"}}}}}}, "put": {"tags": ["DataTypes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDataTypeCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateDataTypeCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateDataTypeCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DataTypeDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DataTypeDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataTypeDtoResult"}}}}}}, "delete": {"tags": ["DataTypes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/dynamicoperations/execute": {"post": {"tags": ["DynamicOperations"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DynamicOperationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DynamicOperationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DynamicOperationRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/dynamicoperations/bulk-execute": {"post": {"tags": ["DynamicOperations"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkDynamicOperationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkDynamicOperationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BulkDynamicOperationRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/dynamicoperations/products": {"post": {"tags": ["DynamicOperations"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DynamicProductOperationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DynamicProductOperationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DynamicProductOperationRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/dynamicoperations/features": {"post": {"tags": ["DynamicOperations"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DynamicFeatureOperationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DynamicFeatureOperationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DynamicFeatureOperationRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/dynamicoperations/objects": {"post": {"tags": ["DynamicOperations"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DynamicObjectOperationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DynamicObjectOperationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DynamicObjectOperationRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/dynamicoperations/values": {"post": {"tags": ["DynamicOperations"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DynamicValueOperationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DynamicValueOperationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DynamicValueOperationRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/dynamicoperations/objects/create-with-metadata": {"post": {"tags": ["DynamicOperations"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateObjectWithMetadataRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateObjectWithMetadataRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateObjectWithMetadataRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/dynamicoperations/supported-entities": {"get": {"tags": ["DynamicOperations"], "responses": {"200": {"description": "OK"}}}}, "/api/entitysetup": {"get": {"tags": ["EntitySetup"], "parameters": [{"name": "ProductId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "FeatureId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "ObjectId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "OnlyVisibleMetadata", "in": "query", "schema": {"type": "boolean"}}, {"name": "OnlyActiveMetadata", "in": "query", "schema": {"type": "boolean"}}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "OrderBy", "in": "query", "schema": {"type": "string"}}, {"name": "OrderDirection", "in": "query", "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/entitysetup/product/{productId}": {"get": {"tags": ["EntitySetup"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "onlyVisibleMetadata", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "onlyActiveMetadata", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/entitysetup/feature/{featureId}": {"get": {"tags": ["EntitySetup"], "parameters": [{"name": "featureId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "onlyVisibleMetadata", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "onlyActiveMetadata", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/entitysetup/object/{objectId}": {"get": {"tags": ["EntitySetup"], "parameters": [{"name": "objectId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "onlyVisibleMetadata", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "onlyActiveMetadata", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDtoResult"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/features": {"get": {"tags": ["Features"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "ProductId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FeatureDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FeatureDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FeatureDtoPaginatedResult"}}}}}}, "post": {"tags": ["Features"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFeatureCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateFeatureCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateFeatureCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FeatureDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FeatureDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FeatureDtoResult"}}}}}}}, "/api/features/{id}": {"get": {"tags": ["Features"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FeatureDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FeatureDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FeatureDtoResult"}}}}}}, "put": {"tags": ["Features"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateFeatureCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateFeatureCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateFeatureCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FeatureDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FeatureDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FeatureDtoResult"}}}}}}, "delete": {"tags": ["Features"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/fieldmappings": {"get": {"tags": ["FieldMappings"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "ApiName", "in": "query", "schema": {"type": "string"}}, {"name": "SourceType", "in": "query", "schema": {"type": "string"}}, {"name": "ObjectMetadataId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "UserId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "RoleId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "TargetObjectName", "in": "query", "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoPaginatedResult"}}}}}}, "post": {"tags": ["FieldMappings"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFieldMappingCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateFieldMappingCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateFieldMappingCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoResult"}}}}}}}, "/api/fieldmappings/{id}": {"get": {"tags": ["FieldMappings"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoResult"}}}}}}, "put": {"tags": ["FieldMappings"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateFieldMappingCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateFieldMappingCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateFieldMappingCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoResult"}}}}}}, "delete": {"tags": ["FieldMappings"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/fieldmappings/bulk": {"post": {"tags": ["FieldMappings"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFieldMappingsCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateFieldMappingsCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateFieldMappingsCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewFieldMappingDtoListResult"}}}}}}}, "/api/integrationapis": {"get": {"tags": ["IntegrationApis"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "ProductId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoPaginatedResult"}}}}}}, "post": {"tags": ["IntegrationApis"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationApiCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationApiCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationApiCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoResult"}}}}}}}, "/api/integrationapis/{id}": {"get": {"tags": ["IntegrationApis"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoResult"}}}}}}, "put": {"tags": ["IntegrationApis"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateIntegrationApiCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateIntegrationApiCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateIntegrationApiCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoResult"}}}}}}, "delete": {"tags": ["IntegrationApis"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/integrationapis/bulk": {"post": {"tags": ["IntegrationApis"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationApisCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationApisCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationApisCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationApiDtoListResult"}}}}}}}, "/api/integrationconfigurations": {"get": {"tags": ["IntegrationConfigurations"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "IntegrationId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "IntegrationApiId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "ObjectId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "Direction", "in": "query", "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoPaginatedResult"}}}}}}, "post": {"tags": ["IntegrationConfigurations"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationConfigurationCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationConfigurationCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationConfigurationCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoResult"}}}}}}}, "/api/integrationconfigurations/{id}": {"get": {"tags": ["IntegrationConfigurations"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoResult"}}}}}}, "put": {"tags": ["IntegrationConfigurations"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateIntegrationConfigurationCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateIntegrationConfigurationCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateIntegrationConfigurationCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoResult"}}}}}}, "delete": {"tags": ["IntegrationConfigurations"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/integrationconfigurations/bulk": {"post": {"tags": ["IntegrationConfigurations"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationConfigurationsCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationConfigurationsCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationConfigurationsCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDtoListResult"}}}}}}}, "/api/integrations": {"get": {"tags": ["Integrations"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "ProductId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "AuthType", "in": "query", "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/IntegrationDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IntegrationDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IntegrationDtoPaginatedResult"}}}}}}, "post": {"tags": ["Integrations"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/IntegrationDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IntegrationDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IntegrationDtoResult"}}}}}}}, "/api/integrations/{id}": {"get": {"tags": ["Integrations"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/IntegrationDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IntegrationDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IntegrationDtoResult"}}}}}}, "put": {"tags": ["Integrations"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateIntegrationCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateIntegrationCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateIntegrationCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/IntegrationDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IntegrationDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IntegrationDtoResult"}}}}}}, "delete": {"tags": ["Integrations"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/integrations/bulk": {"post": {"tags": ["Integrations"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationsCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationsCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateIntegrationsCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/IntegrationDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IntegrationDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IntegrationDtoListResult"}}}}}}}, "/api/metadata": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "DataTypeId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "IsVisible", "in": "query", "schema": {"type": "boolean"}}, {"name": "OrderBy", "in": "query", "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MetadataDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MetadataDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MetadataDtoPaginatedResult"}}}}}}, "post": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMetadataCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateMetadataCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateMetadataCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MetadataDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MetadataDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MetadataDtoResult"}}}}}}}, "/api/metadata/{id}": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MetadataDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MetadataDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MetadataDtoResult"}}}}}}, "put": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMetadataCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateMetadataCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateMetadataCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MetadataDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MetadataDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MetadataDtoResult"}}}}}}, "delete": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/nestedfeaturecreation": {"post": {"tags": ["NestedFeatureCreation"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFeatureWithNestedDataRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateFeatureWithNestedDataRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateFeatureWithNestedDataRequestDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CreateFeatureWithNestedDataResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateFeatureWithNestedDataResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateFeatureWithNestedDataResponseDtoResult"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CreateFeatureWithNestedDataResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateFeatureWithNestedDataResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateFeatureWithNestedDataResponseDtoResult"}}}}, "500": {"description": "Internal Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CreateFeatureWithNestedDataResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateFeatureWithNestedDataResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateFeatureWithNestedDataResponseDtoResult"}}}}}}}, "/api/nestedfeaturecreation/upsert-object": {"post": {"tags": ["NestedFeatureCreation"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpsertObjectWithMetadataRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpsertObjectWithMetadataRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpsertObjectWithMetadataRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/objects": {"post": {"tags": ["Objects"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateObjectCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateObjectCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateObjectCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectDtoResult"}}}}}}}, "/api/objectvalues/by-feature/{featureId}": {"get": {"tags": ["ObjectValues"], "parameters": [{"name": "featureId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "objectId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "onlyVisibleFields", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "orderBy", "in": "query", "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectValueResponseDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectValueResponseDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectValueResponseDtoListResult"}}}}}}}, "/api/objectvalues": {"get": {"tags": ["ObjectValues"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "FeatureId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "ObjectId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "MetadataId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "RefId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON>lude<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean"}}, {"name": "OnlyVisibleFields", "in": "query", "schema": {"type": "boolean"}}, {"name": "Only<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean"}}, {"name": "OrderBy", "in": "query", "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectValueResponseDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectValueResponseDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectValueResponseDtoPaginatedResult"}}}}}}, "post": {"tags": ["ObjectValues"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateObjectValueCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateObjectValueCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateObjectValueCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectValueDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectValueDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectValueDtoResult"}}}}}}}, "/api/objectvalues/{id}": {"get": {"tags": ["ObjectValues"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectValueResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectValueResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectValueResponseDtoResult"}}}}}}}, "/api/objectvalues-pivot/{refId}": {"get": {"tags": ["ObjectValuesPivot"], "parameters": [{"name": "refId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "includeInactive", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "onlyVisible", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectValuesResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectValuesResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectValuesResponseDtoResult"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/objectvalues-pivot/object/{objectId}": {"get": {"tags": ["ObjectValuesPivot"], "parameters": [{"name": "objectId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "onlyActive", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 50}}, {"name": "orderBy", "in": "query", "schema": {"type": "string"}}, {"name": "orderDirection", "in": "query", "schema": {"type": "string", "default": "desc"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectValuesRefIdSummaryDtoPaginatedResultResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectValuesRefIdSummaryDtoPaginatedResultResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectValuesRefIdSummaryDtoPaginatedResultResult"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/objectvalues-pivot": {"post": {"tags": ["ObjectValuesPivot"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpsertObjectValuesRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpsertObjectValuesRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpsertObjectValuesRequestDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UpsertObjectValuesResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpsertObjectValuesResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpsertObjectValuesResponseDtoResult"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/objectvalues-pivot/{refId}/object/{objectId}": {"put": {"tags": ["ObjectValuesPivot"], "parameters": [{"name": "refId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "objectId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "autoCreateMetadata", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "strictValidation", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "application/*+json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UpsertObjectValuesResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpsertObjectValuesResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpsertObjectValuesResponseDtoResult"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/products": {"get": {"tags": ["Products"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDtoPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDtoPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDtoPaginatedResult"}}}}}}, "post": {"tags": ["Products"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDtoResult"}}}}}}}, "/api/products/{id}": {"get": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDtoResult"}}}}}}, "put": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateProductCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateProductCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDtoResult"}}}}}}}, "/api/products/with-subscription": {"post": {"tags": ["Products"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductWithSubscriptionCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductWithSubscriptionCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductWithSubscriptionCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductWithSubscriptionDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductWithSubscriptionDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductWithSubscriptionDtoResult"}}}}}}}, "/api/roles": {"get": {"tags": ["Roles"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RoleDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RoleDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleDtoListResult"}}}}}}, "post": {"tags": ["Roles"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRoleRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateRoleRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateRoleRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}, "put": {"tags": ["Roles"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateRoleRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/roles/{id}": {"get": {"tags": ["Roles"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RoleDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RoleDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleDtoResult"}}}}}}, "delete": {"tags": ["Roles"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/tenants": {"get": {"tags": ["Tenants"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TenantDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TenantDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantDtoListResult"}}}}}}, "post": {"tags": ["Tenants"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TenantDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}, "put": {"tags": ["Tenants"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TenantDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/tenants/{id}": {"get": {"tags": ["Tenants"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TenantWithProductDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TenantWithProductDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TenantWithProductDtoResult"}}}}}}, "delete": {"tags": ["Tenants"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/tenants/{id}/activate": {"post": {"tags": ["Tenants"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/tenants/{id}/deactivate": {"post": {"tags": ["Tenants"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/tokens": {"post": {"tags": ["Tokens"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TokenRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TokenRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TokenResponseResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TokenResponseResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TokenResponseResult"}}}}}}}, "/api/tokens/refresh": {"post": {"tags": ["Tokens"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TokenResponseResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TokenResponseResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TokenResponseResult"}}}}}}}, "/api/userdata/create-comprehensive": {"post": {"tags": ["UserData"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDataCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUserDataCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUserDataCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDataResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDataResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDataResponseDtoResult"}}}}}}}, "/api/users": {"get": {"tags": ["Users"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDetailsDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDetailsDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDetailsDtoListResult"}}}}}}, "post": {"tags": ["Users"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterUserRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegisterUserRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegisterUserRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/users/paginated": {"get": {"tags": ["Users"], "parameters": [{"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "searchString", "in": "query", "schema": {"type": "string"}}, {"name": "includeInactive", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDetailsDtoObjectPagedResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDetailsDtoObjectPagedResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDetailsDtoObjectPagedResponse"}}}}}}}, "/api/users/{id}": {"get": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDetailsDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDetailsDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDetailsDtoResult"}}}}}}, "put": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/users/{id}/change-password": {"put": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/users/forgot-password": {"post": {"tags": ["Users"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/users/reset-password": {"post": {"tags": ["Users"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/users/create": {"post": {"tags": ["Users"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUserCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUserCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDtoApiResponse"}}}}}}}, "/api/users/bulk-create": {"post": {"tags": ["Users"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkCreateUsersCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkCreateUsersCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BulkCreateUsersCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BulkCreateUsersResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BulkCreateUsersResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkCreateUsersResponseApiResponse"}}}}}}}, "/api/users/bulk-update-roles": {"post": {"tags": ["Users"], "parameters": [{"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateUserRolesCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateUserRolesCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BulkUpdateUserRolesCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BulkUpdateUserRolesResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateUserRolesResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateUserRolesResponseApiResponse"}}}}}}}, "/api/users/{userId}/roles": {"get": {"tags": ["Users"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserRoleDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserRoleDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserRoleDtoListApiResponse"}}}}}}, "put": {"tags": ["Users"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserRoleDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserRoleDto"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserRoleDto"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}}}}}}, "/api/users/{userId}/roles/assign": {"post": {"tags": ["Users"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}}}}}}, "/api/users/{userId}/roles/remove": {"post": {"tags": ["Users"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}}}}}}, "/api/users/roles/{roleName}/users": {"get": {"tags": ["Users"], "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "tenant", "in": "header", "description": "Input your tenant Id to access this API", "required": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDtoListApiResponse"}}}}}}}}, "components": {"schemas": {"BooleanResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "BulkCreateUsersCommand": {"type": "object", "properties": {"users": {"type": "array", "items": {"$ref": "#/components/schemas/CreateUserCommand"}, "nullable": true}, "continueOnError": {"type": "boolean"}, "validateBeforeCreate": {"type": "boolean"}, "productId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "BulkCreateUsersResponse": {"type": "object", "properties": {"totalRequested": {"type": "integer", "format": "int32"}, "successfullyCreated": {"type": "integer", "format": "int32"}, "failed": {"type": "integer", "format": "int32"}, "createdUsers": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}, "nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/BulkUserCreationError"}, "nullable": true}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BulkCreateUsersResponseApiResponse": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/BulkCreateUsersResponse"}}, "additionalProperties": false}, "BulkDynamicOperationRequest": {"type": "object", "properties": {"entityType": {"type": "string", "nullable": true}, "operationType": {"$ref": "#/components/schemas/DynamicOperationType"}, "tenantId": {"type": "string", "nullable": true}, "propertyValuesList": {"type": "array", "items": {"type": "object", "additionalProperties": {}}, "nullable": true}, "keyProperties": {"type": "array", "items": {"type": "string"}, "nullable": true}, "batchSize": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "BulkUpdateUserRolesCommand": {"type": "object", "properties": {"userRoleUpdates": {"type": "array", "items": {"$ref": "#/components/schemas/UserRoleUpdateRequest"}, "nullable": true}, "continueOnError": {"type": "boolean"}, "validateBeforeUpdate": {"type": "boolean"}, "productId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "BulkUpdateUserRolesResponse": {"type": "object", "properties": {"totalRequested": {"type": "integer", "format": "int32"}, "successfullyUpdated": {"type": "integer", "format": "int32"}, "failed": {"type": "integer", "format": "int32"}, "updatedUsers": {"type": "array", "items": {"$ref": "#/components/schemas/UserRoleUpdateResult"}, "nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/BulkUserRoleUpdateError"}, "nullable": true}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BulkUpdateUserRolesResponseApiResponse": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/BulkUpdateUserRolesResponse"}}, "additionalProperties": false}, "BulkUserCreationError": {"type": "object", "properties": {"userIndex": {"type": "integer", "format": "int32"}, "email": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}, "errorDetails": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BulkUserRoleUpdateError": {"type": "object", "properties": {"userIndex": {"type": "integer", "format": "int32"}, "userId": {"type": "string", "nullable": true}, "attemptedRoleIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "errorMessage": {"type": "string", "nullable": true}, "errorDetails": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ChangePasswordRequest": {"type": "object", "properties": {"currentPassword": {"type": "string", "nullable": true}, "newPassword": {"type": "string", "nullable": true}, "confirmNewPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ChildObjectCreationDto": {"required": ["name"], "type": "object", "properties": {"name": {"maxLength": 255, "minLength": 1, "type": "string"}, "description": {"maxLength": 1000, "type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "metaJson": {"nullable": true}, "metaValues": {"type": "array", "items": {"type": "object", "additionalProperties": {}}, "nullable": true}}, "additionalProperties": false}, "ChildObjectCreationResultDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "parentObjectId": {"type": "string", "format": "uuid"}, "metadataCreated": {"type": "integer", "format": "int32"}, "objectValuesCreated": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ComprehensiveEntityDataResponseDto": {"type": "object", "properties": {"products": {"type": "array", "items": {"$ref": "#/components/schemas/ProductComprehensiveDto"}, "nullable": true}}, "additionalProperties": false}, "ComprehensiveEntityDataResponseDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ComprehensiveEntityDataResponseDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "CreateDataTypeCommand": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "uiComponent": {"type": "string", "nullable": true}, "validationPattern": {"type": "string", "nullable": true}, "minLength": {"type": "integer", "format": "int32", "nullable": true}, "maxLength": {"type": "integer", "format": "int32", "nullable": true}, "minValue": {"type": "number", "format": "double", "nullable": true}, "maxValue": {"type": "number", "format": "double", "nullable": true}, "decimalPlaces": {"type": "integer", "format": "int32", "nullable": true}, "stepValue": {"type": "number", "format": "double", "nullable": true}, "isRequired": {"type": "boolean"}, "inputType": {"type": "string", "nullable": true}, "inputMask": {"type": "string", "nullable": true}, "placeholder": {"type": "string", "nullable": true}, "htmlAttributes": {"type": "string", "nullable": true}, "defaultOptions": {"type": "string", "nullable": true}, "allowsMultiple": {"type": "boolean"}, "allowsCustomOptions": {"type": "boolean"}, "maxSelections": {"type": "integer", "format": "int32", "nullable": true}, "allowedFileTypes": {"type": "string", "nullable": true}, "maxFileSizeBytes": {"type": "integer", "format": "int64", "nullable": true}, "requiredErrorMessage": {"type": "string", "nullable": true}, "patternErrorMessage": {"type": "string", "nullable": true}, "minLengthErrorMessage": {"type": "string", "nullable": true}, "maxLengthErrorMessage": {"type": "string", "nullable": true}, "minValueErrorMessage": {"type": "string", "nullable": true}, "maxValueErrorMessage": {"type": "string", "nullable": true}, "fileTypeErrorMessage": {"type": "string", "nullable": true}, "fileSizeErrorMessage": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateFeatureCommand": {"type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isDefault": {"type": "boolean"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateFeatureWithNestedDataRequestDto": {"required": ["feature"], "type": "object", "properties": {"feature": {"$ref": "#/components/schemas/FeatureCreationDto"}}, "additionalProperties": false}, "CreateFeatureWithNestedDataResponseDto": {"type": "object", "properties": {"feature": {"$ref": "#/components/schemas/FeatureCreationResultDto"}, "objects": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectCreationResultDto"}, "nullable": true}, "metadataCreated": {"type": "integer", "format": "int32"}, "objectValuesCreated": {"type": "integer", "format": "int32"}, "childObjectsCreated": {"type": "integer", "format": "int32"}, "childObjectValuesCreated": {"type": "integer", "format": "int32"}, "processingSummary": {"$ref": "#/components/schemas/ProcessingSummaryDto"}}, "additionalProperties": false}, "CreateFeatureWithNestedDataResponseDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/CreateFeatureWithNestedDataResponseDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "CreateFieldMappingCommand": {"type": "object", "properties": {"apiName": {"type": "string", "nullable": true}, "sourceField": {"type": "string", "nullable": true}, "sourceType": {"type": "string", "nullable": true}, "objectMetadataId": {"type": "string", "format": "uuid", "nullable": true}, "userId": {"type": "string", "format": "uuid", "nullable": true}, "roleId": {"type": "string", "format": "uuid", "nullable": true}, "targetObjectName": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateFieldMappingRequest": {"type": "object", "properties": {"apiName": {"type": "string", "nullable": true}, "sourceField": {"type": "string", "nullable": true}, "sourceType": {"type": "string", "nullable": true}, "objectMetadataId": {"type": "string", "format": "uuid", "nullable": true}, "userId": {"type": "string", "format": "uuid", "nullable": true}, "roleId": {"type": "string", "format": "uuid", "nullable": true}, "targetObjectName": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateFieldMappingsCommand": {"type": "object", "properties": {"fieldMappings": {"type": "array", "items": {"$ref": "#/components/schemas/CreateFieldMappingRequest"}, "nullable": true}}, "additionalProperties": false}, "CreateIntegrationApiCommand": {"type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "endpointUrl": {"type": "string", "nullable": true}, "schema": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateIntegrationApiDto": {"type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "endpointUrl": {"type": "string", "nullable": true}, "schema": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateIntegrationApisCommand": {"type": "object", "properties": {"integrationApis": {"type": "array", "items": {"$ref": "#/components/schemas/CreateIntegrationApiDto"}, "nullable": true}}, "additionalProperties": false}, "CreateIntegrationCommand": {"type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "authType": {"type": "string", "nullable": true}, "authConfig": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "syncFrequency": {"type": "string", "format": "date-span", "nullable": true}}, "additionalProperties": false}, "CreateIntegrationConfigurationCommand": {"type": "object", "properties": {"integrationId": {"type": "string", "format": "uuid"}, "integrationApiId": {"type": "string", "format": "uuid"}, "objectId": {"type": "string", "format": "uuid"}, "direction": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateIntegrationConfigurationRequest": {"type": "object", "properties": {"integrationId": {"type": "string", "format": "uuid"}, "integrationApiId": {"type": "string", "format": "uuid"}, "objectId": {"type": "string", "format": "uuid"}, "direction": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateIntegrationConfigurationsCommand": {"type": "object", "properties": {"configurations": {"type": "array", "items": {"$ref": "#/components/schemas/CreateIntegrationConfigurationRequest"}, "nullable": true}}, "additionalProperties": false}, "CreateIntegrationRequest": {"type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "authType": {"type": "string", "nullable": true}, "authConfig": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "syncFrequency": {"type": "string", "format": "date-span", "nullable": true}}, "additionalProperties": false}, "CreateIntegrationsCommand": {"type": "object", "properties": {"integrations": {"type": "array", "items": {"$ref": "#/components/schemas/CreateIntegrationRequest"}, "nullable": true}}, "additionalProperties": false}, "CreateMetadataCommand": {"type": "object", "properties": {"metadataKey": {"type": "string", "nullable": true}, "dataTypeId": {"type": "string", "format": "uuid"}, "customValidationPattern": {"type": "string", "nullable": true}, "customMinLength": {"type": "integer", "format": "int32", "nullable": true}, "customMaxLength": {"type": "integer", "format": "int32", "nullable": true}, "customMinValue": {"type": "number", "format": "double", "nullable": true}, "customMaxValue": {"type": "number", "format": "double", "nullable": true}, "customIsRequired": {"type": "boolean", "nullable": true}, "customPlaceholder": {"type": "string", "nullable": true}, "customOptions": {"type": "string", "nullable": true}, "customMaxSelections": {"type": "integer", "format": "int32", "nullable": true}, "customAllowedFileTypes": {"type": "string", "nullable": true}, "customMaxFileSize": {"type": "integer", "format": "int64", "nullable": true}, "customErrorMessage": {"type": "string", "nullable": true}, "displayLabel": {"type": "string", "nullable": true}, "helpText": {"type": "string", "nullable": true}, "fieldOrder": {"type": "integer", "format": "int32", "nullable": true}, "isVisible": {"type": "boolean"}, "isReadonly": {"type": "boolean"}}, "additionalProperties": false}, "CreateObjectCommand": {"type": "object", "properties": {"featureId": {"type": "string", "format": "uuid"}, "parentObjectId": {"type": "string", "format": "uuid", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateObjectValueCommand": {"type": "object", "properties": {"objectMetadataId": {"type": "string", "format": "uuid"}, "refId": {"type": "string", "format": "uuid", "nullable": true}, "parentObjectValueId": {"type": "string", "format": "uuid", "nullable": true}, "value": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateObjectWithMetadataRequest": {"type": "object", "properties": {"productId": {"type": "string", "format": "uuid"}, "featureId": {"type": "string", "format": "uuid"}, "objectName": {"type": "string", "nullable": true}, "metadata": {"type": "array", "items": {"type": "object", "additionalProperties": {}}, "nullable": true}, "values": {"type": "array", "items": {"type": "object", "additionalProperties": {}}, "nullable": true}}, "additionalProperties": false}, "CreateProductCommand": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "version": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "isUserImported": {"type": "boolean"}, "isRoleAssigned": {"type": "boolean"}, "apiKey": {"type": "string", "nullable": true}, "isOnboardCompleted": {"type": "boolean"}, "applicationUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateProductWithSubscriptionCommand": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "version": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "subscriptionType": {"type": "string", "nullable": true}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "autoRenew": {"type": "boolean"}, "pricingTier": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateRoleRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "productId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "isSystemRole": {"type": "boolean"}, "permissions": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateUserCommand": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "confirmPassword": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "imageUrl": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "isMFAEnabled": {"type": "boolean"}, "integrationSourceId": {"type": "string", "format": "uuid", "nullable": true}, "externalUserId": {"type": "string", "nullable": true}, "externalUserData": {"type": "string", "nullable": true}, "mustChangePassword": {"type": "boolean"}, "objectId": {"type": "string", "nullable": true}, "roles": {"type": "array", "items": {"type": "string"}, "nullable": true}, "roleIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}}, "additionalProperties": false}, "CreateUserDataCommand": {"type": "object", "properties": {"userId": {"type": "string", "format": "uuid"}, "userName": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "emailConfirmed": {"type": "boolean"}, "imageUrl": {"type": "string", "nullable": true}, "altPhoneNumber": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "altEmail": {"type": "string", "nullable": true}, "bloodGroup": {"type": "integer", "format": "int32", "nullable": true}, "gender": {"type": "integer", "format": "int32", "nullable": true}, "permanentAddress": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "profileCompletion": {"type": "integer", "format": "int32", "nullable": true}, "empNo": {"type": "string", "nullable": true}, "officeName": {"type": "string", "nullable": true}, "officeAddress": {"type": "string", "nullable": true}, "reportsTo": {"type": "string", "format": "uuid", "nullable": true}, "generalManager": {"type": "string", "format": "uuid", "nullable": true}, "department": {"$ref": "#/components/schemas/DepartmentDto"}, "designation": {"$ref": "#/components/schemas/DesignationDto"}, "description": {"type": "string", "nullable": true}, "documents": {"type": "string", "nullable": true}, "leadCount": {"type": "integer", "format": "int32", "nullable": true}, "userRoles": {"type": "array", "items": {"$ref": "#/components/schemas/UserDataRoleDto"}, "nullable": true}, "rolePermission": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isAutomationEnabled": {"type": "boolean", "nullable": true}, "timeZoneInfo": {"$ref": "#/components/schemas/TimeZoneInfoDto"}, "shouldShowTimeZone": {"type": "boolean", "nullable": true}, "licenseNo": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DataTypeDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "uiComponent": {"type": "string", "nullable": true}, "validationPattern": {"type": "string", "nullable": true}, "minLength": {"type": "integer", "format": "int32", "nullable": true}, "maxLength": {"type": "integer", "format": "int32", "nullable": true}, "minValue": {"type": "number", "format": "double", "nullable": true}, "maxValue": {"type": "number", "format": "double", "nullable": true}, "decimalPlaces": {"type": "integer", "format": "int32", "nullable": true}, "stepValue": {"type": "number", "format": "double", "nullable": true}, "isRequired": {"type": "boolean"}, "inputType": {"type": "string", "nullable": true}, "inputMask": {"type": "string", "nullable": true}, "placeholder": {"type": "string", "nullable": true}, "htmlAttributes": {"type": "string", "nullable": true}, "defaultOptions": {"type": "string", "nullable": true}, "allowsMultiple": {"type": "boolean"}, "allowsCustomOptions": {"type": "boolean"}, "maxSelections": {"type": "integer", "format": "int32", "nullable": true}, "allowedFileTypes": {"type": "string", "nullable": true}, "maxFileSizeBytes": {"type": "integer", "format": "int64", "nullable": true}, "requiredErrorMessage": {"type": "string", "nullable": true}, "patternErrorMessage": {"type": "string", "nullable": true}, "minLengthErrorMessage": {"type": "string", "nullable": true}, "maxLengthErrorMessage": {"type": "string", "nullable": true}, "minValueErrorMessage": {"type": "string", "nullable": true}, "maxValueErrorMessage": {"type": "string", "nullable": true}, "fileTypeErrorMessage": {"type": "string", "nullable": true}, "fileSizeErrorMessage": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid"}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "DataTypeDtoPaginatedResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/DataTypeDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}, "page": {"type": "integer", "format": "int32"}, "firstPage": {"type": "integer", "format": "int32"}, "lastPage": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalItems": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "DataTypeDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/DataTypeDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "DataTypeInfoDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "uiComponent": {"type": "string", "nullable": true}, "inputType": {"type": "string", "nullable": true}, "inputMask": {"type": "string", "nullable": true}, "placeholder": {"type": "string", "nullable": true}, "defaultOptions": {"type": "string", "nullable": true}, "validationPattern": {"type": "string", "nullable": true}, "minLength": {"type": "integer", "format": "int32", "nullable": true}, "maxLength": {"type": "integer", "format": "int32", "nullable": true}, "minValue": {"type": "number", "format": "double", "nullable": true}, "maxValue": {"type": "number", "format": "double", "nullable": true}, "decimalPlaces": {"type": "integer", "format": "int32", "nullable": true}, "stepValue": {"type": "number", "format": "double", "nullable": true}, "isRequired": {"type": "boolean", "nullable": true}, "allowsMultiple": {"type": "boolean", "nullable": true}, "allowsCustomOptions": {"type": "boolean", "nullable": true}, "htmlAttributes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DepartmentDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DesignationDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DynamicFeatureOperationRequest": {"type": "object", "properties": {"operationType": {"$ref": "#/components/schemas/DynamicOperationType"}, "featureData": {"type": "array", "items": {"type": "object", "additionalProperties": {}}, "nullable": true}, "includeObjects": {"type": "boolean"}, "includeMetadata": {"type": "boolean"}}, "additionalProperties": false}, "DynamicObjectOperationRequest": {"type": "object", "properties": {"operationType": {"$ref": "#/components/schemas/DynamicOperationType"}, "objectData": {"type": "array", "items": {"type": "object", "additionalProperties": {}}, "nullable": true}, "includeMetadata": {"type": "boolean"}, "includeValues": {"type": "boolean"}}, "additionalProperties": false}, "DynamicOperationRequest": {"type": "object", "properties": {"entityType": {"type": "string", "nullable": true}, "operationType": {"$ref": "#/components/schemas/DynamicOperationType"}, "tenantId": {"type": "string", "nullable": true}, "propertyValues": {"type": "object", "additionalProperties": {"nullable": true}, "nullable": true}, "keyProperties": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "DynamicOperationType": {"enum": [0, 1, 2, 3, 4, 5], "type": "integer", "format": "int32"}, "DynamicProductOperationRequest": {"type": "object", "properties": {"operationType": {"$ref": "#/components/schemas/DynamicOperationType"}, "productData": {"type": "array", "items": {"type": "object", "additionalProperties": {}}, "nullable": true}, "includeFeatures": {"type": "boolean"}, "includeObjects": {"type": "boolean"}, "includeMetadata": {"type": "boolean"}}, "additionalProperties": false}, "DynamicValueOperationRequest": {"type": "object", "properties": {"valueEntityType": {"type": "string", "nullable": true}, "operationType": {"$ref": "#/components/schemas/DynamicOperationType"}, "valueData": {"type": "array", "items": {"type": "object", "additionalProperties": {}}, "nullable": true}, "refId": {"type": "string", "format": "uuid", "nullable": true}, "validateMetadata": {"type": "boolean"}}, "additionalProperties": false}, "FeatureComprehensiveDto": {"type": "object", "properties": {"featureInfo": {"$ref": "#/components/schemas/FeatureInfoDto"}, "featureMetadata": {"type": "array", "items": {"$ref": "#/components/schemas/MetadataWithValuesDto"}, "nullable": true}, "objects": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectComprehensiveDto"}, "nullable": true}}, "additionalProperties": false}, "FeatureCreationDto": {"required": ["name", "productId"], "type": "object", "properties": {"featureId": {"type": "string", "format": "uuid", "nullable": true}, "productId": {"type": "string", "format": "uuid"}, "name": {"maxLength": 255, "minLength": 1, "type": "string"}, "description": {"maxLength": 1000, "type": "string", "nullable": true}, "isDefault": {"type": "boolean"}, "isActive": {"type": "boolean"}, "metaJson": {"nullable": true}, "values": {"type": "object", "additionalProperties": {}, "nullable": true}, "objects": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectCreationDto"}, "nullable": true}}, "additionalProperties": false}, "FeatureCreationResultDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "productId": {"type": "string", "format": "uuid"}, "isNewFeature": {"type": "boolean"}, "featureMetadataCreated": {"type": "integer", "format": "int32"}, "featureValuesCreated": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "FeatureDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isDefault": {"type": "boolean"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid"}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "FeatureDtoPaginatedResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/FeatureDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}, "page": {"type": "integer", "format": "int32"}, "firstPage": {"type": "integer", "format": "int32"}, "lastPage": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalItems": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "FeatureDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/FeatureDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "FeatureInfoDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "ForgotPasswordRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}}, "additionalProperties": false}, "HttpStatusCode": {"enum": [100, 101, 102, 103, 200, 201, 202, 203, 204, 205, 206, 207, 208, 226, 300, 301, 302, 303, 304, 305, 306, 307, 308, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 421, 422, 423, 424, 426, 428, 429, 431, 451, 500, 501, 502, 503, 504, 505, 506, 507, 508, 510, 511], "type": "integer", "format": "int32"}, "IntegrationDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "authType": {"type": "string", "nullable": true}, "authConfig": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "syncFrequency": {"type": "string", "format": "date-span", "nullable": true}, "lastSyncAt": {"type": "string", "format": "date-time", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}, "modifiedAt": {"type": "string", "format": "date-time"}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "IntegrationDtoListResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/IntegrationDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "IntegrationDtoPaginatedResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/IntegrationDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}, "page": {"type": "integer", "format": "int32"}, "firstPage": {"type": "integer", "format": "int32"}, "lastPage": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalItems": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "IntegrationDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/IntegrationDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "MetadataDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "metadataKey": {"type": "string", "nullable": true}, "dataTypeId": {"type": "string", "format": "uuid"}, "dataTypeName": {"type": "string", "nullable": true}, "customValidationPattern": {"type": "string", "nullable": true}, "customMinLength": {"type": "integer", "format": "int32", "nullable": true}, "customMaxLength": {"type": "integer", "format": "int32", "nullable": true}, "customMinValue": {"type": "number", "format": "double", "nullable": true}, "customMaxValue": {"type": "number", "format": "double", "nullable": true}, "customIsRequired": {"type": "boolean", "nullable": true}, "customPlaceholder": {"type": "string", "nullable": true}, "customOptions": {"type": "string", "nullable": true}, "customMaxSelections": {"type": "integer", "format": "int32", "nullable": true}, "customAllowedFileTypes": {"type": "string", "nullable": true}, "customMaxFileSize": {"type": "integer", "format": "int64", "nullable": true}, "customErrorMessage": {"type": "string", "nullable": true}, "displayLabel": {"type": "string", "nullable": true}, "helpText": {"type": "string", "nullable": true}, "fieldOrder": {"type": "integer", "format": "int32", "nullable": true}, "isVisible": {"type": "boolean"}, "isReadonly": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid"}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "MetadataDtoPaginatedResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/MetadataDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}, "page": {"type": "integer", "format": "int32"}, "firstPage": {"type": "integer", "format": "int32"}, "lastPage": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalItems": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "MetadataDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/MetadataDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "MetadataInfoDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "metadataKey": {"type": "string", "nullable": true}, "displayLabel": {"type": "string", "nullable": true}, "helpText": {"type": "string", "nullable": true}, "fieldOrder": {"type": "integer", "format": "int32", "nullable": true}, "isVisible": {"type": "boolean", "nullable": true}, "isReadonly": {"type": "boolean", "nullable": true}, "customPlaceholder": {"type": "string", "nullable": true}, "customOptions": {"type": "string", "nullable": true}, "customIsRequired": {"type": "boolean", "nullable": true}, "dataType": {"$ref": "#/components/schemas/DataTypeInfoDto"}, "metadataLink": {"$ref": "#/components/schemas/MetadataLinkInfoDto"}}, "additionalProperties": false}, "MetadataLinkInfoDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "isUnique": {"type": "boolean"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "MetadataWithValuesDto": {"type": "object", "properties": {"metadata": {"$ref": "#/components/schemas/MetadataInfoDto"}, "values": {"type": "array", "items": {"$ref": "#/components/schemas/ValueInfoDto"}, "nullable": true}}, "additionalProperties": false}, "ObjectComprehensiveDto": {"type": "object", "properties": {"objectInfo": {"$ref": "#/components/schemas/ObjectInfoDto"}, "parentObject": {"$ref": "#/components/schemas/ParentObjectInfoDto"}, "objectMetadata": {"type": "array", "items": {"$ref": "#/components/schemas/MetadataWithValuesDto"}, "nullable": true}, "childObjects": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectComprehensiveDto"}, "nullable": true}}, "additionalProperties": false}, "ObjectCreationDto": {"required": ["name"], "type": "object", "properties": {"name": {"maxLength": 255, "minLength": 1, "type": "string"}, "description": {"maxLength": 1000, "type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "metaJson": {"nullable": true}, "metaValues": {"type": "array", "items": {"type": "object", "additionalProperties": {}}, "nullable": true}, "childObjects": {"type": "array", "items": {"$ref": "#/components/schemas/ChildObjectCreationDto"}, "nullable": true}}, "additionalProperties": false}, "ObjectCreationResultDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "featureId": {"type": "string", "format": "uuid"}, "metadataCreated": {"type": "integer", "format": "int32"}, "objectValuesCreated": {"type": "integer", "format": "int32"}, "childObjects": {"type": "array", "items": {"$ref": "#/components/schemas/ChildObjectCreationResultDto"}, "nullable": true}}, "additionalProperties": false}, "ObjectDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "featureId": {"type": "string", "format": "uuid"}, "featureName": {"type": "string", "nullable": true}, "parentObjectId": {"type": "string", "format": "uuid", "nullable": true}, "parentObjectName": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "childObjectsCount": {"type": "integer", "format": "int32"}, "metadataCount": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid"}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "ObjectDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ObjectDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ObjectInfoDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "parentObjectId": {"type": "string", "format": "uuid", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "ObjectMetadataDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "objectId": {"type": "string", "format": "uuid"}, "objectName": {"type": "string", "nullable": true}, "metadataId": {"type": "string", "format": "uuid"}, "metadataKey": {"type": "string", "nullable": true}, "metadataDisplayLabel": {"type": "string", "nullable": true}, "isUnique": {"type": "boolean"}, "isActive": {"type": "boolean"}, "valuesCount": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid"}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "ObjectUpsertDto": {"required": ["name"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "featureId": {"type": "string", "format": "uuid", "nullable": true}, "parentObjectId": {"type": "string", "format": "uuid", "nullable": true}, "name": {"maxLength": 255, "minLength": 1, "type": "string"}, "description": {"maxLength": 1000, "type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "metaJson": {"nullable": true}, "metaValues": {"type": "array", "items": {"type": "object", "additionalProperties": {}}, "nullable": true}}, "additionalProperties": false}, "ObjectValueDetailDto": {"type": "object", "properties": {"value": {"type": "string", "nullable": true}, "metadataId": {"type": "string", "format": "uuid"}, "metadataKey": {"type": "string", "nullable": true}, "displayLabel": {"type": "string", "nullable": true}, "helpText": {"type": "string", "nullable": true}, "fieldOrder": {"type": "integer", "format": "int32", "nullable": true}, "isVisible": {"type": "boolean"}, "isReadonly": {"type": "boolean"}, "isRequired": {"type": "boolean"}, "isUnique": {"type": "boolean"}, "dataType": {"$ref": "#/components/schemas/DataTypeInfoDto"}, "objectValueId": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "ObjectValueDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "objectMetadataId": {"type": "string", "format": "uuid"}, "objectName": {"type": "string", "nullable": true}, "metadataKey": {"type": "string", "nullable": true}, "refId": {"type": "string", "format": "uuid", "nullable": true}, "parentObjectValueId": {"type": "string", "format": "uuid", "nullable": true}, "parentObjectValue": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}, "childValuesCount": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}, "modifiedAt": {"type": "string", "format": "date-time"}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "ObjectValueDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ObjectValueDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ObjectValueResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "refId": {"type": "string", "format": "uuid", "nullable": true}, "parentObjectValueId": {"type": "string", "format": "uuid", "nullable": true}, "value": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "objectMetadata": {"$ref": "#/components/schemas/ObjectMetadataDto"}, "childValues": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectValueResponseDto"}, "nullable": true}}, "additionalProperties": false}, "ObjectValueResponseDtoListResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectValueResponseDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ObjectValueResponseDtoPaginatedResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectValueResponseDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}, "page": {"type": "integer", "format": "int32"}, "firstPage": {"type": "integer", "format": "int32"}, "lastPage": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalItems": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "ObjectValueResponseDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ObjectValueResponseDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ObjectValuesRefIdSummaryDto": {"type": "object", "properties": {"refId": {"type": "string", "format": "uuid"}, "objectId": {"type": "string", "format": "uuid"}, "objectName": {"type": "string", "nullable": true}, "valueCount": {"type": "integer", "format": "int32"}, "lastModified": {"type": "string", "format": "date-time"}, "isComplete": {"type": "boolean"}, "metaValues": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ObjectValuesRefIdSummaryDtoPaginatedResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectValuesRefIdSummaryDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}, "page": {"type": "integer", "format": "int32"}, "firstPage": {"type": "integer", "format": "int32"}, "lastPage": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalItems": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "ObjectValuesRefIdSummaryDtoPaginatedResultResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ObjectValuesRefIdSummaryDtoPaginatedResult"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ObjectValuesResponseDto": {"type": "object", "properties": {"refId": {"type": "string", "format": "uuid"}, "objectId": {"type": "string", "format": "uuid"}, "objectName": {"type": "string", "nullable": true}, "objectDescription": {"type": "string", "nullable": true}, "parentObjectId": {"type": "string", "format": "uuid", "nullable": true}, "parentObjectName": {"type": "string", "nullable": true}, "values": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/ObjectValueDetailDto"}, "nullable": true}, "tenantId": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "ObjectValuesResponseDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ObjectValuesResponseDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ParentObjectInfoDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}, "ProcessingSummaryDto": {"type": "object", "properties": {"processingTimeMs": {"type": "integer", "format": "int64"}, "transactionsExecuted": {"type": "integer", "format": "int32"}, "warnings": {"type": "array", "items": {"type": "string"}, "nullable": true}, "dataTypeMappings": {"type": "object", "additionalProperties": {"type": "string", "format": "uuid"}, "nullable": true}}, "additionalProperties": false}, "ProductComprehensiveDto": {"type": "object", "properties": {"productInfo": {"$ref": "#/components/schemas/ProductInfoDto"}, "productMetadata": {"type": "array", "items": {"$ref": "#/components/schemas/MetadataWithValuesDto"}, "nullable": true}, "features": {"type": "array", "items": {"$ref": "#/components/schemas/FeatureComprehensiveDto"}, "nullable": true}}, "additionalProperties": false}, "ProductDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "version": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "isUserImported": {"type": "boolean"}, "isRoleAssigned": {"type": "boolean"}, "apiKey": {"type": "string", "nullable": true}, "isOnboardCompleted": {"type": "boolean"}, "applicationUrl": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid"}, "modifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "ProductDtoPaginatedResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}, "page": {"type": "integer", "format": "int32"}, "firstPage": {"type": "integer", "format": "int32"}, "lastPage": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalItems": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "ProductDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ProductDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ProductInfoDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "tenantId": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "version": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "ProductWithSubscriptionDto": {"type": "object", "properties": {"product": {"$ref": "#/components/schemas/ProductDto"}, "subscription": {"$ref": "#/components/schemas/SubscriptionDto"}}, "additionalProperties": false}, "ProductWithSubscriptionDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ProductWithSubscriptionDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "RefreshTokenRequest": {"type": "object", "properties": {"token": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RegisterUserRequest": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "confirmPassword": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ResetPasswordRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "confirmPassword": {"type": "string", "nullable": true}, "token": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RoleDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "normalizedName": {"type": "string", "nullable": true}, "concurrencyStamp": {"type": "string", "nullable": true}, "productId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "isSystemRole": {"type": "boolean"}, "permissions": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}, "modifiedAt": {"type": "string", "format": "date-time"}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}, "isDeleted": {"type": "boolean"}}, "additionalProperties": false}, "RoleDtoListResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RoleDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "RoleDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/RoleDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "StringApiResponse": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "SubscriptionDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "subscriptionType": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "autoRenew": {"type": "boolean"}, "pricingTier": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "modifiedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "TenantDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "connectionString": {"type": "string", "nullable": true}, "adminEmail": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "validUpto": {"type": "string", "format": "date-time"}, "issuer": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TenantDtoListResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TenantDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "TenantWithProductDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "connectionString": {"type": "string", "nullable": true}, "adminEmail": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "validUpto": {"type": "string", "format": "date-time"}, "issuer": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TenantWithProductDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/TenantWithProductDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "TimeZoneInfoDto": {"type": "object", "properties": {"timeZoneId": {"type": "string", "nullable": true}, "timeZoneDisplay": {"type": "string", "nullable": true}, "timeZoneName": {"type": "string", "nullable": true}, "baseUTcOffset": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TokenRequest": {"type": "object", "properties": {"usernameOrEmail": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TokenResponse": {"type": "object", "properties": {"token": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "expiresIn": {"type": "integer", "format": "int32"}, "userId": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "roles": {"type": "array", "items": {"type": "string"}, "nullable": true}, "tenantId": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "TokenResponseResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/TokenResponse"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "UpdateDataTypeCommand": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "uiComponent": {"type": "string", "nullable": true}, "validationPattern": {"type": "string", "nullable": true}, "minLength": {"type": "integer", "format": "int32", "nullable": true}, "maxLength": {"type": "integer", "format": "int32", "nullable": true}, "minValue": {"type": "number", "format": "double", "nullable": true}, "maxValue": {"type": "number", "format": "double", "nullable": true}, "decimalPlaces": {"type": "integer", "format": "int32", "nullable": true}, "stepValue": {"type": "number", "format": "double", "nullable": true}, "isRequired": {"type": "boolean"}, "inputType": {"type": "string", "nullable": true}, "inputMask": {"type": "string", "nullable": true}, "placeholder": {"type": "string", "nullable": true}, "htmlAttributes": {"type": "string", "nullable": true}, "defaultOptions": {"type": "string", "nullable": true}, "allowsMultiple": {"type": "boolean"}, "allowsCustomOptions": {"type": "boolean"}, "maxSelections": {"type": "integer", "format": "int32", "nullable": true}, "allowedFileTypes": {"type": "string", "nullable": true}, "maxFileSizeBytes": {"type": "integer", "format": "int64", "nullable": true}, "requiredErrorMessage": {"type": "string", "nullable": true}, "patternErrorMessage": {"type": "string", "nullable": true}, "minLengthErrorMessage": {"type": "string", "nullable": true}, "maxLengthErrorMessage": {"type": "string", "nullable": true}, "minValueErrorMessage": {"type": "string", "nullable": true}, "maxValueErrorMessage": {"type": "string", "nullable": true}, "fileTypeErrorMessage": {"type": "string", "nullable": true}, "fileSizeErrorMessage": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateFeatureCommand": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "isDefault": {"type": "boolean"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateFieldMappingCommand": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "apiName": {"type": "string", "nullable": true}, "sourceField": {"type": "string", "nullable": true}, "sourceType": {"type": "string", "nullable": true}, "objectMetadataId": {"type": "string", "format": "uuid", "nullable": true}, "userId": {"type": "string", "format": "uuid", "nullable": true}, "roleId": {"type": "string", "format": "uuid", "nullable": true}, "targetObjectName": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateIntegrationApiCommand": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "endpointUrl": {"type": "string", "nullable": true}, "schema": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateIntegrationCommand": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "authType": {"type": "string", "nullable": true}, "authConfig": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "syncFrequency": {"type": "string", "format": "date-span", "nullable": true}}, "additionalProperties": false}, "UpdateIntegrationConfigurationCommand": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "integrationApiId": {"type": "string", "format": "uuid"}, "objectId": {"type": "string", "format": "uuid"}, "direction": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateMetadataCommand": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "metadataKey": {"type": "string", "nullable": true}, "dataTypeId": {"type": "string", "format": "uuid"}, "customValidationPattern": {"type": "string", "nullable": true}, "customMinLength": {"type": "integer", "format": "int32", "nullable": true}, "customMaxLength": {"type": "integer", "format": "int32", "nullable": true}, "customMinValue": {"type": "number", "format": "double", "nullable": true}, "customMaxValue": {"type": "number", "format": "double", "nullable": true}, "customIsRequired": {"type": "boolean", "nullable": true}, "customPlaceholder": {"type": "string", "nullable": true}, "customOptions": {"type": "string", "nullable": true}, "customMaxSelections": {"type": "integer", "format": "int32", "nullable": true}, "customAllowedFileTypes": {"type": "string", "nullable": true}, "customMaxFileSize": {"type": "integer", "format": "int64", "nullable": true}, "customErrorMessage": {"type": "string", "nullable": true}, "displayLabel": {"type": "string", "nullable": true}, "helpText": {"type": "string", "nullable": true}, "fieldOrder": {"type": "integer", "format": "int32", "nullable": true}, "isVisible": {"type": "boolean"}, "isReadonly": {"type": "boolean"}}, "additionalProperties": false}, "UpdateProductCommand": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "version": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "isUserImported": {"type": "boolean"}, "isRoleAssigned": {"type": "boolean"}, "apiKey": {"type": "string", "nullable": true}, "isOnboardCompleted": {"type": "boolean"}, "applicationUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateRoleRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "productId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "isSystemRole": {"type": "boolean"}, "permissions": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateUserRequest": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "imageUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpsertObjectValuesRequestDto": {"type": "object", "properties": {"refId": {"type": "string", "format": "uuid"}, "objectId": {"type": "string", "format": "uuid"}, "values": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}, "autoCreateMetadata": {"type": "boolean"}, "strictValidation": {"type": "boolean"}}, "additionalProperties": false}, "UpsertObjectValuesResponseDto": {"type": "object", "properties": {"objectValues": {"$ref": "#/components/schemas/ObjectValuesResponseDto"}, "createdCount": {"type": "integer", "format": "int32"}, "updatedCount": {"type": "integer", "format": "int32"}, "metadataCreatedCount": {"type": "integer", "format": "int32"}, "validationErrors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "warnings": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UpsertObjectValuesResponseDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/UpsertObjectValuesResponseDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "UpsertObjectWithMetadataRequestDto": {"required": ["object"], "type": "object", "properties": {"object": {"$ref": "#/components/schemas/ObjectUpsertDto"}}, "additionalProperties": false}, "UserDataResponseDto": {"type": "object", "properties": {"userId": {"type": "string", "format": "uuid"}, "metadataCreated": {"type": "integer", "format": "int32"}, "userMetadataCreated": {"type": "integer", "format": "int32"}, "userValuesCreated": {"type": "integer", "format": "int32"}, "createdMetadataKeys": {"type": "array", "items": {"type": "string"}, "nullable": true}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserDataResponseDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/UserDataResponseDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "UserDataRoleDto": {"type": "object", "properties": {"roleId": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserDetailsDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userName": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "normalizedUserName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "normalizedEmail": {"type": "string", "nullable": true}, "emailConfirmed": {"type": "boolean"}, "phoneNumber": {"type": "string", "nullable": true}, "phoneNumberConfirmed": {"type": "boolean"}, "twoFactorEnabled": {"type": "boolean"}, "lockoutEnd": {"type": "string", "format": "date-time", "nullable": true}, "lockoutEnabled": {"type": "boolean"}, "accessFailedCount": {"type": "integer", "format": "int32"}, "externalUserId": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "lastLoginAt": {"type": "string", "format": "date-time", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "refreshTokenExpiryTime": {"type": "string", "format": "date-time", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}, "modifiedAt": {"type": "string", "format": "date-time"}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}, "isDeleted": {"type": "boolean"}, "roles": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UserDetailsDtoListResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/UserDetailsDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "UserDetailsDtoObjectPagedResponse": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/UserDetailsDto"}, "nullable": true}, "itemsCount": {"type": "integer", "format": "int32", "readOnly": true}, "totalCount": {"type": "integer", "format": "int32"}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "UserDetailsDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/UserDetailsDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "UserDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userName": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "normalizedUserName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "normalizedEmail": {"type": "string", "nullable": true}, "emailConfirmed": {"type": "boolean"}, "phoneNumber": {"type": "string", "nullable": true}, "phoneNumberConfirmed": {"type": "boolean"}, "twoFactorEnabled": {"type": "boolean"}, "lockoutEnd": {"type": "string", "format": "date-time", "nullable": true}, "lockoutEnabled": {"type": "boolean"}, "accessFailedCount": {"type": "integer", "format": "int32"}, "externalUserId": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "lastLoginAt": {"type": "string", "format": "date-time", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "refreshTokenExpiryTime": {"type": "string", "format": "date-time", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}, "modifiedAt": {"type": "string", "format": "date-time"}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}, "isDeleted": {"type": "boolean"}, "roles": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UserDtoApiResponse": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/UserDto"}}, "additionalProperties": false}, "UserDtoListApiResponse": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}, "nullable": true}}, "additionalProperties": false}, "UserRoleDto": {"type": "object", "properties": {"roleId": {"type": "string", "format": "uuid", "nullable": true}, "roleName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "enabled": {"type": "boolean"}}, "additionalProperties": false}, "UserRoleDtoListApiResponse": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/UserRoleDto"}, "nullable": true}}, "additionalProperties": false}, "UserRoleUpdateRequest": {"type": "object", "properties": {"userId": {"type": "string", "nullable": true}, "roleIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UserRoleUpdateResult": {"type": "object", "properties": {"userId": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "assignedRoles": {"type": "array", "items": {"$ref": "#/components/schemas/UserRoleDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ValueInfoDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "refId": {"type": "string", "format": "uuid", "nullable": true}, "value": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ViewFieldMappingDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "apiName": {"type": "string", "nullable": true}, "sourceField": {"type": "string", "nullable": true}, "sourceType": {"type": "string", "nullable": true}, "objectMetadataId": {"type": "string", "format": "uuid", "nullable": true}, "objectMetadataKey": {"type": "string", "nullable": true}, "userId": {"type": "string", "format": "uuid", "nullable": true}, "userName": {"type": "string", "nullable": true}, "roleId": {"type": "string", "format": "uuid", "nullable": true}, "roleName": {"type": "string", "nullable": true}, "targetObjectName": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}, "modifiedAt": {"type": "string", "format": "date-time"}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "ViewFieldMappingDtoListResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ViewFieldMappingDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ViewFieldMappingDtoPaginatedResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ViewFieldMappingDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}, "page": {"type": "integer", "format": "int32"}, "firstPage": {"type": "integer", "format": "int32"}, "lastPage": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalItems": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "ViewFieldMappingDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ViewFieldMappingDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ViewIntegrationApiDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "productName": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "endpointUrl": {"type": "string", "nullable": true}, "schema": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}, "modifiedAt": {"type": "string", "format": "date-time"}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "ViewIntegrationApiDtoListResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ViewIntegrationApiDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ViewIntegrationApiDtoPaginatedResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ViewIntegrationApiDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}, "page": {"type": "integer", "format": "int32"}, "firstPage": {"type": "integer", "format": "int32"}, "lastPage": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalItems": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "ViewIntegrationApiDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ViewIntegrationApiDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ViewIntegrationConfigurationDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "integrationId": {"type": "string", "format": "uuid"}, "integrationName": {"type": "string", "nullable": true}, "integrationApiId": {"type": "string", "format": "uuid"}, "integrationApiName": {"type": "string", "nullable": true}, "objectId": {"type": "string", "format": "uuid"}, "objectName": {"type": "string", "nullable": true}, "direction": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid", "nullable": true}, "modifiedAt": {"type": "string", "format": "date-time"}, "modifiedBy": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "ViewIntegrationConfigurationDtoListResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ViewIntegrationConfigurationDtoPaginatedResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDto"}, "nullable": true}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}, "page": {"type": "integer", "format": "int32"}, "firstPage": {"type": "integer", "format": "int32"}, "lastPage": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "totalItems": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "ViewIntegrationConfigurationDtoResult": {"type": "object", "properties": {"succeeded": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/ViewIntegrationConfigurationDto"}, "message": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "http", "description": "Input your Bearer token to access this API", "scheme": "Bearer", "bearerFormat": "JWT"}, "Tenant": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Input your tenant ID to access this API", "name": "tenant", "in": "header"}}}, "security": [{"Bearer": []}, {"Tenant": []}]}