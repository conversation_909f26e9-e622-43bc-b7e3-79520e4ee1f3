using Application.FeatureValues.DTOs;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.FeatureValues.Commands;

/// <summary>
/// Create FeatureValue command handler
/// </summary>
public class CreateFeatureValueCommandHandler : IRequestHandler<CreateFeatureValueCommand, Result<FeatureValueDto>>
{
    private readonly IRepository<Domain.Entities.FeatureValue> _repository;
    private readonly IRepository<Domain.Entities.FeatureMetadata> _featureMetadataRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateFeatureValueCommandHandler(
        IRepository<Domain.Entities.FeatureValue> repository,
        IRepository<Domain.Entities.FeatureMetadata> featureMetadataRepository)
    {
        _repository = repository;
        _featureMetadataRepository = featureMetadataRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<FeatureValueDto>> Handle(CreateFeatureValueCommand request, CancellationToken cancellationToken)
    {
        // Validate FeatureMetadata exists
        var featureMetadata = await _featureMetadataRepository.GetByIdAsync(request.FeatureMetadataId, cancellationToken);
        if (featureMetadata == null)
        {
            return Result<FeatureValueDto>.Failure($"FeatureMetadata with ID '{request.FeatureMetadataId}' not found.");
        }

        // Create FeatureValue
        var featureValue = new Domain.Entities.FeatureValue
        {
            FeatureMetadataId = request.FeatureMetadataId,
            RefId = request.RefId,
            Value = request.Value
        };

        await _repository.AddAsync(featureValue, cancellationToken);

        var dto = new FeatureValueDto
        {
            Id = featureValue.Id,
            FeatureMetadataId = featureValue.FeatureMetadataId,
            RefId = featureValue.RefId,
            ParentFeatureValueId = request.ParentFeatureValueId, // From request, not entity
            Value = featureValue.Value,
            ChildValuesCount = 0,
            CreatedAt = featureValue.CreatedAt,
            CreatedBy = featureValue.CreatedBy ?? Guid.Empty,
            ModifiedAt = featureValue.ModifiedAt,
            ModifiedBy = featureValue.ModifiedBy
        };

        return Result<FeatureValueDto>.Success(dto);
    }
}
