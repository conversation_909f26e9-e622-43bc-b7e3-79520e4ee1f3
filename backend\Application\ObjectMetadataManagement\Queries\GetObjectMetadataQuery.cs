using Application.ObjectMetadataManagement.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.ObjectMetadataManagement.Queries;

/// <summary>
/// Get ObjectMetadata query
/// </summary>
public class GetObjectMetadataQuery : IRequest<PaginatedResult<ObjectMetadataDto>>
{
    /// <summary>
    /// Page number
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Search term
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Object ID filter
    /// </summary>
    public Guid? ObjectId { get; set; }

    /// <summary>
    /// Metadata ID filter
    /// </summary>
    public Guid? MetadataId { get; set; }

    /// <summary>
    /// Is active filter
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Order by
    /// </summary>
    public string? OrderBy { get; set; }
}
