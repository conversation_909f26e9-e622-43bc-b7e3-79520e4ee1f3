using Abstraction.Database.Repositories;
using Application.MetadataManagement.DTOs;
using Application.MetadataManagement.Specifications;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.MetadataManagement.Commands;

/// <summary>
/// Create Metadata command handler
/// </summary>
public class CreateMetadataCommandHandler : IRequestHandler<CreateMetadataCommand, Result<MetadataDto>>
{
    private readonly IRepository<Domain.Entities.Metadata> _repository;
    private readonly IRepository<DataType> _dataTypeRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateMetadataCommandHandler(
        IRepository<Domain.Entities.Metadata> repository,
        IRepository<DataType> dataTypeRepository)
    {
        _repository = repository;
        _dataTypeRepository = dataTypeRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<MetadataDto>> Handle(CreateMetadataCommand request, CancellationToken cancellationToken)
    {
        // Validate DataType exists
        var dataType = await _dataTypeRepository.GetByIdAsync(request.DataTypeId, cancellationToken);
        if (dataType == null)
        {
            return Result<MetadataDto>.Failure($"DataType with ID '{request.DataTypeId}' not found.");
        }

        // Check if Metadata with same key already exists
        var existingMetadata = await _repository.GetBySpecAsync(new MetadataByKeySpec(request.MetadataKey), cancellationToken);
        if (existingMetadata != null)
        {
            return Result<MetadataDto>.Failure($"Metadata with key '{request.MetadataKey}' already exists.");
        }

        // Create new Metadata
        var metadata = new Domain.Entities.Metadata
        {
            MetadataKey = request.MetadataKey,
            DataTypeId = request.DataTypeId,
            CustomValidationPattern = request.CustomValidationPattern,
            CustomMinLength = request.CustomMinLength,
            CustomMaxLength = request.CustomMaxLength,
            CustomMinValue = request.CustomMinValue,
            CustomMaxValue = request.CustomMaxValue,
            CustomIsRequired = request.CustomIsRequired,
            CustomPlaceholder = request.CustomPlaceholder,
            CustomOptions = request.CustomOptions,
            CustomMaxSelections = request.CustomMaxSelections,
            CustomAllowedFileTypes = request.CustomAllowedFileTypes,
            CustomMaxFileSize = request.CustomMaxFileSize,
            CustomErrorMessage = request.CustomErrorMessage,
            DisplayLabel = request.DisplayLabel,
            HelpText = request.HelpText,
            FieldOrder = request.FieldOrder,
            IsVisible = request.IsVisible,
            IsReadonly = request.IsReadonly
        };

        var createdMetadata = await _repository.AddAsync(metadata, cancellationToken);

        var dto = new MetadataDto
        {
            Id = createdMetadata.Id,
            MetadataKey = createdMetadata.MetadataKey,
            DataTypeId = createdMetadata.DataTypeId,
            DataTypeName = dataType.Name,
            CustomValidationPattern = createdMetadata.CustomValidationPattern,
            CustomMinLength = createdMetadata.CustomMinLength,
            CustomMaxLength = createdMetadata.CustomMaxLength,
            CustomMinValue = createdMetadata.CustomMinValue,
            CustomMaxValue = createdMetadata.CustomMaxValue,
            CustomIsRequired = createdMetadata.CustomIsRequired,
            CustomPlaceholder = createdMetadata.CustomPlaceholder,
            CustomOptions = createdMetadata.CustomOptions,
            CustomMaxSelections = createdMetadata.CustomMaxSelections,
            CustomAllowedFileTypes = createdMetadata.CustomAllowedFileTypes,
            CustomMaxFileSize = createdMetadata.CustomMaxFileSize,
            CustomErrorMessage = createdMetadata.CustomErrorMessage,
            DisplayLabel = createdMetadata.DisplayLabel,
            HelpText = createdMetadata.HelpText,
            FieldOrder = createdMetadata.FieldOrder,
            IsVisible = createdMetadata.IsVisible,
            IsReadonly = createdMetadata.IsReadonly,
            CreatedAt = createdMetadata.CreatedAt,
            CreatedBy = createdMetadata.CreatedBy ?? Guid.Empty,
            ModifiedAt = createdMetadata.ModifiedAt,
            ModifiedBy = createdMetadata.ModifiedBy
        };

        return Result<MetadataDto>.Success(dto);
    }
}
