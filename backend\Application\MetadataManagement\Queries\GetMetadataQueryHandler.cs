using Application.MetadataManagement.DTOs;
using Application.MetadataManagement.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.MetadataManagement.Queries;

/// <summary>
/// Get Metadata query handler
/// </summary>
public class GetMetadataQueryHandler : IRequestHandler<GetMetadataQuery, PaginatedResult<MetadataDto>>
{
    private readonly IRepository<Domain.Entities.Metadata> _repository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetMetadataQueryHandler(IRepository<Domain.Entities.Metadata> repository)
    {
        _repository = repository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<PaginatedResult<MetadataDto>> Handle(GetMetadataQuery request, CancellationToken cancellationToken)
    {
        var skip = (request.PageNumber - 1) * request.PageSize;
        var spec = new MetadataWithFiltersSpec(request.SearchTerm, request.DataTypeId, request.IsVisible, request.OrderBy, skip, request.PageSize);
        var countSpec = new MetadataCountSpec(request.SearchTerm, request.DataTypeId, request.IsVisible);

        var metadata = await _repository.ListAsync(spec, cancellationToken);
        var totalCount = await _repository.CountAsync(countSpec, cancellationToken);

        var metadataDtos = metadata.Select(m => new MetadataDto
        {
            Id = m.Id,
            MetadataKey = m.MetadataKey,
            DataTypeId = m.DataTypeId,
            DataTypeName = m.DataType?.Name,
            CustomValidationPattern = m.CustomValidationPattern,
            CustomMinLength = m.CustomMinLength,
            CustomMaxLength = m.CustomMaxLength,
            CustomMinValue = m.CustomMinValue,
            CustomMaxValue = m.CustomMaxValue,
            CustomIsRequired = m.CustomIsRequired,
            CustomPlaceholder = m.CustomPlaceholder,
            CustomOptions = m.CustomOptions,
            CustomMaxSelections = m.CustomMaxSelections,
            CustomAllowedFileTypes = m.CustomAllowedFileTypes,
            CustomMaxFileSize = m.CustomMaxFileSize,
            CustomErrorMessage = m.CustomErrorMessage,
            DisplayLabel = m.DisplayLabel,
            HelpText = m.HelpText,
            FieldOrder = m.FieldOrder,
            IsVisible = m.IsVisible,
            IsReadonly = m.IsReadonly,
            CreatedAt = m.CreatedAt,
            CreatedBy = m.CreatedBy ?? Guid.Empty,
            ModifiedAt = m.ModifiedAt,
            ModifiedBy = m.ModifiedBy
        }).ToList();

        return new PaginatedResult<MetadataDto>(metadataDtos, request.PageNumber, request.PageSize, totalCount);
    }
}
