using Application.ObjectValues.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.ObjectValues.Commands;

/// <summary>
/// Create ObjectValue command
/// </summary>
public class CreateObjectValueCommand : IRequest<Result<ObjectValueDto>>
{
    /// <summary>
    /// Object metadata ID
    /// </summary>
    public Guid ObjectMetadataId { get; set; }

    /// <summary>
    /// Reference ID for grouping related values
    /// </summary>
    public Guid? RefId { get; set; }

    /// <summary>
    /// Parent object value ID for hierarchical structure
    /// </summary>
    public Guid? ParentObjectValueId { get; set; }

    /// <summary>
    /// The actual value as text
    /// </summary>
    public string? Value { get; set; }
}
