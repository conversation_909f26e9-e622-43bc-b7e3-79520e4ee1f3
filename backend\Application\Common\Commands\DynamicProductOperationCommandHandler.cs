using Abstraction.Common;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;
using System.Diagnostics;

namespace Application.Common.Commands;

/// <summary>
/// Handler for dynamic product operation commands
/// </summary>
public class DynamicProductOperationCommandHandler : IRequestHandler<DynamicProductOperationCommand, Result<DynamicOperationResponse>>
{
    private readonly IDynamicRepository<Product> _productRepository;
    private readonly IDynamicRepository<Feature> _featureRepository;
    private readonly IDynamicRepository<Domain.Entities.Object> _objectRepository;
    private readonly IDynamicRepository<Domain.Entities.ProductMetadata> _productMetadataRepository;
    private readonly IDynamicRepository<Domain.Entities.FeatureMetadata> _featureMetadataRepository;
    private readonly IDynamicRepository<Domain.Entities.ObjectMetadata> _objectMetadataRepository;
    private readonly IDynamicRepository<ProductValue> _productValueRepository;
    private readonly IDynamicRepository<FeatureValue> _featureValueRepository;
    private readonly IDynamicRepository<ObjectValue> _objectValueRepository;
    private readonly IDynamicRepository<DataType> _dataTypeRepository;
    private readonly IDynamicRepository<Metadata> _metadataRepository;
    private readonly ILogger<DynamicProductOperationCommandHandler> _logger;

    // Store processed entities for metadata processing
    private List<Feature> _processedFeatures = new();
    private List<Domain.Entities.Object> _processedObjects = new();

    /// <summary>
    /// Constructor
    /// </summary>
    public DynamicProductOperationCommandHandler(
        IDynamicRepository<Product> productRepository,
        IDynamicRepository<Feature> featureRepository,
        IDynamicRepository<Domain.Entities.Object> objectRepository,
        IDynamicRepository<Domain.Entities.ProductMetadata> productMetadataRepository,
        IDynamicRepository<Domain.Entities.FeatureMetadata> featureMetadataRepository,
        IDynamicRepository<Domain.Entities.ObjectMetadata> objectMetadataRepository,
        IDynamicRepository<ProductValue> productValueRepository,
        IDynamicRepository<FeatureValue> featureValueRepository,
        IDynamicRepository<ObjectValue> objectValueRepository,
        IDynamicRepository<DataType> dataTypeRepository,
        IDynamicRepository<Metadata> metadataRepository,
        ILogger<DynamicProductOperationCommandHandler> logger)
    {
        _productRepository = productRepository;
        _featureRepository = featureRepository;
        _objectRepository = objectRepository;
        _productMetadataRepository = productMetadataRepository;
        _featureMetadataRepository = featureMetadataRepository;
        _objectMetadataRepository = objectMetadataRepository;
        _productValueRepository = productValueRepository;
        _featureValueRepository = featureValueRepository;
        _objectValueRepository = objectValueRepository;
        _dataTypeRepository = dataTypeRepository;
        _metadataRepository = metadataRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle dynamic product operation command
    /// </summary>
    public async Task<Result<DynamicOperationResponse>> Handle(DynamicProductOperationCommand request, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Processing dynamic product operation: {OperationType} - {Count} products", 
                request.OperationType, request.ProductData.Count);

            var response = new DynamicOperationResponse
            {
                Success = true,
                Message = $"Dynamic product {request.OperationType} operation completed successfully"
            };

            // Check if this is a project plan processing request (empty ProductData means use projectplan.json)
            if (!request.ProductData.Any() && request.IncludeMetadata)
            {
                _logger.LogInformation("Processing project plan data from projectplan.json");
                await ProcessProjectPlanData(request, response, cancellationToken);
            }
            else
            {
                // Process products
                var processedProducts = await ProcessProducts(request, response, cancellationToken);

                // Process related entities if requested
                if (request.IncludeFeatures)
                {
                    await ProcessRelatedFeatures(request, processedProducts, response, cancellationToken);
                }

                if (request.IncludeObjects)
                {
                    await ProcessRelatedObjects(request, processedProducts, response, cancellationToken);
                }

                if (request.IncludeMetadata)
                {
                    await ProcessRelatedMetadata(request, processedProducts, response, cancellationToken);
                }

                // Process values after metadata is created
                try
                {
                    _logger.LogInformation("About to start values processing...");
                    await ProcessRelatedValues(request, processedProducts, response, cancellationToken);
                    _logger.LogInformation("Values processing completed successfully");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error during values processing");
                    throw;
                }
            }

            stopwatch.Stop();
            response.ExecutionTimeMs = stopwatch.ElapsedMilliseconds;
            response.ProcessedCount = request.ProductData.Count;

            _logger.LogInformation("Dynamic product operation completed in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
            return Result<DynamicOperationResponse>.Success(response);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Dynamic product operation failed after {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
            
            var errorResponse = new DynamicOperationResponse
            {
                Success = false,
                Message = "Dynamic product operation failed",
                ExecutionTimeMs = stopwatch.ElapsedMilliseconds,
                FailedCount = request.ProductData.Count,
                Errors = new List<string> { ex.Message }
            };

            return Result<DynamicOperationResponse>.Success(errorResponse);
        }
    }

    /// <summary>
    /// Process products based on operation type
    /// </summary>
    private async Task<IEnumerable<Product>> ProcessProducts(
        DynamicProductOperationCommand request, 
        DynamicOperationResponse response, 
        CancellationToken cancellationToken)
    {
        switch (request.OperationType)
        {
            case DynamicOperationType.BulkInsert:
                var insertedProducts = await _productRepository.DynamicBulkInsertAsync(request.ProductData, cancellationToken);
                response.InsertedCount += insertedProducts.Count();
                response.CreatedIds.AddRange(insertedProducts.Select(p => p.Id));
                return insertedProducts;

            case DynamicOperationType.BulkUpsert:
                var upsertedProducts = await _productRepository.DynamicBulkUpsertAsync(request.ProductData, cancellationToken);
                response.InsertedCount += upsertedProducts.Count(); // Simplified - could track inserts vs updates separately
                response.CreatedIds.AddRange(upsertedProducts.Select(p => p.Id));
                return upsertedProducts;

            case DynamicOperationType.Insert:
                if (request.ProductData.Count == 1)
                {
                    var insertedProduct = await _productRepository.DynamicInsertAsync(request.ProductData.First(), cancellationToken);
                    response.InsertedCount = 1;
                    response.CreatedIds.Add(insertedProduct.Id);
                    return new[] { insertedProduct };
                }
                else
                {
                    var bulkInsertedProducts = await _productRepository.DynamicBulkInsertAsync(request.ProductData, cancellationToken);
                    response.InsertedCount += bulkInsertedProducts.Count();
                    response.CreatedIds.AddRange(bulkInsertedProducts.Select(p => p.Id));
                    return bulkInsertedProducts;
                }

            case DynamicOperationType.Upsert:
                if (request.ProductData.Count == 1)
                {
                    var upsertedProduct = await _productRepository.DynamicUpsertAsync(request.ProductData.First(), cancellationToken);
                    response.InsertedCount = 1;
                    response.CreatedIds.Add(upsertedProduct.Id);
                    return new[] { upsertedProduct };
                }
                else
                {
                    var bulkUpsertedProducts = await _productRepository.DynamicBulkUpsertAsync(request.ProductData, cancellationToken);
                    response.InsertedCount += bulkUpsertedProducts.Count();
                    response.CreatedIds.AddRange(bulkUpsertedProducts.Select(p => p.Id));
                    return bulkUpsertedProducts;
                }

            default:
                throw new NotSupportedException($"Operation type {request.OperationType} not supported for products");
        }
    }

    /// <summary>
    /// Process related features
    /// </summary>
    private async Task ProcessRelatedFeatures(
        DynamicProductOperationCommand request,
        IEnumerable<Product> products,
        DynamicOperationResponse response,
        CancellationToken cancellationToken)
    {
        var featureDataList = new List<Dictionary<string, object?>>();
        var productsList = products.ToList();

        for (int i = 0; i < request.ProductData.Count; i++)
        {
            var productData = request.ProductData[i];
            var product = i < productsList.Count ? productsList[i] : productsList.FirstOrDefault();

            if (product == null) continue;

            // Check for both "features" and "Features" (case insensitive)
            var featuresKey = productData.Keys.FirstOrDefault(k => k.Equals("features", StringComparison.OrdinalIgnoreCase));

            if (featuresKey != null && productData[featuresKey] != null)
            {
                _logger.LogDebug("Found features data for product {ProductId}", product.Id);

                try
                {
                    // Handle different JSON deserialization formats
                    var featuresData = productData[featuresKey];

                    if (featuresData is System.Text.Json.JsonElement jsonElement && jsonElement.ValueKind == System.Text.Json.JsonValueKind.Array)
                    {
                        foreach (var featureElement in jsonElement.EnumerateArray())
                        {
                            var featureDict = JsonElementToDictionary(featureElement);
                            featureDict["ProductId"] = product.Id;
                            featureDataList.Add(featureDict);
                        }
                    }
                    else if (featuresData is IEnumerable<object> featuresList)
                    {
                        foreach (var featureObj in featuresList)
                        {
                            var featureDict = ObjectToDictionary(featureObj);
                            featureDict["ProductId"] = product.Id;
                            featureDataList.Add(featureDict);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing features for product {ProductId}", product.Id);
                }
            }
        }

        if (featureDataList.Any())
        {
            _logger.LogInformation("Processing {Count} features", featureDataList.Count);
            var processedFeatures = await _featureRepository.DynamicBulkUpsertAsync(featureDataList, cancellationToken);
            response.InsertedCount += processedFeatures.Count();
            response.CreatedIds.AddRange(processedFeatures.Select(f => f.Id));

            // Store processed features for object processing and metadata processing
            _processedFeatures = processedFeatures.ToList();

            // Process feature metadata if present
            await ProcessFeatureMetadata(request, _processedFeatures, response, cancellationToken);
        }
    }

    /// <summary>
    /// Process related objects
    /// </summary>
    private async Task ProcessRelatedObjects(
        DynamicProductOperationCommand request,
        IEnumerable<Product> products,
        DynamicOperationResponse response,
        CancellationToken cancellationToken)
    {
        var objectDataList = new List<Dictionary<string, object?>>();
        var productsList = products.ToList();

        // Process objects nested within features
        for (int i = 0; i < request.ProductData.Count; i++)
        {
            var productData = request.ProductData[i];
            var product = i < productsList.Count ? productsList[i] : productsList.FirstOrDefault();

            if (product == null) continue;

            // Check for features that contain objects
            var featuresKey = productData.Keys.FirstOrDefault(k => k.Equals("features", StringComparison.OrdinalIgnoreCase));

            if (featuresKey != null && productData[featuresKey] != null)
            {
                try
                {
                    var featuresData = productData[featuresKey];

                    if (featuresData is System.Text.Json.JsonElement jsonElement && jsonElement.ValueKind == System.Text.Json.JsonValueKind.Array)
                    {
                        var featureIndex = 0;
                        foreach (var featureElement in jsonElement.EnumerateArray())
                        {
                            var feature = featureIndex < _processedFeatures.Count ? _processedFeatures[featureIndex] : _processedFeatures.FirstOrDefault();
                            ProcessObjectsFromFeature(featureElement, feature, objectDataList);
                            featureIndex++;
                        }
                    }
                    else if (featuresData is IEnumerable<object> featuresList)
                    {
                        var featureIndex = 0;
                        foreach (var featureObj in featuresList)
                        {
                            var feature = featureIndex < _processedFeatures.Count ? _processedFeatures[featureIndex] : _processedFeatures.FirstOrDefault();
                            ProcessObjectsFromFeatureObject(featureObj, feature, objectDataList);
                            featureIndex++;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing objects for product {ProductId}", product.Id);
                }
            }

            // Also check for direct objects in product data
            var objectsKey = productData.Keys.FirstOrDefault(k => k.Equals("objects", StringComparison.OrdinalIgnoreCase));
            if (objectsKey != null && productData[objectsKey] != null)
            {
                try
                {
                    var objectsData = productData[objectsKey];
                    if (objectsData is System.Text.Json.JsonElement jsonElement && jsonElement.ValueKind == System.Text.Json.JsonValueKind.Array)
                    {
                        foreach (var objectElement in jsonElement.EnumerateArray())
                        {
                            var objectDict = JsonElementToDictionary(objectElement);
                            // For direct objects, we might not have a specific feature, so we'll use the first available feature
                            if (_processedFeatures.Any())
                            {
                                objectDict["FeatureId"] = _processedFeatures.First().Id;
                            }
                            objectDataList.Add(objectDict);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing direct objects for product {ProductId}", product.Id);
                }
            }
        }

        if (objectDataList.Any())
        {
            _logger.LogInformation("Processing {Count} objects", objectDataList.Count);
            var processedObjects = await _objectRepository.DynamicBulkUpsertAsync(objectDataList, cancellationToken);
            response.InsertedCount += processedObjects.Count();
            response.CreatedIds.AddRange(processedObjects.Select(o => o.Id));

            // Store processed objects for metadata processing
            _processedObjects = processedObjects.ToList();

            // Process object metadata if present
            await ProcessObjectMetadata(request, _processedObjects, response, cancellationToken);
        }
    }

    /// <summary>
    /// Process related metadata
    /// </summary>
    private async Task ProcessRelatedMetadata(
        DynamicProductOperationCommand request,
        IEnumerable<Product> products,
        DynamicOperationResponse response,
        CancellationToken cancellationToken)
    {
        var productMetadataList = new List<Dictionary<string, object?>>();
        var productsList = products.ToList();

        for (int i = 0; i < request.ProductData.Count; i++)
        {
            var productData = request.ProductData[i];
            var product = i < productsList.Count ? productsList[i] : productsList.FirstOrDefault();

            if (product == null) continue;

            // Check for both "metadata" and "Metadata" (case insensitive)
            var metadataKey = productData.Keys.FirstOrDefault(k => k.Equals("metadata", StringComparison.OrdinalIgnoreCase));

            if (metadataKey != null && productData[metadataKey] != null)
            {
                _logger.LogDebug("Found metadata for product {ProductId}", product.Id);

                try
                {
                    var metadataData = productData[metadataKey];

                    if (metadataData is System.Text.Json.JsonElement jsonElement && jsonElement.ValueKind == System.Text.Json.JsonValueKind.Array)
                    {
                        foreach (var metaElement in jsonElement.EnumerateArray())
                        {
                            var metaDict = JsonElementToDictionary(metaElement);
                            await ProcessSingleMetadata(metaDict, product.Id, productMetadataList, cancellationToken);
                        }
                    }
                    else if (metadataData is IEnumerable<object> metadataItems)
                    {
                        foreach (var metaObj in metadataItems)
                        {
                            var metaDict = ObjectToDictionary(metaObj);
                            await ProcessSingleMetadata(metaDict, product.Id, productMetadataList, cancellationToken);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing metadata for product {ProductId}", product.Id);
                }
            }
        }

        if (productMetadataList.Any())
        {
            _logger.LogInformation("Processing {Count} product metadata items", productMetadataList.Count);

            // Process each metadata item individually to ensure proper Metadata creation
            var processedMetadataIds = new List<Guid>();
            foreach (var metadataDict in productMetadataList)
            {
                try
                {
                    var processedMetadata = await _productMetadataRepository.DynamicBulkUpsertAsync(new[] { metadataDict }, cancellationToken);
                    var metadata = processedMetadata.FirstOrDefault();
                    if (metadata != null)
                    {
                        processedMetadataIds.Add(metadata.Id);
                        response.InsertedCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to process individual metadata item");
                }
            }

            response.CreatedIds.AddRange(processedMetadataIds);
        }
    }

    /// <summary>
    /// Convert JsonElement to Dictionary
    /// </summary>
    private Dictionary<string, object?> JsonElementToDictionary(System.Text.Json.JsonElement element)
    {
        var dictionary = new Dictionary<string, object?>();

        foreach (var property in element.EnumerateObject())
        {
            dictionary[property.Name] = JsonElementToObject(property.Value);
        }

        return dictionary;
    }

    /// <summary>
    /// Convert JsonElement to object
    /// </summary>
    private object? JsonElementToObject(System.Text.Json.JsonElement element)
    {
        return element.ValueKind switch
        {
            System.Text.Json.JsonValueKind.String => element.GetString(),
            System.Text.Json.JsonValueKind.Number => element.TryGetInt32(out var intVal) ? (object)intVal :
                                                    element.TryGetInt64(out var longVal) ? (object)longVal :
                                                    element.TryGetDecimal(out var decVal) ? (object)decVal :
                                                    element.GetDouble(),
            System.Text.Json.JsonValueKind.True => true,
            System.Text.Json.JsonValueKind.False => false,
            System.Text.Json.JsonValueKind.Null => null,
            System.Text.Json.JsonValueKind.Array => element.EnumerateArray().Select(JsonElementToObject).ToArray(),
            System.Text.Json.JsonValueKind.Object => JsonElementToDictionary(element),
            _ => element.ToString()
        };
    }

    /// <summary>
    /// Convert object to Dictionary using reflection
    /// </summary>
    private Dictionary<string, object?> ObjectToDictionary(object obj)
    {
        var dictionary = new Dictionary<string, object?>();

        if (obj == null) return dictionary;

        var properties = obj.GetType().GetProperties();
        foreach (var property in properties)
        {
            try
            {
                var value = property.GetValue(obj);
                dictionary[property.Name] = value;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get property {PropertyName} from object", property.Name);
            }
        }

        return dictionary;
    }

    /// <summary>
    /// Process objects from a feature JsonElement
    /// </summary>
    private void ProcessObjectsFromFeature(System.Text.Json.JsonElement featureElement, Feature? feature, List<Dictionary<string, object?>> objectDataList)
    {
        if (feature == null) return;

        if (featureElement.TryGetProperty("objects", out var objectsProperty) && objectsProperty.ValueKind == System.Text.Json.JsonValueKind.Array)
        {
            foreach (var objectElement in objectsProperty.EnumerateArray())
            {
                var objectDict = JsonElementToDictionary(objectElement);
                objectDict["FeatureId"] = feature.Id;

                // Process flat properties as metadata and values automatically
                ProcessFlatObjectProperties(objectDict, objectElement);

                objectDataList.Add(objectDict);
            }
        }
    }

    /// <summary>
    /// Process flat object properties to automatically create metadata and values
    /// </summary>
    private void ProcessFlatObjectProperties(Dictionary<string, object?> objectDict, System.Text.Json.JsonElement objectElement)
    {
        var metadataList = new List<Dictionary<string, object?>>();
        var valuesList = new List<Dictionary<string, object?>>();

        // Skip system properties that shouldn't become metadata
        var skipProperties = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "id", "name", "description", "isActive", "featureId", "parentObjectId",
            "createdAt", "createdBy", "modifiedAt", "modifiedBy", "isDeleted"
        };

        foreach (var property in objectElement.EnumerateObject())
        {
            if (skipProperties.Contains(property.Name))
                continue;

            // Create metadata for this property
            var metadataKey = property.Name;
            var metadataDict = new Dictionary<string, object?>
            {
                ["MetadataKey"] = metadataKey,
                ["DataType"] = DetermineDataType(property.Value),
                ["IsRequired"] = false,
                ["IsUnique"] = false,
                ["IsActive"] = true
            };
            metadataList.Add(metadataDict);

            // Create value for this property
            var valueDict = new Dictionary<string, object?>
            {
                ["MetadataKey"] = metadataKey,
                ["Value"] = GetJsonElementValue(property.Value),
                ["RefId"] = objectDict.ContainsKey("id") ? objectDict["id"] : null
            };
            valuesList.Add(valueDict);
        }

        // Store metadata and values in the object dictionary for later processing
        if (metadataList.Any())
        {
            objectDict["_AutoMetadata"] = metadataList;
        }

        if (valuesList.Any())
        {
            objectDict["_AutoValues"] = valuesList;
        }
    }

    /// <summary>
    /// Determine data type from JsonElement
    /// </summary>
    private string DetermineDataType(System.Text.Json.JsonElement element)
    {
        return element.ValueKind switch
        {
            System.Text.Json.JsonValueKind.String => "text",
            System.Text.Json.JsonValueKind.Number => element.TryGetInt32(out _) ? "number" : "decimal",
            System.Text.Json.JsonValueKind.True or System.Text.Json.JsonValueKind.False => "boolean",
            System.Text.Json.JsonValueKind.Array => "array",
            System.Text.Json.JsonValueKind.Object => "object",
            _ => "text"
        };
    }

    /// <summary>
    /// Get value from JsonElement
    /// </summary>
    private object? GetJsonElementValue(System.Text.Json.JsonElement element)
    {
        return element.ValueKind switch
        {
            System.Text.Json.JsonValueKind.String => element.GetString(),
            System.Text.Json.JsonValueKind.Number => element.TryGetInt32(out var intVal) ? (object)intVal :
                                                    element.TryGetInt64(out var longVal) ? (object)longVal :
                                                    element.TryGetDecimal(out var decVal) ? (object)decVal :
                                                    element.GetDouble(),
            System.Text.Json.JsonValueKind.True => true,
            System.Text.Json.JsonValueKind.False => false,
            System.Text.Json.JsonValueKind.Null => null,
            System.Text.Json.JsonValueKind.Array => element.ToString(),
            System.Text.Json.JsonValueKind.Object => element.ToString(),
            _ => element.ToString()
        };
    }

    /// <summary>
    /// Process objects from a feature object
    /// </summary>
    private void ProcessObjectsFromFeatureObject(object featureObj, Feature? feature, List<Dictionary<string, object?>> objectDataList)
    {
        if (feature == null || featureObj == null) return;

        var featureDict = ObjectToDictionary(featureObj);
        var objectsKey = featureDict.Keys.FirstOrDefault(k => k.Equals("objects", StringComparison.OrdinalIgnoreCase));

        if (objectsKey != null && featureDict[objectsKey] is IEnumerable<object> objects)
        {
            foreach (var objectObj in objects)
            {
                var objectDict = ObjectToDictionary(objectObj);
                objectDict["FeatureId"] = feature.Id;
                objectDataList.Add(objectDict);
            }
        }
    }

    /// <summary>
    /// Process a single metadata item by creating Metadata record and ProductMetadata record
    /// </summary>
    private async Task ProcessSingleMetadata(
        Dictionary<string, object?> metaDict,
        Guid productId,
        List<Dictionary<string, object?>> productMetadataList,
        CancellationToken cancellationToken)
    {
        try
        {
            // Extract metadataId from the input (this is actually a key, not an ID)
            var metadataKey = metaDict.ContainsKey("metadataId") ? metaDict["metadataId"]?.ToString() : null;

            if (string.IsNullOrEmpty(metadataKey))
            {
                _logger.LogWarning("Metadata item missing metadataId, skipping");
                return;
            }

            // Get or create the Metadata record
            var metadataId = await GetOrCreateMetadata(metadataKey, cancellationToken);

            if (metadataId == null)
            {
                _logger.LogWarning("Failed to create or find metadata for key {MetadataKey}", metadataKey);
                return;
            }

            // Create ProductMetadata record
            var productMetadataDict = new Dictionary<string, object?>
            {
                ["ProductId"] = productId,
                ["MetadataId"] = metadataId,
                ["IsUnique"] = metaDict.ContainsKey("isUnique") ? metaDict["isUnique"] : false,
                ["IsActive"] = metaDict.ContainsKey("isActive") ? metaDict["isActive"] : true,
                ["ShouldVisibleInList"] = true,
                ["ShouldVisibleInEdit"] = true,
                ["ShouldVisibleInCreate"] = true,
                ["ShouldVisibleInView"] = true,
                ["IsCalculate"] = true
            };

            productMetadataList.Add(productMetadataDict);

            _logger.LogDebug("Created ProductMetadata for Product {ProductId} and Metadata {MetadataId}", productId, metadataId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing single metadata item");
        }
    }

    /// <summary>
    /// Get or create a Metadata record with proper DataType reference
    /// </summary>
    private async Task<Guid?> GetOrCreateMetadata(string metadataKey, CancellationToken cancellationToken)
    {
        try
        {
            // First, try to find existing metadata by key
            var existingMetadata = await _metadataRepository.ListAsync(cancellationToken);
            var metadata = existingMetadata.FirstOrDefault(m => m.MetadataKey == metadataKey);

            if (metadata != null)
            {
                _logger.LogDebug("Found existing metadata with key {MetadataKey} and ID {MetadataId}", metadataKey, metadata.Id);
                return metadata.Id;
            }

            // If not found, create new metadata
            // Determine DataType based on the metadata key or default to text
            var dataType = await GetDataTypeForMetadata(metadataKey, cancellationToken);

            if (dataType == null)
            {
                _logger.LogError("No suitable DataType found for metadata key {MetadataKey}", metadataKey);
                return null;
            }

            // Create new metadata
            var newMetadataDict = new Dictionary<string, object?>
            {
                ["MetadataKey"] = metadataKey,
                ["DataTypeId"] = dataType.Id,
                ["DisplayLabel"] = metadataKey,
                ["IsRequired"] = false,
                ["IsVisible"] = true,
                ["IsReadonly"] = false,
                ["IsActive"] = true
            };

            var createdMetadata = await _metadataRepository.DynamicBulkUpsertAsync(new[] { newMetadataDict }, cancellationToken);
            var newMetadata = createdMetadata.FirstOrDefault();

            if (newMetadata != null)
            {
                _logger.LogInformation("Created new metadata with key {MetadataKey} and ID {MetadataId}", metadataKey, newMetadata.Id);
                return newMetadata.Id;
            }

            _logger.LogError("Failed to create new metadata for key {MetadataKey}", metadataKey);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting or creating metadata for key {MetadataKey}", metadataKey);
            return null;
        }
    }

    /// <summary>
    /// Get appropriate DataType based on metadata key and field type
    /// </summary>
    private async Task<DataType?> GetDataTypeForMetadata(string metadataKey, CancellationToken cancellationToken)
    {
        try
        {
            var dataTypes = await _dataTypeRepository.ListAsync(cancellationToken);

            if (!dataTypes.Any())
            {
                _logger.LogError("No DataTypes found in the database");
                return null;
            }

            // Determine DataType based on metadata key patterns
            var lowerKey = metadataKey.ToLowerInvariant();

            // Check for specific patterns in metadata key
            if (lowerKey.Contains("date") || lowerKey.Contains("time"))
            {
                var dateType = dataTypes.FirstOrDefault(dt => dt.Name.Equals("date", StringComparison.OrdinalIgnoreCase) && dt.IsActive);
                if (dateType != null) return dateType;
            }

            if (lowerKey.Contains("number") || lowerKey.Contains("count") || lowerKey.Contains("amount") || lowerKey.Contains("price"))
            {
                var numberType = dataTypes.FirstOrDefault(dt => dt.Name.Equals("number", StringComparison.OrdinalIgnoreCase) && dt.IsActive);
                if (numberType != null) return numberType;
            }

            if (lowerKey.Contains("bool") || lowerKey.Contains("flag") || lowerKey.Contains("is") || lowerKey.Contains("has"))
            {
                var boolType = dataTypes.FirstOrDefault(dt => dt.Name.Equals("boolean", StringComparison.OrdinalIgnoreCase) && dt.IsActive);
                if (boolType != null) return boolType;
            }

            if (lowerKey.Contains("email"))
            {
                var emailType = dataTypes.FirstOrDefault(dt => dt.Name.Equals("email", StringComparison.OrdinalIgnoreCase) && dt.IsActive);
                if (emailType != null) return emailType;
            }

            // Default to text type
            var textDataType = dataTypes.FirstOrDefault(dt => dt.Name.Equals("text", StringComparison.OrdinalIgnoreCase) && dt.IsActive);
            if (textDataType != null)
            {
                return textDataType;
            }

            // If no text type, return the first active one
            var firstDataType = dataTypes.FirstOrDefault(dt => dt.IsActive);
            if (firstDataType != null)
            {
                _logger.LogWarning("No 'text' DataType found, using {DataTypeName} as default for metadata {MetadataKey}",
                    firstDataType.Name, metadataKey);
                return firstDataType;
            }

            _logger.LogError("No active DataTypes found in the database");
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting DataType for metadata {MetadataKey}", metadataKey);
            return null;
        }
    }

    /// <summary>
    /// Process feature metadata from the request data
    /// </summary>
    private async Task ProcessFeatureMetadata(
        DynamicProductOperationCommand request,
        IEnumerable<Feature> features,
        DynamicOperationResponse response,
        CancellationToken cancellationToken)
    {
        var featureMetadataList = new List<Dictionary<string, object?>>();
        var featuresList = features.ToList();

        for (int i = 0; i < request.ProductData.Count; i++)
        {
            var productData = request.ProductData[i];

            // Check for features that contain metadata
            var featuresKey = productData.Keys.FirstOrDefault(k => k.Equals("features", StringComparison.OrdinalIgnoreCase));

            if (featuresKey != null && productData[featuresKey] != null)
            {
                try
                {
                    var featuresData = productData[featuresKey];

                    if (featuresData is System.Text.Json.JsonElement jsonElement && jsonElement.ValueKind == System.Text.Json.JsonValueKind.Array)
                    {
                        var featureIndex = 0;
                        foreach (var featureElement in jsonElement.EnumerateArray())
                        {
                            var feature = featureIndex < featuresList.Count ? featuresList[featureIndex] : featuresList.FirstOrDefault();
                            if (feature != null)
                            {
                                await ProcessFeatureMetadataFromElement(featureElement, feature, featureMetadataList, cancellationToken);
                            }
                            featureIndex++;
                        }
                    }
                    else if (featuresData is IEnumerable<object> featuresList2)
                    {
                        var featureIndex = 0;
                        foreach (var featureObj in featuresList2)
                        {
                            var feature = featureIndex < featuresList.Count ? featuresList[featureIndex] : featuresList.FirstOrDefault();
                            if (feature != null)
                            {
                                await ProcessFeatureMetadataFromObject(featureObj, feature, featureMetadataList, cancellationToken);
                            }
                            featureIndex++;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing feature metadata");
                }
            }
        }

        if (featureMetadataList.Any())
        {
            _logger.LogInformation("Processing {Count} feature metadata items", featureMetadataList.Count);

            // Process each metadata item individually to ensure proper Metadata creation
            var processedMetadataIds = new List<Guid>();
            foreach (var metadataDict in featureMetadataList)
            {
                try
                {
                    var processedMetadata = await _featureMetadataRepository.DynamicBulkUpsertAsync(new[] { metadataDict }, cancellationToken);
                    var metadata = processedMetadata.FirstOrDefault();
                    if (metadata != null)
                    {
                        processedMetadataIds.Add(metadata.Id);
                        response.InsertedCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to process individual feature metadata item");
                }
            }

            response.CreatedIds.AddRange(processedMetadataIds);
        }
    }

    /// <summary>
    /// Process feature metadata from JsonElement
    /// </summary>
    private async Task ProcessFeatureMetadataFromElement(
        System.Text.Json.JsonElement featureElement,
        Feature feature,
        List<Dictionary<string, object?>> featureMetadataList,
        CancellationToken cancellationToken)
    {
        if (featureElement.TryGetProperty("metadata", out var metadataProperty) && metadataProperty.ValueKind == System.Text.Json.JsonValueKind.Array)
        {
            foreach (var metaElement in metadataProperty.EnumerateArray())
            {
                var metaDict = JsonElementToDictionary(metaElement);
                await ProcessSingleFeatureMetadata(metaDict, feature.Id, featureMetadataList, cancellationToken);
            }
        }
    }

    /// <summary>
    /// Process feature metadata from object
    /// </summary>
    private async Task ProcessFeatureMetadataFromObject(
        object featureObj,
        Feature feature,
        List<Dictionary<string, object?>> featureMetadataList,
        CancellationToken cancellationToken)
    {
        var featureDict = ObjectToDictionary(featureObj);
        if (featureDict.ContainsKey("metadata") && featureDict["metadata"] is IEnumerable<object> metadataItems)
        {
            foreach (var metaObj in metadataItems)
            {
                var metaDict = ObjectToDictionary(metaObj);
                await ProcessSingleFeatureMetadata(metaDict, feature.Id, featureMetadataList, cancellationToken);
            }
        }
    }

    /// <summary>
    /// Process a single feature metadata item
    /// </summary>
    private async Task ProcessSingleFeatureMetadata(
        Dictionary<string, object?> metaDict,
        Guid featureId,
        List<Dictionary<string, object?>> featureMetadataList,
        CancellationToken cancellationToken)
    {
        try
        {
            // Extract metadataId from the input (this is actually a key, not an ID)
            var metadataKey = metaDict.ContainsKey("metadataId") ? metaDict["metadataId"]?.ToString() : null;

            if (string.IsNullOrEmpty(metadataKey))
            {
                _logger.LogWarning("Feature metadata item missing metadataId, skipping");
                return;
            }

            // Get or create the Metadata record
            var metadataId = await GetOrCreateMetadata(metadataKey, cancellationToken);

            if (metadataId == null)
            {
                _logger.LogWarning("Failed to create or find metadata for key {MetadataKey}", metadataKey);
                return;
            }

            // Create FeatureMetadata record
            var featureMetadataDict = new Dictionary<string, object?>
            {
                ["FeatureId"] = featureId,
                ["MetadataId"] = metadataId,
                ["IsUnique"] = metaDict.ContainsKey("isUnique") ? metaDict["isUnique"] : false,
                ["IsActive"] = metaDict.ContainsKey("isActive") ? metaDict["isActive"] : true,
                ["ShouldVisibleInList"] = true,
                ["ShouldVisibleInEdit"] = true,
                ["ShouldVisibleInCreate"] = true,
                ["ShouldVisibleInView"] = true,
                ["IsCalculate"] = true
            };

            featureMetadataList.Add(featureMetadataDict);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing single feature metadata");
        }
    }

    /// <summary>
    /// Process object metadata from the request data
    /// </summary>
    private async Task ProcessObjectMetadata(
        DynamicProductOperationCommand request,
        IEnumerable<Domain.Entities.Object> objects,
        DynamicOperationResponse response,
        CancellationToken cancellationToken)
    {
        var objectMetadataList = new List<Dictionary<string, object?>>();
        var objectsList = objects.ToList();

        for (int i = 0; i < request.ProductData.Count; i++)
        {
            var productData = request.ProductData[i];

            // Check for features that contain objects with metadata
            var featuresKey = productData.Keys.FirstOrDefault(k => k.Equals("features", StringComparison.OrdinalIgnoreCase));

            if (featuresKey != null && productData[featuresKey] != null)
            {
                try
                {
                    var featuresData = productData[featuresKey];

                    if (featuresData is System.Text.Json.JsonElement jsonElement && jsonElement.ValueKind == System.Text.Json.JsonValueKind.Array)
                    {
                        var objectIndex = 0;
                        foreach (var featureElement in jsonElement.EnumerateArray())
                        {
                            objectIndex = await ProcessObjectMetadataFromFeatureElement(featureElement, objectsList, objectIndex, objectMetadataList, cancellationToken);
                        }
                    }
                    else if (featuresData is IEnumerable<object> featuresList)
                    {
                        var objectIndex = 0;
                        foreach (var featureObj in featuresList)
                        {
                            objectIndex = await ProcessObjectMetadataFromFeatureObject(featureObj, objectsList, objectIndex, objectMetadataList, cancellationToken);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing object metadata");
                }
            }

            // Also check for direct objects with metadata in product data
            var objectsKey = productData.Keys.FirstOrDefault(k => k.Equals("objects", StringComparison.OrdinalIgnoreCase));
            if (objectsKey != null && productData[objectsKey] != null)
            {
                try
                {
                    var objectsData = productData[objectsKey];
                    if (objectsData is System.Text.Json.JsonElement jsonElement && jsonElement.ValueKind == System.Text.Json.JsonValueKind.Array)
                    {
                        var objectIndex = 0;
                        foreach (var objectElement in jsonElement.EnumerateArray())
                        {
                            var obj = objectIndex < objectsList.Count ? objectsList[objectIndex] : objectsList.FirstOrDefault();
                            if (obj != null)
                            {
                                await ProcessObjectMetadataFromElement(objectElement, obj, objectMetadataList, cancellationToken);
                            }
                            objectIndex++;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing direct object metadata");
                }
            }
        }

        if (objectMetadataList.Any())
        {
            _logger.LogInformation("Processing {Count} object metadata items", objectMetadataList.Count);

            // Process each metadata item individually to ensure proper Metadata creation
            var processedMetadataIds = new List<Guid>();
            foreach (var metadataDict in objectMetadataList)
            {
                try
                {
                    var processedMetadata = await _objectMetadataRepository.DynamicBulkUpsertAsync(new[] { metadataDict }, cancellationToken);
                    var metadata = processedMetadata.FirstOrDefault();
                    if (metadata != null)
                    {
                        processedMetadataIds.Add(metadata.Id);
                        response.InsertedCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to process individual object metadata item");
                }
            }

            response.CreatedIds.AddRange(processedMetadataIds);
        }
    }

    /// <summary>
    /// Process object metadata from feature JsonElement
    /// </summary>
    private async Task<int> ProcessObjectMetadataFromFeatureElement(
        System.Text.Json.JsonElement featureElement,
        List<Domain.Entities.Object> objectsList,
        int objectIndex,
        List<Dictionary<string, object?>> objectMetadataList,
        CancellationToken cancellationToken)
    {
        if (featureElement.TryGetProperty("objects", out var objectsProperty) && objectsProperty.ValueKind == System.Text.Json.JsonValueKind.Array)
        {
            foreach (var objectElement in objectsProperty.EnumerateArray())
            {
                var obj = objectIndex < objectsList.Count ? objectsList[objectIndex] : objectsList.FirstOrDefault();
                if (obj != null)
                {
                    await ProcessObjectMetadataFromElement(objectElement, obj, objectMetadataList, cancellationToken);
                }
                objectIndex++;
            }
        }
        return objectIndex;
    }

    /// <summary>
    /// Process object metadata from feature object
    /// </summary>
    private async Task<int> ProcessObjectMetadataFromFeatureObject(
        object featureObj,
        List<Domain.Entities.Object> objectsList,
        int objectIndex,
        List<Dictionary<string, object?>> objectMetadataList,
        CancellationToken cancellationToken)
    {
        var featureDict = ObjectToDictionary(featureObj);
        if (featureDict.ContainsKey("objects") && featureDict["objects"] is IEnumerable<object> objectItems)
        {
            foreach (var objectObj in objectItems)
            {
                var obj = objectIndex < objectsList.Count ? objectsList[objectIndex] : objectsList.FirstOrDefault();
                if (obj != null)
                {
                    var objectDict = ObjectToDictionary(objectObj);
                    if (objectDict.ContainsKey("metadata") && objectDict["metadata"] is IEnumerable<object> metadataItems)
                    {
                        foreach (var metaObj in metadataItems)
                        {
                            var metaDict = ObjectToDictionary(metaObj);
                            await ProcessSingleObjectMetadata(metaDict, obj.Id, objectMetadataList, cancellationToken);
                        }
                    }
                }
                objectIndex++;
            }
        }
        return objectIndex;
    }

    /// <summary>
    /// Process object metadata from JsonElement
    /// </summary>
    private async Task ProcessObjectMetadataFromElement(
        System.Text.Json.JsonElement objectElement,
        Domain.Entities.Object obj,
        List<Dictionary<string, object?>> objectMetadataList,
        CancellationToken cancellationToken)
    {
        // First check for explicit metadata array (old format)
        if (objectElement.TryGetProperty("metadata", out var metadataProperty) && metadataProperty.ValueKind == System.Text.Json.JsonValueKind.Array)
        {
            foreach (var metaElement in metadataProperty.EnumerateArray())
            {
                var metaDict = JsonElementToDictionary(metaElement);
                await ProcessSingleObjectMetadata(metaDict, obj.Id, objectMetadataList, cancellationToken);
            }
        }

        // Also check for auto-generated metadata from flat properties (new format)
        if (objectElement.TryGetProperty("_AutoMetadata", out var autoMetadataProperty) && autoMetadataProperty.ValueKind == System.Text.Json.JsonValueKind.Array)
        {
            foreach (var metaElement in autoMetadataProperty.EnumerateArray())
            {
                var metaDict = JsonElementToDictionary(metaElement);
                await ProcessSingleObjectMetadata(metaDict, obj.Id, objectMetadataList, cancellationToken);
            }
        }

        // Always auto-generate metadata from flat properties (in addition to explicit metadata)
        await ProcessFlatPropertiesAsMetadata(objectElement, obj.Id, objectMetadataList, cancellationToken);
    }

    /// <summary>
    /// Process flat properties as metadata automatically
    /// </summary>
    private async Task ProcessFlatPropertiesAsMetadata(
        System.Text.Json.JsonElement objectElement,
        Guid objectId,
        List<Dictionary<string, object?>> objectMetadataList,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing flat properties as metadata for object {ObjectId}", objectId);

        // Skip system properties that shouldn't become metadata
        var skipProperties = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "id", "name", "description", "isActive", "featureId", "parentObjectId",
            "createdAt", "createdBy", "modifiedAt", "modifiedBy", "isDeleted"
        };

        var propertyCount = 0;
        foreach (var property in objectElement.EnumerateObject())
        {
            if (skipProperties.Contains(property.Name))
            {
                _logger.LogDebug("Skipping system property: {PropertyName}", property.Name);
                continue;
            }

            propertyCount++;
            _logger.LogDebug("Processing property {PropertyName} as metadata", property.Name);

            // Create metadata for this property
            var metadataDict = new Dictionary<string, object?>
            {
                ["metadataId"] = property.Name, // Use property name as metadata key
                ["isUnique"] = false,
                ["isActive"] = true
            };

            await ProcessSingleObjectMetadata(metadataDict, objectId, objectMetadataList, cancellationToken);
        }

        _logger.LogInformation("Processed {PropertyCount} flat properties as metadata for object {ObjectId}", propertyCount, objectId);
    }

    /// <summary>
    /// Process a single object metadata item
    /// </summary>
    private async Task ProcessSingleObjectMetadata(
        Dictionary<string, object?> metaDict,
        Guid objectId,
        List<Dictionary<string, object?>> objectMetadataList,
        CancellationToken cancellationToken)
    {
        try
        {
            // Extract metadataId from the input (this is actually a key, not an ID)
            var metadataKey = metaDict.ContainsKey("metadataId") ? metaDict["metadataId"]?.ToString() : null;

            if (string.IsNullOrEmpty(metadataKey))
            {
                _logger.LogWarning("Object metadata item missing metadataId, skipping");
                return;
            }

            // Get or create the Metadata record
            var metadataId = await GetOrCreateMetadata(metadataKey, cancellationToken);

            if (metadataId == null)
            {
                _logger.LogWarning("Failed to create or find metadata for key {MetadataKey}", metadataKey);
                return;
            }

            // Create ObjectMetadata record
            var objectMetadataDict = new Dictionary<string, object?>
            {
                ["ObjectId"] = objectId,
                ["MetadataId"] = metadataId,
                ["IsUnique"] = metaDict.ContainsKey("isUnique") ? metaDict["isUnique"] : false,
                ["IsActive"] = metaDict.ContainsKey("isActive") ? metaDict["isActive"] : true,
                ["ShouldVisibleInList"] = true,
                ["ShouldVisibleInEdit"] = true,
                ["ShouldVisibleInCreate"] = true,
                ["ShouldVisibleInView"] = true,
                ["IsCalculate"] = true
            };

            objectMetadataList.Add(objectMetadataDict);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing single object metadata");
        }
    }

    /// <summary>
    /// Process all related values (Product, Feature, Object)
    /// </summary>
    private async Task ProcessRelatedValues(
        DynamicProductOperationCommand request,
        IEnumerable<Product> products,
        DynamicOperationResponse response,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting values processing for {ProductCount} products", products.Count());

        // Process product values
        _logger.LogInformation("Processing product values...");
        await ProcessProductValues(request, products, response, cancellationToken);

        // Process feature values
        _logger.LogInformation("Processing feature values...");
        await ProcessFeatureValues(request, response, cancellationToken);

        // Process object values
        _logger.LogInformation("Processing object values...");
        await ProcessObjectValues(request, response, cancellationToken);

        _logger.LogInformation("Completed values processing");
    }

    /// <summary>
    /// Process product values from the request data
    /// </summary>
    private async Task ProcessProductValues(
        DynamicProductOperationCommand request,
        IEnumerable<Product> products,
        DynamicOperationResponse response,
        CancellationToken cancellationToken)
    {
        var productValuesList = new List<Dictionary<string, object?>>();
        var productsList = products.ToList();

        for (int i = 0; i < request.ProductData.Count; i++)
        {
            var productData = request.ProductData[i];
            var product = i < productsList.Count ? productsList[i] : productsList.FirstOrDefault();

            if (product == null) continue;

            // Check for values in product data
            var valuesKey = productData.Keys.FirstOrDefault(k => k.Equals("values", StringComparison.OrdinalIgnoreCase));

            if (valuesKey != null && productData[valuesKey] != null)
            {
                _logger.LogInformation("Found values data for product {ProductId}", product.Id);
                try
                {
                    var valuesData = productData[valuesKey];

                    if (valuesData is System.Text.Json.JsonElement jsonElement && jsonElement.ValueKind == System.Text.Json.JsonValueKind.Array)
                    {
                        _logger.LogInformation("Processing {Count} product values from JsonElement", jsonElement.GetArrayLength());
                        foreach (var valueElement in jsonElement.EnumerateArray())
                        {
                            var valueDict = JsonElementToDictionary(valueElement);
                            await ProcessSingleProductValue(valueDict, product.Id, productValuesList, cancellationToken);
                        }
                    }
                    else if (valuesData is IEnumerable<object> valueItems)
                    {
                        var valueItemsList = valueItems.ToList();
                        _logger.LogInformation("Processing {Count} product values from object enumerable", valueItemsList.Count);
                        foreach (var valueObj in valueItemsList)
                        {
                            var valueDict = ObjectToDictionary(valueObj);
                            await ProcessSingleProductValue(valueDict, product.Id, productValuesList, cancellationToken);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing product values for product {ProductId}", product.Id);
                }
            }
            else
            {
                _logger.LogDebug("No values found for product {ProductId}", product.Id);
            }
        }

        if (productValuesList.Any())
        {
            _logger.LogInformation("Processing {Count} product values", productValuesList.Count);

            // Process each value item individually
            var processedValueIds = new List<Guid>();
            foreach (var valueDict in productValuesList)
            {
                try
                {
                    var processedValues = await _productValueRepository.DynamicBulkUpsertAsync(new[] { valueDict }, cancellationToken);
                    var value = processedValues.FirstOrDefault();
                    if (value != null)
                    {
                        processedValueIds.Add(value.Id);
                        response.InsertedCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to process individual product value item");
                }
            }

            response.CreatedIds.AddRange(processedValueIds);
        }
    }

    /// <summary>
    /// Process a single product value item
    /// </summary>
    private async Task ProcessSingleProductValue(
        Dictionary<string, object?> valueDict,
        Guid productId,
        List<Dictionary<string, object?>> productValuesList,
        CancellationToken cancellationToken)
    {
        try
        {
            // Extract metadataKey from the input
            var metadataKey = valueDict.ContainsKey("metadataKey") ? valueDict["metadataKey"]?.ToString() : null;
            var value = valueDict.ContainsKey("value") ? valueDict["value"]?.ToString() : null;
            var refId = valueDict.ContainsKey("refId") ? valueDict["refId"] : null;

            _logger.LogInformation("Processing product value: metadataKey={MetadataKey}, value={Value}, refId={RefId}", metadataKey, value, refId);

            if (string.IsNullOrEmpty(metadataKey))
            {
                _logger.LogWarning("Product value item missing metadataKey, skipping");
                return;
            }

            // Find the ProductMetadata record
            _logger.LogInformation("Looking up ProductMetadata for product {ProductId} and metadata key {MetadataKey}", productId, metadataKey);
            var productMetadata = await GetProductMetadataByKey(productId, metadataKey, cancellationToken);

            if (productMetadata == null)
            {
                _logger.LogWarning("ProductMetadata not found for product {ProductId} and metadata key {MetadataKey}", productId, metadataKey);
                return;
            }

            _logger.LogInformation("Found ProductMetadata {ProductMetadataId} for product {ProductId} and metadata key {MetadataKey}", productMetadata.Id, productId, metadataKey);

            // Create ProductValue record
            var productValueDict = new Dictionary<string, object?>
            {
                ["ProductMetadataId"] = productMetadata.Id,
                ["RefId"] = refId,
                ["Value"] = value
            };

            productValuesList.Add(productValueDict);
            _logger.LogInformation("Added ProductValue to processing list");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing single product value");
        }
    }

    /// <summary>
    /// Get ProductMetadata by product ID and metadata key
    /// </summary>
    private async Task<Domain.Entities.ProductMetadata?> GetProductMetadataByKey(
        Guid productId,
        string metadataKey,
        CancellationToken cancellationToken)
    {
        try
        {
            var productMetadataList = await _productMetadataRepository.ListAsync(cancellationToken);
            var metadataList = await _metadataRepository.ListAsync(cancellationToken);

            var metadata = metadataList.FirstOrDefault(m => m.MetadataKey == metadataKey);
            if (metadata == null) return null;

            return productMetadataList.FirstOrDefault(pm => pm.ProductId == productId && pm.MetadataId == metadata.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting ProductMetadata for product {ProductId} and key {MetadataKey}", productId, metadataKey);
            return null;
        }
    }

    /// <summary>
    /// Process feature values from the request data
    /// </summary>
    private async Task ProcessFeatureValues(
        DynamicProductOperationCommand request,
        DynamicOperationResponse response,
        CancellationToken cancellationToken)
    {
        var featureValuesList = new List<Dictionary<string, object?>>();

        for (int i = 0; i < request.ProductData.Count; i++)
        {
            var productData = request.ProductData[i];

            // Check for features that contain values
            var featuresKey = productData.Keys.FirstOrDefault(k => k.Equals("features", StringComparison.OrdinalIgnoreCase));

            if (featuresKey != null && productData[featuresKey] != null)
            {
                try
                {
                    var featuresData = productData[featuresKey];

                    if (featuresData is System.Text.Json.JsonElement jsonElement && jsonElement.ValueKind == System.Text.Json.JsonValueKind.Array)
                    {
                        var featureIndex = 0;
                        foreach (var featureElement in jsonElement.EnumerateArray())
                        {
                            var feature = featureIndex < _processedFeatures.Count ? _processedFeatures[featureIndex] : _processedFeatures.FirstOrDefault();
                            if (feature != null)
                            {
                                await ProcessFeatureValuesFromElement(featureElement, feature, featureValuesList, cancellationToken);
                            }
                            featureIndex++;
                        }
                    }
                    else if (featuresData is IEnumerable<object> featuresList)
                    {
                        var featureIndex = 0;
                        foreach (var featureObj in featuresList)
                        {
                            var feature = featureIndex < _processedFeatures.Count ? _processedFeatures[featureIndex] : _processedFeatures.FirstOrDefault();
                            if (feature != null)
                            {
                                await ProcessFeatureValuesFromObject(featureObj, feature, featureValuesList, cancellationToken);
                            }
                            featureIndex++;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing feature values");
                }
            }
        }

        if (featureValuesList.Any())
        {
            _logger.LogInformation("Processing {Count} feature values", featureValuesList.Count);

            // Process each value item individually
            var processedValueIds = new List<Guid>();
            foreach (var valueDict in featureValuesList)
            {
                try
                {
                    var processedValues = await _featureValueRepository.DynamicBulkUpsertAsync(new[] { valueDict }, cancellationToken);
                    var value = processedValues.FirstOrDefault();
                    if (value != null)
                    {
                        processedValueIds.Add(value.Id);
                        response.InsertedCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to process individual feature value item");
                }
            }

            response.CreatedIds.AddRange(processedValueIds);
        }
    }

    /// <summary>
    /// Process feature values from JsonElement
    /// </summary>
    private async Task ProcessFeatureValuesFromElement(
        System.Text.Json.JsonElement featureElement,
        Feature feature,
        List<Dictionary<string, object?>> featureValuesList,
        CancellationToken cancellationToken)
    {
        if (featureElement.TryGetProperty("values", out var valuesProperty) && valuesProperty.ValueKind == System.Text.Json.JsonValueKind.Array)
        {
            foreach (var valueElement in valuesProperty.EnumerateArray())
            {
                var valueDict = JsonElementToDictionary(valueElement);
                await ProcessSingleFeatureValue(valueDict, feature.Id, featureValuesList, cancellationToken);
            }
        }
    }

    /// <summary>
    /// Process feature values from object
    /// </summary>
    private async Task ProcessFeatureValuesFromObject(
        object featureObj,
        Feature feature,
        List<Dictionary<string, object?>> featureValuesList,
        CancellationToken cancellationToken)
    {
        var featureDict = ObjectToDictionary(featureObj);
        if (featureDict.ContainsKey("values") && featureDict["values"] is IEnumerable<object> valueItems)
        {
            foreach (var valueObj in valueItems)
            {
                var valueDict = ObjectToDictionary(valueObj);
                await ProcessSingleFeatureValue(valueDict, feature.Id, featureValuesList, cancellationToken);
            }
        }
    }

    /// <summary>
    /// Process a single feature value item
    /// </summary>
    private async Task ProcessSingleFeatureValue(
        Dictionary<string, object?> valueDict,
        Guid featureId,
        List<Dictionary<string, object?>> featureValuesList,
        CancellationToken cancellationToken)
    {
        try
        {
            // Extract metadataKey from the input
            var metadataKey = valueDict.ContainsKey("metadataKey") ? valueDict["metadataKey"]?.ToString() : null;
            var value = valueDict.ContainsKey("value") ? valueDict["value"]?.ToString() : null;
            var refId = valueDict.ContainsKey("refId") ? valueDict["refId"] : null;

            if (string.IsNullOrEmpty(metadataKey))
            {
                _logger.LogWarning("Feature value item missing metadataKey, skipping");
                return;
            }

            // Find the FeatureMetadata record
            var featureMetadata = await GetFeatureMetadataByKey(featureId, metadataKey, cancellationToken);

            if (featureMetadata == null)
            {
                _logger.LogWarning("FeatureMetadata not found for feature {FeatureId} and metadata key {MetadataKey}", featureId, metadataKey);
                return;
            }

            // Create FeatureValue record
            var featureValueDict = new Dictionary<string, object?>
            {
                ["FeatureMetadataId"] = featureMetadata.Id,
                ["RefId"] = refId,
                ["Value"] = value
            };

            featureValuesList.Add(featureValueDict);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing single feature value");
        }
    }

    /// <summary>
    /// Get FeatureMetadata by feature ID and metadata key
    /// </summary>
    private async Task<Domain.Entities.FeatureMetadata?> GetFeatureMetadataByKey(
        Guid featureId,
        string metadataKey,
        CancellationToken cancellationToken)
    {
        try
        {
            var featureMetadataList = await _featureMetadataRepository.ListAsync(cancellationToken);
            var metadataList = await _metadataRepository.ListAsync(cancellationToken);

            var metadata = metadataList.FirstOrDefault(m => m.MetadataKey == metadataKey);
            if (metadata == null) return null;

            return featureMetadataList.FirstOrDefault(fm => fm.FeatureId == featureId && fm.MetadataId == metadata.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting FeatureMetadata for feature {FeatureId} and key {MetadataKey}", featureId, metadataKey);
            return null;
        }
    }

    /// <summary>
    /// Process object values from the request data
    /// </summary>
    private async Task ProcessObjectValues(
        DynamicProductOperationCommand request,
        DynamicOperationResponse response,
        CancellationToken cancellationToken)
    {
        var objectValuesList = new List<Dictionary<string, object?>>();

        for (int i = 0; i < request.ProductData.Count; i++)
        {
            var productData = request.ProductData[i];

            // Check for features that contain objects with values
            var featuresKey = productData.Keys.FirstOrDefault(k => k.Equals("features", StringComparison.OrdinalIgnoreCase));

            if (featuresKey != null && productData[featuresKey] != null)
            {
                try
                {
                    var featuresData = productData[featuresKey];

                    if (featuresData is System.Text.Json.JsonElement jsonElement && jsonElement.ValueKind == System.Text.Json.JsonValueKind.Array)
                    {
                        var objectIndex = 0;
                        foreach (var featureElement in jsonElement.EnumerateArray())
                        {
                            objectIndex = await ProcessObjectValuesFromFeatureElement(featureElement, _processedObjects, objectIndex, objectValuesList, cancellationToken);
                        }
                    }
                    else if (featuresData is IEnumerable<object> featuresList)
                    {
                        var objectIndex = 0;
                        foreach (var featureObj in featuresList)
                        {
                            objectIndex = await ProcessObjectValuesFromFeatureObject(featureObj, _processedObjects, objectIndex, objectValuesList, cancellationToken);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing object values");
                }
            }

            // Also check for direct objects with values in product data
            var objectsKey = productData.Keys.FirstOrDefault(k => k.Equals("objects", StringComparison.OrdinalIgnoreCase));
            if (objectsKey != null && productData[objectsKey] != null)
            {
                try
                {
                    var objectsData = productData[objectsKey];
                    if (objectsData is System.Text.Json.JsonElement jsonElement && jsonElement.ValueKind == System.Text.Json.JsonValueKind.Array)
                    {
                        var objectIndex = 0;
                        foreach (var objectElement in jsonElement.EnumerateArray())
                        {
                            var obj = objectIndex < _processedObjects.Count ? _processedObjects[objectIndex] : _processedObjects.FirstOrDefault();
                            if (obj != null)
                            {
                                await ProcessObjectValuesFromElement(objectElement, obj, objectValuesList, cancellationToken);
                            }
                            objectIndex++;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing direct object values");
                }
            }
        }

        if (objectValuesList.Any())
        {
            _logger.LogInformation("Processing {Count} object values", objectValuesList.Count);

            // Process each value item individually
            var processedValueIds = new List<Guid>();
            foreach (var valueDict in objectValuesList)
            {
                try
                {
                    var processedValues = await _objectValueRepository.DynamicBulkUpsertAsync(new[] { valueDict }, cancellationToken);
                    var value = processedValues.FirstOrDefault();
                    if (value != null)
                    {
                        processedValueIds.Add(value.Id);
                        response.InsertedCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to process individual object value item");
                }
            }

            response.CreatedIds.AddRange(processedValueIds);
        }
    }

    /// <summary>
    /// Process object values from feature JsonElement
    /// </summary>
    private async Task<int> ProcessObjectValuesFromFeatureElement(
        System.Text.Json.JsonElement featureElement,
        List<Domain.Entities.Object> objectsList,
        int objectIndex,
        List<Dictionary<string, object?>> objectValuesList,
        CancellationToken cancellationToken)
    {
        if (featureElement.TryGetProperty("objects", out var objectsProperty) && objectsProperty.ValueKind == System.Text.Json.JsonValueKind.Array)
        {
            foreach (var objectElement in objectsProperty.EnumerateArray())
            {
                var obj = objectIndex < objectsList.Count ? objectsList[objectIndex] : objectsList.FirstOrDefault();
                if (obj != null)
                {
                    await ProcessObjectValuesFromElement(objectElement, obj, objectValuesList, cancellationToken);
                }
                objectIndex++;
            }
        }
        return objectIndex;
    }

    /// <summary>
    /// Process object values from feature object
    /// </summary>
    private async Task<int> ProcessObjectValuesFromFeatureObject(
        object featureObj,
        List<Domain.Entities.Object> objectsList,
        int objectIndex,
        List<Dictionary<string, object?>> objectValuesList,
        CancellationToken cancellationToken)
    {
        var featureDict = ObjectToDictionary(featureObj);
        if (featureDict.ContainsKey("objects") && featureDict["objects"] is IEnumerable<object> objectItems)
        {
            foreach (var objectObj in objectItems)
            {
                var obj = objectIndex < objectsList.Count ? objectsList[objectIndex] : objectsList.FirstOrDefault();
                if (obj != null)
                {
                    var objectDict = ObjectToDictionary(objectObj);
                    if (objectDict.ContainsKey("values") && objectDict["values"] is IEnumerable<object> valueItems)
                    {
                        foreach (var valueObj in valueItems)
                        {
                            var valueDict = ObjectToDictionary(valueObj);
                            await ProcessSingleObjectValue(valueDict, obj.Id, objectValuesList, cancellationToken);
                        }
                    }
                }
                objectIndex++;
            }
        }
        return objectIndex;
    }

    /// <summary>
    /// Process object values from JsonElement
    /// </summary>
    private async Task ProcessObjectValuesFromElement(
        System.Text.Json.JsonElement objectElement,
        Domain.Entities.Object obj,
        List<Dictionary<string, object?>> objectValuesList,
        CancellationToken cancellationToken)
    {
        // First check for explicit values array (old format)
        if (objectElement.TryGetProperty("values", out var valuesProperty) && valuesProperty.ValueKind == System.Text.Json.JsonValueKind.Array)
        {
            foreach (var valueElement in valuesProperty.EnumerateArray())
            {
                var valueDict = JsonElementToDictionary(valueElement);
                await ProcessSingleObjectValue(valueDict, obj.Id, objectValuesList, cancellationToken);
            }
        }

        // Also check for auto-generated values from flat properties (new format)
        if (objectElement.TryGetProperty("_AutoValues", out var autoValuesProperty) && autoValuesProperty.ValueKind == System.Text.Json.JsonValueKind.Array)
        {
            foreach (var valueElement in autoValuesProperty.EnumerateArray())
            {
                var valueDict = JsonElementToDictionary(valueElement);
                await ProcessSingleObjectValue(valueDict, obj.Id, objectValuesList, cancellationToken);
            }
        }

        // Always auto-generate values from flat properties (in addition to explicit values)
        await ProcessFlatPropertiesAsValues(objectElement, obj.Id, objectValuesList, cancellationToken);
    }

    /// <summary>
    /// Process flat properties as values automatically
    /// </summary>
    private async Task ProcessFlatPropertiesAsValues(
        System.Text.Json.JsonElement objectElement,
        Guid objectId,
        List<Dictionary<string, object?>> objectValuesList,
        CancellationToken cancellationToken)
    {
        // Skip system properties that shouldn't become values
        var skipProperties = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "id", "name", "description", "isActive", "featureId", "parentObjectId",
            "createdAt", "createdBy", "modifiedAt", "modifiedBy", "isDeleted"
        };

        // Get the refId from the object's id property if available
        var refId = objectElement.TryGetProperty("id", out var idProperty) ? idProperty.GetString() : null;

        foreach (var property in objectElement.EnumerateObject())
        {
            if (skipProperties.Contains(property.Name))
                continue;

            // Create value for this property
            var valueDict = new Dictionary<string, object?>
            {
                ["metadataKey"] = property.Name,
                ["value"] = GetJsonElementValue(property.Value),
                ["refId"] = refId
            };

            await ProcessSingleObjectValue(valueDict, objectId, objectValuesList, cancellationToken);
        }
    }

    /// <summary>
    /// Process a single object value item
    /// </summary>
    private async Task ProcessSingleObjectValue(
        Dictionary<string, object?> valueDict,
        Guid objectId,
        List<Dictionary<string, object?>> objectValuesList,
        CancellationToken cancellationToken)
    {
        try
        {
            // Extract metadataKey from the input
            var metadataKey = valueDict.ContainsKey("metadataKey") ? valueDict["metadataKey"]?.ToString() : null;
            var value = valueDict.ContainsKey("value") ? valueDict["value"]?.ToString() : null;
            var refId = valueDict.ContainsKey("refId") ? valueDict["refId"] : null;
            var parentObjectValueId = valueDict.ContainsKey("parentObjectValueId") ? valueDict["parentObjectValueId"] : null;

            if (string.IsNullOrEmpty(metadataKey))
            {
                _logger.LogWarning("Object value item missing metadataKey, skipping");
                return;
            }

            // Find the ObjectMetadata record
            var objectMetadata = await GetObjectMetadataByKey(objectId, metadataKey, cancellationToken);

            if (objectMetadata == null)
            {
                _logger.LogWarning("ObjectMetadata not found for object {ObjectId} and metadata key {MetadataKey}", objectId, metadataKey);
                return;
            }

            // Create ObjectValue record
            var objectValueDict = new Dictionary<string, object?>
            {
                ["ObjectMetadataId"] = objectMetadata.Id,
                ["RefId"] = refId,
                ["ParentObjectValueId"] = parentObjectValueId,
                ["Value"] = value
            };

            objectValuesList.Add(objectValueDict);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing single object value");
        }
    }

    /// <summary>
    /// Get ObjectMetadata by object ID and metadata key
    /// </summary>
    private async Task<Domain.Entities.ObjectMetadata?> GetObjectMetadataByKey(
        Guid objectId,
        string metadataKey,
        CancellationToken cancellationToken)
    {
        try
        {
            var objectMetadataList = await _objectMetadataRepository.ListAsync(cancellationToken);
            var metadataList = await _metadataRepository.ListAsync(cancellationToken);

            var metadata = metadataList.FirstOrDefault(m => m.MetadataKey == metadataKey);
            if (metadata == null) return null;

            return objectMetadataList.FirstOrDefault(om => om.ObjectId == objectId && om.MetadataId == metadata.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting ObjectMetadata for object {ObjectId} and key {MetadataKey}", objectId, metadataKey);
            return null;
        }
    }

    /// <summary>
    /// Process project plan data - create one Product, one Feature, one Object with all units as metadata and values
    /// </summary>
    private async Task ProcessProjectPlanData(
        DynamicProductOperationCommand request,
        DynamicOperationResponse response,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting project plan data processing...");

        try
        {
            // Check if agenttracking.json exists for Agent Tracking processing
            var agentTrackingPath = Path.Combine("Shared", "Common", "agenttracking.json");
            if (File.Exists(agentTrackingPath))
            {
                await ProcessAgentTrackingData(response, cancellationToken);
                return;
            }

            // Default to Inventory Management processing
            await ProcessInventoryManagementData(response, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing project plan data");
            throw;
        }
    }

    /// <summary>
    /// Process Inventory Management data from projectplan.json
    /// </summary>
    private async Task ProcessInventoryManagementData(
        DynamicOperationResponse response,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing Inventory Management data...");

        // Step 1: Create the single Product
        var productData = new Dictionary<string, object?>
        {
            ["name"] = "Inventory Management",
            ["description"] = "Real Estate Inventory Management System",
            ["version"] = "1.0.0",
            ["isActive"] = true
        };

        var product = await _productRepository.DynamicUpsertAsync(productData, cancellationToken);
        response.InsertedCount++;
        response.CreatedIds.Add(product.Id);
        _logger.LogInformation("Created Product: {ProductId}", product.Id);

        // Step 2: Create the single Feature
        var featureData = new Dictionary<string, object?>
        {
            ["name"] = "Unit Inventory",
            ["description"] = "Real Estate Unit Inventory Management",
            ["isDefault"] = true,
            ["isActive"] = true,
            ["ProductId"] = product.Id
        };

        var feature = await _featureRepository.DynamicUpsertAsync(featureData, cancellationToken);
        response.InsertedCount++;
        response.CreatedIds.Add(feature.Id);
        _logger.LogInformation("Created Feature: {FeatureId}", feature.Id);

        // Step 3: Create the single Object for "Units"
        var objectData = new Dictionary<string, object?>
        {
            ["name"] = "Units",
            ["description"] = "Real Estate Units Collection",
            ["isActive"] = true,
            ["FeatureId"] = feature.Id
        };

        var obj = await _objectRepository.DynamicUpsertAsync(objectData, cancellationToken);
        response.InsertedCount++;
        response.CreatedIds.Add(obj.Id);
        _logger.LogInformation("Created Object: {ObjectId}", obj.Id);

        // Step 4: Process all unit data to extract metadata and values
        await ProcessUnitsMetadataAndValues(obj.Id, response, cancellationToken);
    }

    /// <summary>
    /// Process Agent Tracking data from agenttracking.json
    /// </summary>
    private async Task ProcessAgentTrackingData(
        DynamicOperationResponse response,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing Agent Tracking data...");

        // Step 1: Create the single Product
        var productData = new Dictionary<string, object?>
        {
            ["name"] = "Agent Tracking",
            ["description"] = "Agent Communication Tracking System",
            ["version"] = "1.0.0",
            ["isActive"] = true
        };

        var product = await _productRepository.DynamicUpsertAsync(productData, cancellationToken);
        response.InsertedCount++;
        response.CreatedIds.Add(product.Id);
        _logger.LogInformation("Created Product: {ProductId}", product.Id);

        // Step 2: Create the single Feature
        var featureData = new Dictionary<string, object?>
        {
            ["name"] = "Agent",
            ["description"] = "Agent Communication Management",
            ["isDefault"] = true,
            ["isActive"] = true,
            ["ProductId"] = product.Id
        };

        var feature = await _featureRepository.DynamicUpsertAsync(featureData, cancellationToken);
        response.InsertedCount++;
        response.CreatedIds.Add(feature.Id);
        _logger.LogInformation("Created Feature: {FeatureId}", feature.Id);

        // Step 3: Create the single Object for "Communication"
        var objectData = new Dictionary<string, object?>
        {
            ["name"] = "Communication",
            ["description"] = "Agent Communication Records Collection",
            ["isActive"] = true,
            ["FeatureId"] = feature.Id
        };

        var obj = await _objectRepository.DynamicUpsertAsync(objectData, cancellationToken);
        response.InsertedCount++;
        response.CreatedIds.Add(obj.Id);
        _logger.LogInformation("Created Object: {ObjectId}", obj.Id);

        // Step 4: Process all communication data to extract metadata and values
        await ProcessCommunicationMetadataAndValues(obj.Id, response, cancellationToken);
    }

    /// <summary>
    /// Process units metadata and values from projectplan.json
    /// </summary>
    private async Task ProcessUnitsMetadataAndValues(
        Guid objectId,
        DynamicOperationResponse response,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing units metadata and values for object {ObjectId}", objectId);

        try
        {
            // Read the projectplan.json file
            var projectPlanPath = Path.Combine("..", "Shared", "Common", "projectplan.json");
            if (!File.Exists(projectPlanPath))
            {
                _logger.LogWarning("Project plan file not found at {Path}", projectPlanPath);
                return;
            }

            var jsonContent = await File.ReadAllTextAsync(projectPlanPath, cancellationToken);
            var projectPlan = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(jsonContent);

            if (projectPlan == null || !projectPlan.ContainsKey("units"))
            {
                _logger.LogWarning("No units found in project plan");
                return;
            }

            var unitsElement = (System.Text.Json.JsonElement)projectPlan["units"];
            if (unitsElement.ValueKind != System.Text.Json.JsonValueKind.Array)
            {
                _logger.LogWarning("Units is not an array in project plan");
                return;
            }

            var units = unitsElement.EnumerateArray().ToList();
            _logger.LogInformation("Found {UnitCount} units in project plan", units.Count);

            // Step 1: Extract all unique property names from all units to create metadata
            var allPropertyNames = new HashSet<string>();
            foreach (var unit in units)
            {
                foreach (var property in unit.EnumerateObject())
                {
                    allPropertyNames.Add(property.Name);
                }
            }

            _logger.LogInformation("Found {PropertyCount} unique properties: {Properties}",
                allPropertyNames.Count, string.Join(", ", allPropertyNames));

            // Step 2: Create metadata for each unique property
            var metadataMap = new Dictionary<string, Guid>();
            foreach (var propertyName in allPropertyNames)
            {
                var metadataId = await CreateMetadataForProperty(propertyName, cancellationToken);
                if (metadataId.HasValue)
                {
                    metadataMap[propertyName] = metadataId.Value;
                    response.InsertedCount++;
                    response.CreatedIds.Add(metadataId.Value);
                }
            }

            _logger.LogInformation("Created {MetadataCount} metadata entries", metadataMap.Count);

            // Step 3: Create ObjectMetadata linking the object to each metadata
            foreach (var kvp in metadataMap)
            {
                var objectMetadataData = new Dictionary<string, object?>
                {
                    ["ObjectId"] = objectId,
                    ["MetadataId"] = kvp.Value
                };

                var objectMetadata = await _objectMetadataRepository.DynamicUpsertAsync(objectMetadataData, cancellationToken);
                response.InsertedCount++;
                response.CreatedIds.Add(objectMetadata.Id);
            }

            _logger.LogInformation("Created {ObjectMetadataCount} ObjectMetadata entries", metadataMap.Count);

            // Step 4: Create ObjectValues for each unit's property values
            var valueCount = 0;
            foreach (var unit in units)
            {
                foreach (var property in unit.EnumerateObject())
                {
                    if (metadataMap.TryGetValue(property.Name, out var metadataId))
                    {
                        var objectMetadata = await GetObjectMetadataByObjectAndMetadata(objectId, metadataId, cancellationToken);
                        if (objectMetadata != null)
                        {
                            var valueData = new Dictionary<string, object?>
                            {
                                ["ObjectMetadataId"] = objectMetadata.Id,
                                ["Value"] = JsonElementToObject(property.Value)?.ToString() ?? ""
                            };

                            var objectValue = await _objectValueRepository.DynamicUpsertAsync(valueData, cancellationToken);
                            response.InsertedCount++;
                            response.CreatedIds.Add(objectValue.Id);
                            valueCount++;
                        }
                    }
                }
            }

            _logger.LogInformation("Created {ValueCount} ObjectValue entries", valueCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing units metadata and values");
            throw;
        }
    }

    /// <summary>
    /// Process communication metadata and values from agenttracking.json
    /// </summary>
    private async Task ProcessCommunicationMetadataAndValues(
        Guid objectId,
        DynamicOperationResponse response,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing communication metadata and values for object {ObjectId}", objectId);

        try
        {
            // Read the agenttracking.json file
            var agentTrackingPath = Path.Combine("Shared", "Common", "agenttracking.json");
            if (!File.Exists(agentTrackingPath))
            {
                _logger.LogWarning("Agent tracking file not found at {Path}", agentTrackingPath);
                return;
            }

            var jsonContent = await File.ReadAllTextAsync(agentTrackingPath, cancellationToken);
            var agentTracking = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(jsonContent);

            if (agentTracking == null || !agentTracking.ContainsKey("communication"))
            {
                _logger.LogWarning("No communication found in agent tracking");
                return;
            }

            var communicationElement = (System.Text.Json.JsonElement)agentTracking["communication"];
            if (communicationElement.ValueKind != System.Text.Json.JsonValueKind.Array)
            {
                _logger.LogWarning("Communication is not an array in agent tracking");
                return;
            }

            var communications = communicationElement.EnumerateArray().ToList();
            _logger.LogInformation("Found {CommunicationCount} communications in agent tracking", communications.Count);

            // Step 1: Extract all unique property names from all communications to create metadata
            var allPropertyNames = new HashSet<string>();
            foreach (var communication in communications)
            {
                foreach (var property in communication.EnumerateObject())
                {
                    allPropertyNames.Add(property.Name);
                }
            }

            _logger.LogInformation("Found {PropertyCount} unique properties: {Properties}",
                allPropertyNames.Count, string.Join(", ", allPropertyNames));

            // Step 2: Create metadata for each unique property
            var metadataMap = new Dictionary<string, Guid>();
            foreach (var propertyName in allPropertyNames)
            {
                var metadataId = await CreateMetadataForProperty(propertyName, cancellationToken);
                if (metadataId.HasValue)
                {
                    metadataMap[propertyName] = metadataId.Value;
                    response.InsertedCount++;
                    response.CreatedIds.Add(metadataId.Value);
                }
            }

            _logger.LogInformation("Created {MetadataCount} metadata entries", metadataMap.Count);

            // Step 3: Create ObjectMetadata linking the object to each metadata
            foreach (var kvp in metadataMap)
            {
                var objectMetadataData = new Dictionary<string, object?>
                {
                    ["ObjectId"] = objectId,
                    ["MetadataId"] = kvp.Value
                };

                var objectMetadata = await _objectMetadataRepository.DynamicUpsertAsync(objectMetadataData, cancellationToken);
                response.InsertedCount++;
                response.CreatedIds.Add(objectMetadata.Id);
            }

            _logger.LogInformation("Created {ObjectMetadataCount} ObjectMetadata entries", metadataMap.Count);

            // Step 4: Create ObjectValues for each communication's property values
            var valueCount = 0;
            foreach (var communication in communications)
            {
                foreach (var property in communication.EnumerateObject())
                {
                    if (metadataMap.TryGetValue(property.Name, out var metadataId))
                    {
                        var objectMetadata = await GetObjectMetadataByObjectAndMetadata(objectId, metadataId, cancellationToken);
                        if (objectMetadata != null)
                        {
                            var valueData = new Dictionary<string, object?>
                            {
                                ["ObjectMetadataId"] = objectMetadata.Id,
                                ["Value"] = JsonElementToObject(property.Value)?.ToString() ?? ""
                            };

                            var objectValue = await _objectValueRepository.DynamicUpsertAsync(valueData, cancellationToken);
                            response.InsertedCount++;
                            response.CreatedIds.Add(objectValue.Id);
                            valueCount++;
                        }
                    }
                }
            }

            _logger.LogInformation("Created {ValueCount} ObjectValue entries", valueCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing communication metadata and values");
            throw;
        }
    }

    /// <summary>
    /// Create metadata for a property name
    /// </summary>
    private async Task<Guid?> CreateMetadataForProperty(string propertyName, CancellationToken cancellationToken)
    {
        try
        {
            // First check if metadata with this key already exists
            var existingMetadataList = await _metadataRepository.ListAsync(cancellationToken);
            var existingMetadata = existingMetadataList.FirstOrDefault(m => m.MetadataKey.Equals(propertyName, StringComparison.OrdinalIgnoreCase));

            if (existingMetadata != null)
            {
                _logger.LogDebug("Metadata for property '{PropertyName}' already exists with ID {MetadataId}", propertyName, existingMetadata.Id);
                return existingMetadata.Id;
            }

            // Determine data type based on property name patterns
            var dataTypeName = DetermineDataTypeFromPropertyName(propertyName);

            // Get the DataType entity
            var dataTypeList = await _dataTypeRepository.ListAsync(cancellationToken);
            var dataType = dataTypeList.FirstOrDefault(dt => dt.Name.Equals(dataTypeName, StringComparison.OrdinalIgnoreCase));

            if (dataType == null)
            {
                _logger.LogWarning("DataType '{DataTypeName}' not found for property '{PropertyName}'", dataTypeName, propertyName);
                return null;
            }

            // Create metadata
            var metadataData = new Dictionary<string, object?>
            {
                ["MetadataKey"] = propertyName,
                ["DataTypeId"] = dataType.Id,
                ["DisplayLabel"] = propertyName,
                ["IsVisible"] = true,
                ["IsReadonly"] = false
            };

            var metadata = await _metadataRepository.DynamicUpsertAsync(metadataData, cancellationToken);
            _logger.LogDebug("Created metadata for property '{PropertyName}' with ID {MetadataId}", propertyName, metadata.Id);

            return metadata.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating metadata for property '{PropertyName}'", propertyName);
            return null;
        }
    }

    /// <summary>
    /// Determine data type from property name patterns
    /// </summary>
    private string DetermineDataTypeFromPropertyName(string propertyName)
    {
        var lowerName = propertyName.ToLowerInvariant();

        // Numeric fields
        if (lowerName.Contains("area") || lowerName.Contains("amount") || lowerName.Contains("price") ||
            lowerName.Contains("number") || lowerName.Equals("floor") || lowerName.Equals("tower"))
        {
            return "number";
        }

        // Date fields
        if (lowerName.Contains("date"))
        {
            return "date";
        }

        // Email fields
        if (lowerName.Contains("email"))
        {
            return "email";
        }

        // Phone fields
        if (lowerName.Contains("phone") || lowerName.Contains("contact"))
        {
            return "tel";
        }

        // Default to text
        return "text";
    }

    /// <summary>
    /// Get ObjectMetadata by ObjectId and MetadataId
    /// </summary>
    private async Task<Domain.Entities.ObjectMetadata?> GetObjectMetadataByObjectAndMetadata(
        Guid objectId,
        Guid metadataId,
        CancellationToken cancellationToken)
    {
        try
        {
            var objectMetadataList = await _objectMetadataRepository.ListAsync(cancellationToken);
            return objectMetadataList.FirstOrDefault(om => om.ObjectId == objectId && om.MetadataId == metadataId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting ObjectMetadata for object {ObjectId} and metadata {MetadataId}", objectId, metadataId);
            return null;
        }
    }
}
