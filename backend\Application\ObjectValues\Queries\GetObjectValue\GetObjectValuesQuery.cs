using Application.ObjectValues.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.ObjectValues.Queries.GetObjectValue;

/// <summary>
/// Get ObjectValues query
/// </summary>
public class GetObjectValuesQuery : IRequest<PaginatedResult<ObjectValueDto>>
{
    /// <summary>
    /// Page number
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Search term
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Object metadata ID filter
    /// </summary>
    public Guid? ObjectMetadataId { get; set; }

    /// <summary>
    /// Reference ID filter
    /// </summary>
    public Guid? RefId { get; set; }

    /// <summary>
    /// Parent object value ID filter
    /// </summary>
    public Guid? ParentObjectValueId { get; set; }

    /// <summary>
    /// Order by
    /// </summary>
    public string? OrderBy { get; set; }
}
