using Application.IntegrationApis.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.IntegrationApis.Queries;

/// <summary>
/// Get integration APIs query
/// </summary>
public class GetIntegrationApisQuery : IRequest<PaginatedResult<ViewIntegrationApiDto>>
{
    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Search term for name or endpoint URL
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Filter by active status
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Filter by product ID
    /// </summary>
    public Guid? ProductId { get; set; }
}

/// <summary>
/// Get integration API by ID query
/// </summary>
public class GetIntegrationApiByIdQuery : IRequest<Result<ViewIntegrationApiDto>>
{
    /// <summary>
    /// Integration API ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public GetIntegrationApiByIdQuery(Guid id)
    {
        Id = id;
    }
}
