using Application.Integrations.DTOs;
using Application.Integrations.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.Integrations.Queries;

/// <summary>
/// Get integrations query handler
/// </summary>
public class GetIntegrationsQueryHandler : IRequestHandler<GetIntegrationsQuery, PaginatedResult<IntegrationDto>>
{
    private readonly IReadRepository<Integration> _integrationRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetIntegrationsQueryHandler(IReadRepository<Integration> integrationRepository)
    {
        _integrationRepository = integrationRepository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<PaginatedResult<IntegrationDto>> Handle(GetIntegrationsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            // Calculate pagination
            var skip = (request.PageNumber - 1) * request.PageSize;

            // Create specifications for data and count
            var dataSpec = new IntegrationsWithFiltersSpec(
                request.SearchTerm,
                request.IsActive,
                request.ProductId,
                request.AuthType,
                skip,
                request.PageSize);

            var countSpec = new IntegrationsCountSpec(
                request.SearchTerm,
                request.IsActive,
                request.ProductId,
                request.AuthType);

            // Get data and count
            var integrations = await _integrationRepository.ListAsync(dataSpec, cancellationToken);
            var totalCount = await _integrationRepository.CountAsync(countSpec, cancellationToken);

            // Map to DTOs
            var integrationDtos = integrations.Adapt<List<IntegrationDto>>();

            return new PaginatedResult<IntegrationDto>(
                integrationDtos,
                request.PageNumber,
                request.PageSize,
                totalCount);
        }
        catch (Exception ex)
        {
            return PaginatedResult<IntegrationDto>.Failure($"Failed to get integrations: {ex.Message}");
        }
    }
}
