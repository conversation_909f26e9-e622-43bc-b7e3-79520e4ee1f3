using Ardalis.Specification;
using Domain.Entities;

namespace Application.Objects.Specifications;

/// <summary>
/// Specification to get Object by feature and name
/// </summary>
public class ObjectByFeatureAndNameSpec : Specification<Domain.Entities.Object>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ObjectByFeatureAndNameSpec(Guid featureId, string name)
    {
        Query.Where(obj => obj.FeatureId == featureId && obj.Name == name && !obj.IsDeleted);
    }
}
