using Application.UserValues.Commands;
using Application.UserValues.DTOs;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;

namespace Web.Host.Controllers;

/// <summary>
/// User Data controller for comprehensive user data management
/// </summary>
[Route("api/[controller]")]
public class UserDataController : BaseApiController
{
    /// <summary>
    /// Create comprehensive user data with automatic metadata and value creation
    /// </summary>
    /// <param name="command">User data command</param>
    /// <returns>Result with creation summary</returns>
    [HttpPost("create-comprehensive")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<UserDataResponseDto>>> CreateUserData(CreateUserDataCommand command)
    {
        return Ok(await Mediator.Send(command));
    }
}
