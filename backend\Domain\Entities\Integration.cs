using System.ComponentModel.DataAnnotations;
using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Integration entity - stores integration configurations for external systems
/// </summary>
public class Integration : AuditableEntity, IAggregateRoot
{
    /// <summary>
    /// Product ID this integration belongs to
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Integration name
    /// </summary>
    [MaxLength(255)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Authentication type (e.g., "OAuth2", "ApiKey", "Basic")
    /// </summary>
    [MaxLength(50)]
    public string AuthType { get; set; } = string.Empty;

    /// <summary>
    /// Authentication configuration stored as JSON
    /// </summary>
    public string AuthConfig { get; set; } = string.Empty;

    /// <summary>
    /// Whether the integration is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Sync frequency for automatic synchronization
    /// </summary>
    public TimeSpan? SyncFrequency { get; set; }

    /// <summary>
    /// Last synchronization timestamp
    /// </summary>
    public DateTime? LastSyncAt { get; set; }

    // Navigation properties
    /// <summary>
    /// Product this integration belongs to
    /// </summary>
    public virtual Product Product { get; set; } = null!;

    /// <summary>
    /// Integration configurations
    /// </summary>
    public virtual ICollection<IntegrationConfiguration> IntegrationConfigurations { get; set; } = new List<IntegrationConfiguration>();

    /// <summary>
    /// Field mappings for this integration
    /// </summary>
    public virtual ICollection<FieldMapping> FieldMappings { get; set; } = new List<FieldMapping>();

    /// <summary>
    /// Conflict resolutions for this integration
    /// </summary>
    public virtual ICollection<ConflictResolution> ConflictResolutions { get; set; } = new List<ConflictResolution>();

    /// <summary>
    /// Sync history for this integration
    /// </summary>
    public virtual ICollection<SyncHistory> SyncHistories { get; set; } = new List<SyncHistory>();
}
