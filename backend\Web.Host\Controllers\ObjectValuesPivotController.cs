using Application.ObjectValues.Commands.UpsertObjectValues;
using Application.ObjectValues.DTOs;
using Application.ObjectValues.Queries.GetObjectValuesByRefId;
using Application.ObjectValues.Queries.GetRefIdsByObjectId;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;

namespace Web.Host.Controllers;

/// <summary>
/// Controller for ObjectValues management with dynamic pivot querying and upsert functionality
/// Handles RefId-based grouping of ObjectValues for dynamic object instances
/// </summary>
[ApiController]
[Route("api/objectvalues-pivot")]
public class ObjectValuesPivotController : BaseApiController
{
    /// <summary>
    /// Get ObjectValues by RefId with complete metadata information
    /// </summary>
    /// <param name="refId">Reference ID that groups related ObjectValues</param>
    /// <param name="includeInactive">Whether to include inactive records</param>
    /// <param name="onlyVisible">Whether to include only visible metadata</param>
    /// <returns>ObjectValues response with all metadata values for the RefId</returns>
    /// <response code="200">Returns the ObjectValues for the specified RefId</response>
    /// <response code="404">If the RefId is not found</response>
    /// <response code="400">If the request parameters are invalid</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet("{refId}")]
    [TenantIdHeader]
    [ProducesResponseType(typeof(Result<ObjectValuesResponseDto>), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<Result<ObjectValuesResponseDto>>> GetObjectValuesByRefId(
        Guid refId,
        [FromQuery] bool includeInactive = false,
        [FromQuery] bool onlyVisible = true)
    {
        if (refId == Guid.Empty)
        {
            return BadRequest(Result<ObjectValuesResponseDto>.Failure("RefId cannot be empty"));
        }

        var query = new GetObjectValuesByRefIdQuery(refId, includeInactive, onlyVisible);
        var result = await Mediator.Send(query);

        if (!result.Succeeded)
        {
            return NotFound(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Get all RefIds for a specific ObjectId with pagination and search
    /// </summary>
    /// <param name="objectId">Object ID to filter by</param>
    /// <param name="searchTerm">Optional search term for values</param>
    /// <param name="onlyActive">Whether to include only active records</param>
    /// <param name="pageNumber">Page number for pagination (default: 1)</param>
    /// <param name="pageSize">Page size for pagination (default: 50, max: 1000)</param>
    /// <param name="orderBy">Order by field (LastModified, ValueCount, RefId)</param>
    /// <param name="orderDirection">Order direction (asc/desc, default: desc)</param>
    /// <returns>Paginated list of RefId summaries</returns>
    /// <response code="200">Returns the paginated RefId summaries</response>
    /// <response code="400">If the request parameters are invalid</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet("object/{objectId}")]
    [TenantIdHeader]
    [ProducesResponseType(typeof(Result<PaginatedResult<ObjectValuesRefIdSummaryDto>>), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [AllowAnonymous]
    public async Task<ActionResult<Result<PaginatedResult<ObjectValuesRefIdSummaryDto>>>> GetRefIdsByObjectId(
        Guid objectId,
        [FromQuery] string? searchTerm = null,
        [FromQuery] bool onlyActive = true,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? orderBy = null,
        [FromQuery] string orderDirection = "desc")
    {
        if (objectId == Guid.Empty)
        {
            return BadRequest(Result<PaginatedResult<ObjectValuesRefIdSummaryDto>>.Failure("ObjectId cannot be empty"));
        }

        var query = new GetRefIdsByObjectIdQuery(
            objectId,
            searchTerm,
            onlyActive,
            pageNumber,
            pageSize,
            orderBy,
            orderDirection);

        var result = await Mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Upsert ObjectValues for a specific RefId
    /// </summary>
    /// <param name="request">Upsert request with RefId, ObjectId, and values</param>
    /// <returns>Upsert response with statistics and updated data</returns>
    /// <response code="200">Returns the upsert response with updated ObjectValues</response>
    /// <response code="400">If the request is invalid</response>
    /// <response code="404">If the ObjectId is not found</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpPost]
    [TenantIdHeader]
    [ProducesResponseType(typeof(Result<UpsertObjectValuesResponseDto>), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<Result<UpsertObjectValuesResponseDto>>> UpsertObjectValues(
        [FromBody] UpsertObjectValuesRequestDto request)
    {
        if (request == null)
        {
            return BadRequest(Result<UpsertObjectValuesResponseDto>.Failure("Request cannot be null"));
        }

        var command = UpsertObjectValuesCommand.FromDto(request);
        var result = await Mediator.Send(command);

        if (!result.Succeeded)
        {
            // Check if it's a not found error
            if ( result.Message != null && result.Message.Contains("not found"))
            {
                return NotFound(result);
            }
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Upsert ObjectValues for a specific RefId (alternative endpoint with path parameters)
    /// </summary>
    /// <param name="refId">Reference ID that groups related ObjectValues</param>
    /// <param name="objectId">Object ID that these values belong to</param>
    /// <param name="values">Dictionary of values keyed by MetadataKey</param>
    /// <param name="autoCreateMetadata">Whether to create missing metadata automatically</param>
    /// <param name="strictValidation">Whether to validate data types strictly</param>
    /// <returns>Upsert response with statistics and updated data</returns>
    /// <response code="200">Returns the upsert response with updated ObjectValues</response>
    /// <response code="400">If the request is invalid</response>
    /// <response code="404">If the ObjectId is not found</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpPut("{refId}/object/{objectId}")]
    [TenantIdHeader]
    [ProducesResponseType(typeof(Result<UpsertObjectValuesResponseDto>), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<Result<UpsertObjectValuesResponseDto>>> UpsertObjectValuesByPath(
        Guid refId,
        Guid objectId,
        [FromBody] Dictionary<string, string> values,
        [FromQuery] bool autoCreateMetadata = true,
        [FromQuery] bool strictValidation = true)
    {
        if (refId == Guid.Empty)
        {
            return BadRequest(Result<UpsertObjectValuesResponseDto>.Failure("RefId cannot be empty"));
        }

        if (objectId == Guid.Empty)
        {
            return BadRequest(Result<UpsertObjectValuesResponseDto>.Failure("ObjectId cannot be empty"));
        }

        if (values == null || values.Count == 0)
        {
            return BadRequest(Result<UpsertObjectValuesResponseDto>.Failure("Values cannot be null or empty"));
        }

        var command = new UpsertObjectValuesCommand(
            refId,
            objectId,
            values,
            autoCreateMetadata,
            strictValidation);

        var result = await Mediator.Send(command);

        if (!result.Succeeded)
        {
            if (result.Message != null && result.Message.Contains("not found"))
            {
                return NotFound(result);
            }
            return BadRequest(result);
        }

        return Ok(result);
    }
}
