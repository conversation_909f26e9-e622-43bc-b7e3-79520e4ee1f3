using Application.ProductValues.DTOs;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.ProductValues.Commands;

/// <summary>
/// Create ProductValue command handler
/// </summary>
public class CreateProductValueCommandHandler : IRequestHandler<CreateProductValueCommand, Result<ProductValueDto>>
{
    private readonly IRepository<Domain.Entities.ProductValue> _repository;
    private readonly IRepository<Domain.Entities.ProductMetadata> _productMetadataRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateProductValueCommandHandler(
        IRepository<Domain.Entities.ProductValue> repository,
        IRepository<Domain.Entities.ProductMetadata> productMetadataRepository)
    {
        _repository = repository;
        _productMetadataRepository = productMetadataRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<ProductValueDto>> Handle(CreateProductValueCommand request, CancellationToken cancellationToken)
    {
        // Validate ProductMetadata exists
        var productMetadata = await _productMetadataRepository.GetByIdAsync(request.ProductMetadataId, cancellationToken);
        if (productMetadata == null)
        {
            return Result<ProductValueDto>.Failure($"ProductMetadata with ID '{request.ProductMetadataId}' not found.");
        }

        // Create ProductValue
        var productValue = new Domain.Entities.ProductValue
        {
            ProductMetadataId = request.ProductMetadataId,
            RefId = request.RefId,
            Value = request.Value
        };

        await _repository.AddAsync(productValue, cancellationToken);

        var dto = new ProductValueDto
        {
            Id = productValue.Id,
            ProductMetadataId = productValue.ProductMetadataId,
            RefId = productValue.RefId,
            ParentProductValueId = request.ParentProductValueId, // From request, not entity
            Value = productValue.Value,
            ChildValuesCount = 0,
            CreatedAt = productValue.CreatedAt,
            CreatedBy = productValue.CreatedBy ?? Guid.Empty,
            ModifiedAt = productValue.ModifiedAt,
            ModifiedBy = productValue.ModifiedBy
        };

        return Result<ProductValueDto>.Success(dto);
    }
}
