namespace Application.IntegrationConfigurations.DTOs;

/// <summary>
/// View Integration Configuration DTO
/// </summary>
public class ViewIntegrationConfigurationDto
{
    /// <summary>
    /// Integration Configuration ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Integration ID this configuration belongs to
    /// </summary>
    public Guid IntegrationId { get; set; }

    /// <summary>
    /// Integration name
    /// </summary>
    public string IntegrationName { get; set; } = string.Empty;

    /// <summary>
    /// Integration API ID this configuration uses
    /// </summary>
    public Guid IntegrationApiId { get; set; }

    /// <summary>
    /// Integration API name
    /// </summary>
    public string IntegrationApiName { get; set; } = string.Empty;

    /// <summary>
    /// Object ID this configuration applies to
    /// </summary>
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Object name
    /// </summary>
    public string ObjectName { get; set; } = string.Empty;

    /// <summary>
    /// Data flow direction (e.g., "In", "Out", "Both")
    /// </summary>
    public string? Direction { get; set; }

    /// <summary>
    /// Whether the configuration is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Created at
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by
    /// </summary>
    public Guid? CreatedBy { get; set; }

    /// <summary>
    /// Modified at
    /// </summary>
    public DateTime ModifiedAt { get; set; }

    /// <summary>
    /// Modified by
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}
