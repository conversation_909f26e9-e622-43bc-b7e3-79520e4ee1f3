using Application.DataTypes.Commands;
using Application.DataTypes.DTOs;
using Application.DataTypes.Queries;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;

namespace Web.Host.Controllers;

/// <summary>
/// DataTypes controller
/// </summary>
[Route("api/[controller]")]
public class DataTypesController : BaseApiController
{
    /// <summary>
    /// Get all data types with pagination
    /// </summary>
    [HttpGet]
    [TenantIdHeader]
    public async Task<ActionResult<PaginatedResult<DataTypeDto>>> GetDataTypes([FromQuery] GetDataTypesQuery query)
    {
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get data type by ID
    /// </summary>
    [HttpGet("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<DataTypeDto>>> GetDataTypeById(Guid id)
    {
        return Ok(await Mediator.Send(new GetDataTypeByIdQuery(id)));
    }

    /// <summary>
    /// Create a new data type
    /// </summary>
    [HttpPost]
    [TenantIdHeader]
    public async Task<ActionResult<Result<DataTypeDto>>> CreateDataType(CreateDataTypeCommand command)
    {
        return Ok(await Mediator.Send(command));
    }

    /// <summary>
    /// Update an existing data type
    /// </summary>
    [HttpPut("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<DataTypeDto>>> UpdateDataType(Guid id, UpdateDataTypeCommand command)
    {
        command.Id = id;
        return Ok(await Mediator.Send(command));
    }

    /// <summary>
    /// Delete a data type
    /// </summary>
    [HttpDelete("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<bool>>> DeleteDataType(Guid id)
    {
        return Ok(await Mediator.Send(new DeleteDataTypeCommand(id)));
    }
}
