using Application.DataTypes.Commands;
using Application.DataTypes.DTOs;
using Application.DataTypes.Queries;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;
using Swashbuckle.AspNetCore.Annotations;

namespace Web.Host.Controllers;

/// <summary>
/// DataTypes management controller
/// </summary>
[Route("api/datatypes")]
public class DataTypesController : BaseApiController
{
    /// <summary>
    /// Get all data types with pagination and filtering
    /// </summary>
    /// <param name="pageNumber">Page number (default: 1)</param>
    /// <param name="pageSize">Page size (default: 10)</param>
    /// <param name="searchTerm">Search term for filtering</param>
    /// <param name="category">Filter by category</param>
    /// <param name="isActive">Filter by active status</param>
    /// <param name="orderBy">Order by field</param>
    /// <returns>Paginated list of data types</returns>
    /// <response code="200">Returns the paginated list of data types</response>
    /// <response code="400">If the request is invalid</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet]
    [TenantIdHeader]
    [ProducesResponseType(typeof(PaginatedResult<DataTypeDto>), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [SwaggerOperation(OperationId = "GetDataTypes")]
    public async Task<ActionResult<PaginatedResult<DataTypeDto>>> GetDataTypesAsync(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? searchTerm = null,
        [FromQuery] string? category = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] string? orderBy = null)
    {
        var query = new GetDataTypesQuery
        {
            PageNumber = pageNumber,
            PageSize = pageSize,
            SearchTerm = searchTerm,
            Category = category,
            IsActive = isActive,
            OrderBy = orderBy
        };

        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get data type by ID
    /// </summary>
    /// <param name="id">Data type ID</param>
    /// <returns>Data type details</returns>
    /// <response code="200">Returns the data type</response>
    /// <response code="404">If the data type is not found</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet("{id}")]
    [TenantIdHeader]
    [ProducesResponseType(typeof(Result<DataTypeDto>), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    [SwaggerOperation(OperationId = "GetDataTypeById")]
    public async Task<ActionResult<Result<DataTypeDto>>> GetDataTypeByIdAsync(Guid id)
    {
        var query = new GetDataTypeByIdQuery { Id = id };
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Create a new data type
    /// </summary>
    /// <param name="command">Create data type command</param>
    /// <returns>Created data type</returns>
    /// <response code="200">Returns the created data type</response>
    /// <response code="400">If the request is invalid</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpPost]
    [TenantIdHeader]
    [ProducesResponseType(typeof(Result<DataTypeDto>), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [SwaggerOperation(OperationId = "CreateDataType")]
    public async Task<ActionResult<Result<DataTypeDto>>> CreateDataTypeAsync(CreateDataTypeCommand command)
    {
        return Ok(await Mediator.Send(command));
    }

    /// <summary>
    /// Update an existing data type
    /// </summary>
    /// <param name="id">Data type ID</param>
    /// <param name="command">Update data type command</param>
    /// <returns>Updated data type</returns>
    /// <response code="200">Returns the updated data type</response>
    /// <response code="404">If the data type is not found</response>
    /// <response code="400">If the request is invalid</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpPut("{id}")]
    [TenantIdHeader]
    [ProducesResponseType(typeof(Result<DataTypeDto>), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [SwaggerOperation(OperationId = "UpdateDataType")]
    public async Task<ActionResult<Result<DataTypeDto>>> UpdateDataTypeAsync(Guid id, UpdateDataTypeCommand command)
    {
        command.Id = id;
        return Ok(await Mediator.Send(command));
    }

    /// <summary>
    /// Delete a data type
    /// </summary>
    /// <param name="id">Data type ID</param>
    /// <returns>Deletion result</returns>
    /// <response code="200">Returns the deletion result</response>
    /// <response code="404">If the data type is not found</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpDelete("{id}")]
    [TenantIdHeader]
    [ProducesResponseType(typeof(Result<bool>), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    [SwaggerOperation(OperationId = "DeleteDataType")]
    public async Task<ActionResult<Result<bool>>> DeleteDataTypeAsync(Guid id)
    {
        var command = new DeleteDataTypeCommand { Id = id };
        return Ok(await Mediator.Send(command));
    }

    /// <summary>
    /// Bulk create data types
    /// </summary>
    /// <param name="command">Bulk create command containing list of data types to create</param>
    /// <returns>Result with bulk creation response including success/failure counts and details</returns>
    /// <response code="200">Returns the bulk creation result</response>
    /// <response code="400">If the request is invalid</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpPost("bulk-create")]
    [TenantIdHeader]
    [ProducesResponseType(typeof(Result<BulkCreateDataTypesResponse>), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [SwaggerOperation(OperationId = "BulkCreateDataTypes")]
    public async Task<ActionResult<Result<BulkCreateDataTypesResponse>>> BulkCreateDataTypesAsync(BulkCreateDataTypesCommand command)
    {
        return Ok(await Mediator.Send(command));
    }

    /// <summary>
    /// Bulk update data types
    /// </summary>
    /// <param name="command">Bulk update command containing list of data types to update</param>
    /// <returns>Result with bulk update response including success/failure counts and details</returns>
    /// <response code="200">Returns the bulk update result</response>
    /// <response code="400">If the request is invalid</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpPut("bulk-update")]
    [TenantIdHeader]
    [ProducesResponseType(typeof(Result<BulkUpdateDataTypesResponse>), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [SwaggerOperation(OperationId = "BulkUpdateDataTypes")]
    public async Task<ActionResult<Result<BulkUpdateDataTypesResponse>>> BulkUpdateDataTypesAsync(BulkUpdateDataTypesCommand command)
    {
        return Ok(await Mediator.Send(command));
    }
}
