using Ardalis.Specification;
using Domain.Entities;

namespace Application.ObjectValues.Specifications;

/// <summary>
/// Specification to count ObjectValues with filters
/// </summary>
public class ObjectValueCountSpec : Specification<ObjectValue>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ObjectValueCountSpec(
        Guid? featureId = null,
        Guid? objectId = null,
        Guid? metadataId = null,
        Guid? refId = null,
        string? searchTerm = null,
        bool onlyVisibleFields = false)
    {
        Query.Where(ov => !ov.IsDeleted);

        // Include related data for filtering
        Query.Include(ov => ov.ObjectMetadata)
             .ThenInclude(om => om.Object);

        Query.Include(ov => ov.ObjectMetadata)
             .ThenInclude(om => om.Metadata);

        // Filter by feature
        if (featureId.HasValue)
        {
            Query.Where(ov => ov.ObjectMetadata.Object.FeatureId == featureId.Value);
        }

        // Filter by specific object
        if (objectId.HasValue)
        {
            Query.Where(ov => ov.ObjectMetadata.ObjectId == objectId.Value);
        }

        // Filter by metadata
        if (metadataId.HasValue)
        {
            Query.Where(ov => ov.ObjectMetadata.MetadataId == metadataId.Value);
        }

        // Filter by RefId
        if (refId.HasValue)
        {
            Query.Where(ov => ov.RefId == refId.Value);
        }

        // Search term filter
        if (!string.IsNullOrEmpty(searchTerm))
        {
            Query.Where(ov => ov.Value != null && ov.Value.Contains(searchTerm) ||
                             ov.ObjectMetadata.Metadata.MetadataKey.Contains(searchTerm) ||
                             ov.ObjectMetadata.Object.Name.Contains(searchTerm));
        }

        // Filter by visible fields only
        if (onlyVisibleFields)
        {
            Query.Where(ov => ov.ObjectMetadata.Metadata.IsVisible);
        }

        // Additional filters for active records
        Query.Where(ov => ov.ObjectMetadata.Object.IsActive && 
                         !ov.ObjectMetadata.Object.IsDeleted &&
                         ov.ObjectMetadata.IsActive && 
                         !ov.ObjectMetadata.IsDeleted);
    }
}
