using Application.Features.DTOs;
using Application.Features.Specifications;
using Application.Products.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.Features.Commands;

/// <summary>
/// Create feature command handler
/// </summary>
public class CreateFeatureCommandHandler : IRequestHandler<CreateFeatureCommand, Result<FeatureDto>>
{
    private readonly IRepository<Feature> _featureRepository;
    private readonly IReadRepository<Product> _productRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateFeatureCommandHandler(
        IRepository<Feature> featureRepository,
        IReadRepository<Product> productRepository)
    {
        _featureRepository = featureRepository;
        _productRepository = productRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<FeatureDto>> Handle(CreateFeatureCommand request, CancellationToken cancellationToken)
    {
        // Check if product exists (tenant isolation handled by Finbuckle.MultiTenant)
        var productSpec = new ProductByIdSpec(request.ProductId);
        var product = await _productRepository.GetBySpecAsync(productSpec, cancellationToken);

        if (product == null)
        {
            return Result<FeatureDto>.Failure("Product not found.");
        }

        // Check if feature with same name already exists for this product
        var existingFeatureSpec = new FeatureByNameAndProductIdSpec(request.Name, request.ProductId);
        var existingFeature = await _featureRepository.GetBySpecAsync(existingFeatureSpec, cancellationToken);

        if (existingFeature != null)
        {
            return Result<FeatureDto>.Failure("Feature with this name already exists for this product.");
        }

        // Create new feature
        var feature = new Feature
        {
            ProductId = request.ProductId,
            Name = request.Name,
            Description = request.Description,
            IsDefault = request.IsDefault,
            IsActive = request.IsActive
        };

        await _featureRepository.AddAsync(feature, cancellationToken);

        var dto = feature.Adapt<FeatureDto>();
        return Result<FeatureDto>.Success(dto);
    }
}
