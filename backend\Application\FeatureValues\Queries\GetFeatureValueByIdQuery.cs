using Application.FeatureValues.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.FeatureValues.Queries;

/// <summary>
/// Get FeatureValue by ID query
/// </summary>
public class GetFeatureValueByIdQuery : IRequest<Result<FeatureValueDto>>
{
    /// <summary>
    /// FeatureValue ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public GetFeatureValueByIdQuery(Guid id)
    {
        Id = id;
    }
}
