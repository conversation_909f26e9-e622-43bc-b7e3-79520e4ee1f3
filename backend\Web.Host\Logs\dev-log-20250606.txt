[2025-06-06 12:44:01.366 +05:30 INF] Executed DbCommand (47ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:44:01.455 +05:30 INF] Applying Root Migrations.
[2025-06-06 12:44:01.509 +05:30 INF] Executed DbCommand (43ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:44:01.959 +05:30 INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);
[2025-06-06 12:44:02.010 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
LOCK TABLE "__EFMigrationsHistory" IN ACCESS EXCLUSIVE MODE
[2025-06-06 12:44:02.039 +05:30 INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:44:02.047 +05:30 INF] Applying migration '20250601131110_InitTenantDbContext'.
[2025-06-06 12:44:02.091 +05:30 INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = 'MultiTenancy') THEN
        CREATE SCHEMA "MultiTenancy";
    END IF;
END $EF$;
[2025-06-06 12:44:02.134 +05:30 ERR] Failed executing DbCommand (41ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "MultiTenancy"."Tenants" (
    "Id" character varying(64) NOT NULL,
    "CreatedBy" text,
    "CreatedOn" timestamp with time zone NOT NULL,
    "LastModifiedBy" text,
    "LastModifiedOn" timestamp with time zone,
    "Identifier" text,
    "Name" text,
    "ConnectionString" text,
    "AdminEmail" text NOT NULL,
    "IsActive" boolean NOT NULL,
    "ValidUpto" timestamp with time zone,
    "Issuer" text,
    "ResolutionKeys" text[],
    "CompanyName" text,
    "SubscriptionStatus" text NOT NULL,
    CONSTRAINT "PK_Tenants" PRIMARY KEY ("Id")
);
[2025-06-06 12:44:03.165 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-06 12:44:03.488 +05:30 INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:44:03.527 +05:30 INF] Applying Migrations for 'root' tenant.
[2025-06-06 12:44:03.556 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:44:03.973 +05:30 INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);
[2025-06-06 12:44:04.004 +05:30 INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
LOCK TABLE "__EFMigrationsHistory" IN ACCESS EXCLUSIVE MODE
[2025-06-06 12:44:04.033 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:44:04.034 +05:30 INF] Applying migration '20250601135548_InitProject'.
[2025-06-06 12:44:04.187 +05:30 INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = 'Genp') THEN
        CREATE SCHEMA "Genp";
    END IF;
END $EF$;
[2025-06-06 12:44:04.220 +05:30 ERR] Failed executing DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Genp"."DataTypes" (
    "Id" uuid NOT NULL,
    "Name" character varying(50) NOT NULL,
    "DisplayName" character varying(100) NOT NULL,
    "Category" character varying(50) NOT NULL,
    "UiComponent" character varying(50) NOT NULL,
    "ValidationPattern" character varying(500),
    "MinLength" integer,
    "MaxLength" integer,
    "MinValue" numeric,
    "MaxValue" numeric,
    "DecimalPlaces" integer,
    "StepValue" numeric,
    "IsRequired" boolean NOT NULL,
    "InputType" character varying(50),
    "InputMask" character varying(100),
    "Placeholder" character varying(255),
    "HtmlAttributes" character varying(500),
    "DefaultOptions" TEXT,
    "AllowsMultiple" boolean NOT NULL,
    "AllowsCustomOptions" boolean NOT NULL,
    "MaxSelections" integer,
    "AllowedFileTypes" character varying(255),
    "MaxFileSizeBytes" bigint,
    "RequiredErrorMessage" character varying(255),
    "PatternErrorMessage" character varying(255),
    "MinLengthErrorMessage" character varying(255),
    "MaxLengthErrorMessage" character varying(255),
    "MinValueErrorMessage" character varying(255),
    "MaxValueErrorMessage" character varying(255),
    "FileTypeErrorMessage" character varying(255),
    "FileSizeErrorMessage" character varying(255),
    "IsActive" boolean NOT NULL DEFAULT TRUE,
    "IsDeleted" boolean NOT NULL,
    "CreatedAt" timestamp with time zone NOT NULL,
    "CreatedBy" uuid,
    "ModifiedAt" timestamp with time zone NOT NULL,
    "ModifiedBy" uuid,
    CONSTRAINT "PK_DataTypes" PRIMARY KEY ("Id")
);
[2025-06-06 12:44:04.476 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-06 12:44:04.741 +05:30 INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:44:04.743 +05:30 INF] Applying Migrations for 'sleep' tenant.
[2025-06-06 12:44:04.764 +05:30 INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:44:05.055 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);
[2025-06-06 12:44:05.079 +05:30 INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
LOCK TABLE "__EFMigrationsHistory" IN ACCESS EXCLUSIVE MODE
[2025-06-06 12:44:05.101 +05:30 INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:44:05.102 +05:30 INF] Applying migration '20250601135548_InitProject'.
[2025-06-06 12:44:05.172 +05:30 INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = 'Genp') THEN
        CREATE SCHEMA "Genp";
    END IF;
END $EF$;
[2025-06-06 12:44:05.200 +05:30 ERR] Failed executing DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Genp"."DataTypes" (
    "Id" uuid NOT NULL,
    "Name" character varying(50) NOT NULL,
    "DisplayName" character varying(100) NOT NULL,
    "Category" character varying(50) NOT NULL,
    "UiComponent" character varying(50) NOT NULL,
    "ValidationPattern" character varying(500),
    "MinLength" integer,
    "MaxLength" integer,
    "MinValue" numeric,
    "MaxValue" numeric,
    "DecimalPlaces" integer,
    "StepValue" numeric,
    "IsRequired" boolean NOT NULL,
    "InputType" character varying(50),
    "InputMask" character varying(100),
    "Placeholder" character varying(255),
    "HtmlAttributes" character varying(500),
    "DefaultOptions" TEXT,
    "AllowsMultiple" boolean NOT NULL,
    "AllowsCustomOptions" boolean NOT NULL,
    "MaxSelections" integer,
    "AllowedFileTypes" character varying(255),
    "MaxFileSizeBytes" bigint,
    "RequiredErrorMessage" character varying(255),
    "PatternErrorMessage" character varying(255),
    "MinLengthErrorMessage" character varying(255),
    "MaxLengthErrorMessage" character varying(255),
    "MinValueErrorMessage" character varying(255),
    "MaxValueErrorMessage" character varying(255),
    "FileTypeErrorMessage" character varying(255),
    "FileSizeErrorMessage" character varying(255),
    "IsActive" boolean NOT NULL DEFAULT TRUE,
    "IsDeleted" boolean NOT NULL,
    "CreatedAt" timestamp with time zone NOT NULL,
    "CreatedBy" uuid,
    "ModifiedAt" timestamp with time zone NOT NULL,
    "ModifiedBy" uuid,
    CONSTRAINT "PK_DataTypes" PRIMARY KEY ("Id")
);
[2025-06-06 12:44:05.457 +05:30 INF] Connection to sleep's Database Succeeded.
[2025-06-06 12:44:06.316 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-06 12:44:06.550 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-06 12:44:06.616 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-06 12:44:06.616 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_vs/browserLink - null null
[2025-06-06 12:44:06.702 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - 200 16501 application/javascript; charset=utf-8 92.5982ms
[2025-06-06 12:44:06.724 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_vs/browserLink - 200 null text/javascript; charset=UTF-8 117.07ms
[2025-06-06 12:44:06.790 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-06 12:44:06.791 +05:30 INF] Hosting environment: dev
[2025-06-06 12:44:06.791 +05:30 INF] Content root path: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host
[2025-06-06 12:44:06.832 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - null null
[2025-06-06 12:44:07.144 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 12:44:07.151 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-06 12:44:07.157 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-06 12:44:07.158 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 12:44:07.160 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 12:44:07.161 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-06 12:44:07.162 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-06 12:44:07.374 +05:30 ERR] Connection ID "18374686510541701132", Request ID "4000000d-0007-ff00-b63f-84710c7967bb": An unhandled exception was thrown by the application.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - Web.Host.Controllers.SubscriptionsController.GetSubscriptionsAsync (Web.Host). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate schema for type - Shared.Common.Response.PaginatedResult`1[Application.Subscriptions.DTOs.SubscriptionDto]. See inner exception
 ---> System.InvalidOperationException: Can't use schemaId "$SubscriptionDto" for type "$Application.Subscriptions.DTOs.SubscriptionDto". The same schemaId is already used for type "$Application.Products.DTOs.SubscriptionDto"
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaRepository.RegisterType(Type type, String schemaId)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateArraySchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__1()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForMember(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, DataProperty dataProperty)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateObjectSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__3()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.CreateResponseMediaType(Type modelType, SchemaRepository schemaRespository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.<>c__DisplayClass42_0.<GenerateResponse>b__2(String contentType)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponse(ApiDescription apiDescription, SchemaRepository schemaRepository, String statusCode, ApiResponseType apiResponseType)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponses(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Infrastructure.Common.CurrentUserMiddleware.InvokeAsync(HttpContext context, ICurrentUserInitializer currentUserInitializer) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\Common\CurrentUserMiddleware.cs:line 27
   at Infrastructure.MultiTenancy.Middleware.TenantMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\MultiTenancy\Middleware\TenantMiddleware.cs:line 155
   at Finbuckle.MultiTenant.AspNetCore.MultiTenantMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.WebTools.BrowserLink.Net.BrowserLinkMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Watch.BrowserRefresh.BrowserRefreshMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Server.IIS.Core.IISHttpContextOfT`1.ProcessRequestAsync()
[2025-06-06 12:44:07.389 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - 500 null null 556.9232ms
