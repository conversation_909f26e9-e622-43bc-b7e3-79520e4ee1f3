[2025-06-06 12:44:01.366 +05:30 INF] Executed DbCommand (47ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:44:01.455 +05:30 INF] Applying Root Migrations.
[2025-06-06 12:44:01.509 +05:30 INF] Executed DbCommand (43ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:44:01.959 +05:30 INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);
[2025-06-06 12:44:02.010 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
LOCK TABLE "__EFMigrationsHistory" IN ACCESS EXCLUSIVE MODE
[2025-06-06 12:44:02.039 +05:30 INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:44:02.047 +05:30 INF] Applying migration '20250601131110_InitTenantDbContext'.
[2025-06-06 12:44:02.091 +05:30 INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = 'MultiTenancy') THEN
        CREATE SCHEMA "MultiTenancy";
    END IF;
END $EF$;
[2025-06-06 12:44:02.134 +05:30 ERR] Failed executing DbCommand (41ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "MultiTenancy"."Tenants" (
    "Id" character varying(64) NOT NULL,
    "CreatedBy" text,
    "CreatedOn" timestamp with time zone NOT NULL,
    "LastModifiedBy" text,
    "LastModifiedOn" timestamp with time zone,
    "Identifier" text,
    "Name" text,
    "ConnectionString" text,
    "AdminEmail" text NOT NULL,
    "IsActive" boolean NOT NULL,
    "ValidUpto" timestamp with time zone,
    "Issuer" text,
    "ResolutionKeys" text[],
    "CompanyName" text,
    "SubscriptionStatus" text NOT NULL,
    CONSTRAINT "PK_Tenants" PRIMARY KEY ("Id")
);
[2025-06-06 12:44:03.165 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-06 12:44:03.488 +05:30 INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:44:03.527 +05:30 INF] Applying Migrations for 'root' tenant.
[2025-06-06 12:44:03.556 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:44:03.973 +05:30 INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);
[2025-06-06 12:44:04.004 +05:30 INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
LOCK TABLE "__EFMigrationsHistory" IN ACCESS EXCLUSIVE MODE
[2025-06-06 12:44:04.033 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:44:04.034 +05:30 INF] Applying migration '20250601135548_InitProject'.
[2025-06-06 12:44:04.187 +05:30 INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = 'Genp') THEN
        CREATE SCHEMA "Genp";
    END IF;
END $EF$;
[2025-06-06 12:44:04.220 +05:30 ERR] Failed executing DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Genp"."DataTypes" (
    "Id" uuid NOT NULL,
    "Name" character varying(50) NOT NULL,
    "DisplayName" character varying(100) NOT NULL,
    "Category" character varying(50) NOT NULL,
    "UiComponent" character varying(50) NOT NULL,
    "ValidationPattern" character varying(500),
    "MinLength" integer,
    "MaxLength" integer,
    "MinValue" numeric,
    "MaxValue" numeric,
    "DecimalPlaces" integer,
    "StepValue" numeric,
    "IsRequired" boolean NOT NULL,
    "InputType" character varying(50),
    "InputMask" character varying(100),
    "Placeholder" character varying(255),
    "HtmlAttributes" character varying(500),
    "DefaultOptions" TEXT,
    "AllowsMultiple" boolean NOT NULL,
    "AllowsCustomOptions" boolean NOT NULL,
    "MaxSelections" integer,
    "AllowedFileTypes" character varying(255),
    "MaxFileSizeBytes" bigint,
    "RequiredErrorMessage" character varying(255),
    "PatternErrorMessage" character varying(255),
    "MinLengthErrorMessage" character varying(255),
    "MaxLengthErrorMessage" character varying(255),
    "MinValueErrorMessage" character varying(255),
    "MaxValueErrorMessage" character varying(255),
    "FileTypeErrorMessage" character varying(255),
    "FileSizeErrorMessage" character varying(255),
    "IsActive" boolean NOT NULL DEFAULT TRUE,
    "IsDeleted" boolean NOT NULL,
    "CreatedAt" timestamp with time zone NOT NULL,
    "CreatedBy" uuid,
    "ModifiedAt" timestamp with time zone NOT NULL,
    "ModifiedBy" uuid,
    CONSTRAINT "PK_DataTypes" PRIMARY KEY ("Id")
);
[2025-06-06 12:44:04.476 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-06 12:44:04.741 +05:30 INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:44:04.743 +05:30 INF] Applying Migrations for 'sleep' tenant.
[2025-06-06 12:44:04.764 +05:30 INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:44:05.055 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);
[2025-06-06 12:44:05.079 +05:30 INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
LOCK TABLE "__EFMigrationsHistory" IN ACCESS EXCLUSIVE MODE
[2025-06-06 12:44:05.101 +05:30 INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:44:05.102 +05:30 INF] Applying migration '20250601135548_InitProject'.
[2025-06-06 12:44:05.172 +05:30 INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = 'Genp') THEN
        CREATE SCHEMA "Genp";
    END IF;
END $EF$;
[2025-06-06 12:44:05.200 +05:30 ERR] Failed executing DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Genp"."DataTypes" (
    "Id" uuid NOT NULL,
    "Name" character varying(50) NOT NULL,
    "DisplayName" character varying(100) NOT NULL,
    "Category" character varying(50) NOT NULL,
    "UiComponent" character varying(50) NOT NULL,
    "ValidationPattern" character varying(500),
    "MinLength" integer,
    "MaxLength" integer,
    "MinValue" numeric,
    "MaxValue" numeric,
    "DecimalPlaces" integer,
    "StepValue" numeric,
    "IsRequired" boolean NOT NULL,
    "InputType" character varying(50),
    "InputMask" character varying(100),
    "Placeholder" character varying(255),
    "HtmlAttributes" character varying(500),
    "DefaultOptions" TEXT,
    "AllowsMultiple" boolean NOT NULL,
    "AllowsCustomOptions" boolean NOT NULL,
    "MaxSelections" integer,
    "AllowedFileTypes" character varying(255),
    "MaxFileSizeBytes" bigint,
    "RequiredErrorMessage" character varying(255),
    "PatternErrorMessage" character varying(255),
    "MinLengthErrorMessage" character varying(255),
    "MaxLengthErrorMessage" character varying(255),
    "MinValueErrorMessage" character varying(255),
    "MaxValueErrorMessage" character varying(255),
    "FileTypeErrorMessage" character varying(255),
    "FileSizeErrorMessage" character varying(255),
    "IsActive" boolean NOT NULL DEFAULT TRUE,
    "IsDeleted" boolean NOT NULL,
    "CreatedAt" timestamp with time zone NOT NULL,
    "CreatedBy" uuid,
    "ModifiedAt" timestamp with time zone NOT NULL,
    "ModifiedBy" uuid,
    CONSTRAINT "PK_DataTypes" PRIMARY KEY ("Id")
);
[2025-06-06 12:44:05.457 +05:30 INF] Connection to sleep's Database Succeeded.
[2025-06-06 12:44:06.316 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-06 12:44:06.550 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-06 12:44:06.616 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-06 12:44:06.616 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_vs/browserLink - null null
[2025-06-06 12:44:06.702 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - 200 16501 application/javascript; charset=utf-8 92.5982ms
[2025-06-06 12:44:06.724 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_vs/browserLink - 200 null text/javascript; charset=UTF-8 117.07ms
[2025-06-06 12:44:06.790 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-06 12:44:06.791 +05:30 INF] Hosting environment: dev
[2025-06-06 12:44:06.791 +05:30 INF] Content root path: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host
[2025-06-06 12:44:06.832 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - null null
[2025-06-06 12:44:07.144 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 12:44:07.151 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-06 12:44:07.157 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-06 12:44:07.158 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 12:44:07.160 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 12:44:07.161 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-06 12:44:07.162 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-06 12:44:07.374 +05:30 ERR] Connection ID "18374686510541701132", Request ID "4000000d-0007-ff00-b63f-84710c7967bb": An unhandled exception was thrown by the application.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - Web.Host.Controllers.SubscriptionsController.GetSubscriptionsAsync (Web.Host). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate schema for type - Shared.Common.Response.PaginatedResult`1[Application.Subscriptions.DTOs.SubscriptionDto]. See inner exception
 ---> System.InvalidOperationException: Can't use schemaId "$SubscriptionDto" for type "$Application.Subscriptions.DTOs.SubscriptionDto". The same schemaId is already used for type "$Application.Products.DTOs.SubscriptionDto"
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaRepository.RegisterType(Type type, String schemaId)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateArraySchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__1()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForMember(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, DataProperty dataProperty)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateObjectSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__3()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.CreateResponseMediaType(Type modelType, SchemaRepository schemaRespository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.<>c__DisplayClass42_0.<GenerateResponse>b__2(String contentType)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponse(ApiDescription apiDescription, SchemaRepository schemaRepository, String statusCode, ApiResponseType apiResponseType)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponses(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Infrastructure.Common.CurrentUserMiddleware.InvokeAsync(HttpContext context, ICurrentUserInitializer currentUserInitializer) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\Common\CurrentUserMiddleware.cs:line 27
   at Infrastructure.MultiTenancy.Middleware.TenantMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\MultiTenancy\Middleware\TenantMiddleware.cs:line 155
   at Finbuckle.MultiTenant.AspNetCore.MultiTenantMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.WebTools.BrowserLink.Net.BrowserLinkMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Watch.BrowserRefresh.BrowserRefreshMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Server.IIS.Core.IISHttpContextOfT`1.ProcessRequestAsync()
[2025-06-06 12:44:07.389 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - 500 null null 556.9232ms
[2025-06-06 12:52:23.213 +05:30 INF] Executed DbCommand (49ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:52:23.334 +05:30 INF] Applying Root Migrations.
[2025-06-06 12:52:23.380 +05:30 INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:52:23.911 +05:30 INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);
[2025-06-06 12:52:23.951 +05:30 INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
LOCK TABLE "__EFMigrationsHistory" IN ACCESS EXCLUSIVE MODE
[2025-06-06 12:52:23.975 +05:30 INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:52:23.984 +05:30 INF] Applying migration '20250601131110_InitTenantDbContext'.
[2025-06-06 12:52:24.022 +05:30 INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = 'MultiTenancy') THEN
        CREATE SCHEMA "MultiTenancy";
    END IF;
END $EF$;
[2025-06-06 12:52:24.053 +05:30 ERR] Failed executing DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "MultiTenancy"."Tenants" (
    "Id" character varying(64) NOT NULL,
    "CreatedBy" text,
    "CreatedOn" timestamp with time zone NOT NULL,
    "LastModifiedBy" text,
    "LastModifiedOn" timestamp with time zone,
    "Identifier" text,
    "Name" text,
    "ConnectionString" text,
    "AdminEmail" text NOT NULL,
    "IsActive" boolean NOT NULL,
    "ValidUpto" timestamp with time zone,
    "Issuer" text,
    "ResolutionKeys" text[],
    "CompanyName" text,
    "SubscriptionStatus" text NOT NULL,
    CONSTRAINT "PK_Tenants" PRIMARY KEY ("Id")
);
[2025-06-06 12:52:25.533 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-06 12:52:25.889 +05:30 INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:52:25.924 +05:30 INF] Applying Migrations for 'root' tenant.
[2025-06-06 12:52:25.947 +05:30 INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:52:26.535 +05:30 INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);
[2025-06-06 12:52:26.561 +05:30 INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
LOCK TABLE "__EFMigrationsHistory" IN ACCESS EXCLUSIVE MODE
[2025-06-06 12:52:26.583 +05:30 INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:52:26.584 +05:30 INF] Applying migration '20250601135548_InitProject'.
[2025-06-06 12:52:26.806 +05:30 INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = 'Genp') THEN
        CREATE SCHEMA "Genp";
    END IF;
END $EF$;
[2025-06-06 12:52:26.831 +05:30 ERR] Failed executing DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Genp"."DataTypes" (
    "Id" uuid NOT NULL,
    "Name" character varying(50) NOT NULL,
    "DisplayName" character varying(100) NOT NULL,
    "Category" character varying(50) NOT NULL,
    "UiComponent" character varying(50) NOT NULL,
    "ValidationPattern" character varying(500),
    "MinLength" integer,
    "MaxLength" integer,
    "MinValue" numeric,
    "MaxValue" numeric,
    "DecimalPlaces" integer,
    "StepValue" numeric,
    "IsRequired" boolean NOT NULL,
    "InputType" character varying(50),
    "InputMask" character varying(100),
    "Placeholder" character varying(255),
    "HtmlAttributes" character varying(500),
    "DefaultOptions" TEXT,
    "AllowsMultiple" boolean NOT NULL,
    "AllowsCustomOptions" boolean NOT NULL,
    "MaxSelections" integer,
    "AllowedFileTypes" character varying(255),
    "MaxFileSizeBytes" bigint,
    "RequiredErrorMessage" character varying(255),
    "PatternErrorMessage" character varying(255),
    "MinLengthErrorMessage" character varying(255),
    "MaxLengthErrorMessage" character varying(255),
    "MinValueErrorMessage" character varying(255),
    "MaxValueErrorMessage" character varying(255),
    "FileTypeErrorMessage" character varying(255),
    "FileSizeErrorMessage" character varying(255),
    "IsActive" boolean NOT NULL DEFAULT TRUE,
    "IsDeleted" boolean NOT NULL,
    "CreatedAt" timestamp with time zone NOT NULL,
    "CreatedBy" uuid,
    "ModifiedAt" timestamp with time zone NOT NULL,
    "ModifiedBy" uuid,
    CONSTRAINT "PK_DataTypes" PRIMARY KEY ("Id")
);
[2025-06-06 12:52:27.150 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-06 12:52:27.400 +05:30 INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:52:27.402 +05:30 INF] Applying Migrations for 'sleep' tenant.
[2025-06-06 12:52:27.423 +05:30 INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:52:27.805 +05:30 INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);
[2025-06-06 12:52:27.828 +05:30 INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
LOCK TABLE "__EFMigrationsHistory" IN ACCESS EXCLUSIVE MODE
[2025-06-06 12:52:27.852 +05:30 INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:52:27.853 +05:30 INF] Applying migration '20250601135548_InitProject'.
[2025-06-06 12:52:27.959 +05:30 INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = 'Genp') THEN
        CREATE SCHEMA "Genp";
    END IF;
END $EF$;
[2025-06-06 12:52:27.989 +05:30 ERR] Failed executing DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Genp"."DataTypes" (
    "Id" uuid NOT NULL,
    "Name" character varying(50) NOT NULL,
    "DisplayName" character varying(100) NOT NULL,
    "Category" character varying(50) NOT NULL,
    "UiComponent" character varying(50) NOT NULL,
    "ValidationPattern" character varying(500),
    "MinLength" integer,
    "MaxLength" integer,
    "MinValue" numeric,
    "MaxValue" numeric,
    "DecimalPlaces" integer,
    "StepValue" numeric,
    "IsRequired" boolean NOT NULL,
    "InputType" character varying(50),
    "InputMask" character varying(100),
    "Placeholder" character varying(255),
    "HtmlAttributes" character varying(500),
    "DefaultOptions" TEXT,
    "AllowsMultiple" boolean NOT NULL,
    "AllowsCustomOptions" boolean NOT NULL,
    "MaxSelections" integer,
    "AllowedFileTypes" character varying(255),
    "MaxFileSizeBytes" bigint,
    "RequiredErrorMessage" character varying(255),
    "PatternErrorMessage" character varying(255),
    "MinLengthErrorMessage" character varying(255),
    "MaxLengthErrorMessage" character varying(255),
    "MinValueErrorMessage" character varying(255),
    "MaxValueErrorMessage" character varying(255),
    "FileTypeErrorMessage" character varying(255),
    "FileSizeErrorMessage" character varying(255),
    "IsActive" boolean NOT NULL DEFAULT TRUE,
    "IsDeleted" boolean NOT NULL,
    "CreatedAt" timestamp with time zone NOT NULL,
    "CreatedBy" uuid,
    "ModifiedAt" timestamp with time zone NOT NULL,
    "ModifiedBy" uuid,
    CONSTRAINT "PK_DataTypes" PRIMARY KEY ("Id")
);
[2025-06-06 12:52:28.258 +05:30 INF] Connection to sleep's Database Succeeded.
[2025-06-06 12:52:28.620 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-06 12:52:28.760 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-06 12:52:28.834 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-06 12:52:28.834 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_vs/browserLink - null null
[2025-06-06 12:52:28.905 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - 200 16501 application/javascript; charset=utf-8 82.2168ms
[2025-06-06 12:52:28.935 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_vs/browserLink - 200 null text/javascript; charset=UTF-8 114.5467ms
[2025-06-06 12:52:29.036 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-06 12:52:29.036 +05:30 INF] Hosting environment: dev
[2025-06-06 12:52:29.036 +05:30 INF] Content root path: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host
[2025-06-06 12:52:29.042 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - null null
[2025-06-06 12:52:29.469 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 12:52:29.473 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-06 12:52:29.478 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-06 12:52:29.479 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 12:52:29.481 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 12:52:29.482 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-06 12:52:29.483 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-06 12:52:29.768 +05:30 ERR] Connection ID "18086456121505087490", Request ID "40000003-0004-fb00-b63f-84710c7967bb": An unhandled exception was thrown by the application.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - Web.Host.Controllers.SubscriptionsController.GetSubscriptionsAsync (Web.Host). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate schema for type - Shared.Common.Response.PaginatedResult`1[Application.Subscriptions.DTOs.SubscriptionDto]. See inner exception
 ---> System.InvalidOperationException: Can't use schemaId "$SubscriptionDto" for type "$Application.Subscriptions.DTOs.SubscriptionDto". The same schemaId is already used for type "$Application.Products.DTOs.SubscriptionDto"
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaRepository.RegisterType(Type type, String schemaId)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateArraySchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__1()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForMember(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, DataProperty dataProperty)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateObjectSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__3()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.CreateResponseMediaType(Type modelType, SchemaRepository schemaRespository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.<>c__DisplayClass42_0.<GenerateResponse>b__2(String contentType)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponse(ApiDescription apiDescription, SchemaRepository schemaRepository, String statusCode, ApiResponseType apiResponseType)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponses(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Infrastructure.Common.CurrentUserMiddleware.InvokeAsync(HttpContext context, ICurrentUserInitializer currentUserInitializer) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\Common\CurrentUserMiddleware.cs:line 27
   at Infrastructure.MultiTenancy.Middleware.TenantMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\MultiTenancy\Middleware\TenantMiddleware.cs:line 155
   at Finbuckle.MultiTenant.AspNetCore.MultiTenantMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.WebTools.BrowserLink.Net.BrowserLinkMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Watch.BrowserRefresh.BrowserRefreshMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Server.IIS.Core.IISHttpContextOfT`1.ProcessRequestAsync()
[2025-06-06 12:52:29.783 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - 500 null null 742.3332ms
[2025-06-06 12:57:04.817 +05:30 INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:57:04.887 +05:30 INF] Applying Root Migrations.
[2025-06-06 12:57:04.920 +05:30 INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:57:05.380 +05:30 INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);
[2025-06-06 12:57:05.421 +05:30 INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
LOCK TABLE "__EFMigrationsHistory" IN ACCESS EXCLUSIVE MODE
[2025-06-06 12:57:05.451 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:57:05.459 +05:30 INF] Applying migration '20250601131110_InitTenantDbContext'.
[2025-06-06 12:57:05.497 +05:30 INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = 'MultiTenancy') THEN
        CREATE SCHEMA "MultiTenancy";
    END IF;
END $EF$;
[2025-06-06 12:57:05.534 +05:30 ERR] Failed executing DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "MultiTenancy"."Tenants" (
    "Id" character varying(64) NOT NULL,
    "CreatedBy" text,
    "CreatedOn" timestamp with time zone NOT NULL,
    "LastModifiedBy" text,
    "LastModifiedOn" timestamp with time zone,
    "Identifier" text,
    "Name" text,
    "ConnectionString" text,
    "AdminEmail" text NOT NULL,
    "IsActive" boolean NOT NULL,
    "ValidUpto" timestamp with time zone,
    "Issuer" text,
    "ResolutionKeys" text[],
    "CompanyName" text,
    "SubscriptionStatus" text NOT NULL,
    CONSTRAINT "PK_Tenants" PRIMARY KEY ("Id")
);
[2025-06-06 12:57:06.435 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-06 12:57:06.785 +05:30 INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:57:06.820 +05:30 INF] Applying Migrations for 'root' tenant.
[2025-06-06 12:57:06.850 +05:30 INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:57:07.329 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);
[2025-06-06 12:57:07.362 +05:30 INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
LOCK TABLE "__EFMigrationsHistory" IN ACCESS EXCLUSIVE MODE
[2025-06-06 12:57:07.389 +05:30 INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:57:07.391 +05:30 INF] Applying migration '20250601135548_InitProject'.
[2025-06-06 12:57:07.528 +05:30 INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = 'Genp') THEN
        CREATE SCHEMA "Genp";
    END IF;
END $EF$;
[2025-06-06 12:57:07.560 +05:30 ERR] Failed executing DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Genp"."DataTypes" (
    "Id" uuid NOT NULL,
    "Name" character varying(50) NOT NULL,
    "DisplayName" character varying(100) NOT NULL,
    "Category" character varying(50) NOT NULL,
    "UiComponent" character varying(50) NOT NULL,
    "ValidationPattern" character varying(500),
    "MinLength" integer,
    "MaxLength" integer,
    "MinValue" numeric,
    "MaxValue" numeric,
    "DecimalPlaces" integer,
    "StepValue" numeric,
    "IsRequired" boolean NOT NULL,
    "InputType" character varying(50),
    "InputMask" character varying(100),
    "Placeholder" character varying(255),
    "HtmlAttributes" character varying(500),
    "DefaultOptions" TEXT,
    "AllowsMultiple" boolean NOT NULL,
    "AllowsCustomOptions" boolean NOT NULL,
    "MaxSelections" integer,
    "AllowedFileTypes" character varying(255),
    "MaxFileSizeBytes" bigint,
    "RequiredErrorMessage" character varying(255),
    "PatternErrorMessage" character varying(255),
    "MinLengthErrorMessage" character varying(255),
    "MaxLengthErrorMessage" character varying(255),
    "MinValueErrorMessage" character varying(255),
    "MaxValueErrorMessage" character varying(255),
    "FileTypeErrorMessage" character varying(255),
    "FileSizeErrorMessage" character varying(255),
    "IsActive" boolean NOT NULL DEFAULT TRUE,
    "IsDeleted" boolean NOT NULL,
    "CreatedAt" timestamp with time zone NOT NULL,
    "CreatedBy" uuid,
    "ModifiedAt" timestamp with time zone NOT NULL,
    "ModifiedBy" uuid,
    CONSTRAINT "PK_DataTypes" PRIMARY KEY ("Id")
);
[2025-06-06 12:57:07.884 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-06 12:57:08.178 +05:30 INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:57:08.179 +05:30 INF] Applying Migrations for 'sleep' tenant.
[2025-06-06 12:57:08.206 +05:30 INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:57:08.553 +05:30 INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);
[2025-06-06 12:57:08.582 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
LOCK TABLE "__EFMigrationsHistory" IN ACCESS EXCLUSIVE MODE
[2025-06-06 12:57:08.611 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 12:57:08.611 +05:30 INF] Applying migration '20250601135548_InitProject'.
[2025-06-06 12:57:08.693 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = 'Genp') THEN
        CREATE SCHEMA "Genp";
    END IF;
END $EF$;
[2025-06-06 12:57:08.725 +05:30 ERR] Failed executing DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Genp"."DataTypes" (
    "Id" uuid NOT NULL,
    "Name" character varying(50) NOT NULL,
    "DisplayName" character varying(100) NOT NULL,
    "Category" character varying(50) NOT NULL,
    "UiComponent" character varying(50) NOT NULL,
    "ValidationPattern" character varying(500),
    "MinLength" integer,
    "MaxLength" integer,
    "MinValue" numeric,
    "MaxValue" numeric,
    "DecimalPlaces" integer,
    "StepValue" numeric,
    "IsRequired" boolean NOT NULL,
    "InputType" character varying(50),
    "InputMask" character varying(100),
    "Placeholder" character varying(255),
    "HtmlAttributes" character varying(500),
    "DefaultOptions" TEXT,
    "AllowsMultiple" boolean NOT NULL,
    "AllowsCustomOptions" boolean NOT NULL,
    "MaxSelections" integer,
    "AllowedFileTypes" character varying(255),
    "MaxFileSizeBytes" bigint,
    "RequiredErrorMessage" character varying(255),
    "PatternErrorMessage" character varying(255),
    "MinLengthErrorMessage" character varying(255),
    "MaxLengthErrorMessage" character varying(255),
    "MinValueErrorMessage" character varying(255),
    "MaxValueErrorMessage" character varying(255),
    "FileTypeErrorMessage" character varying(255),
    "FileSizeErrorMessage" character varying(255),
    "IsActive" boolean NOT NULL DEFAULT TRUE,
    "IsDeleted" boolean NOT NULL,
    "CreatedAt" timestamp with time zone NOT NULL,
    "CreatedBy" uuid,
    "ModifiedAt" timestamp with time zone NOT NULL,
    "ModifiedBy" uuid,
    CONSTRAINT "PK_DataTypes" PRIMARY KEY ("Id")
);
[2025-06-06 12:57:09.000 +05:30 INF] Connection to sleep's Database Succeeded.
[2025-06-06 12:57:09.283 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-06 12:57:09.361 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-06 12:57:09.413 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_vs/browserLink - null null
[2025-06-06 12:57:09.413 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-06 12:57:09.487 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - 200 16501 application/javascript; charset=utf-8 81.7154ms
[2025-06-06 12:57:09.497 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_vs/browserLink - 200 null text/javascript; charset=UTF-8 94.7348ms
[2025-06-06 12:57:09.571 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-06 12:57:09.572 +05:30 INF] Hosting environment: dev
[2025-06-06 12:57:09.572 +05:30 INF] Content root path: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host
[2025-06-06 12:57:09.578 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - null null
[2025-06-06 12:57:09.907 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 12:57:09.909 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-06 12:57:09.913 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-06 12:57:09.914 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 12:57:09.916 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 12:57:09.917 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-06 12:57:09.917 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-06 12:57:10.142 +05:30 ERR] Connection ID "18158513702658113537", Request ID "40000002-0001-fc00-b63f-84710c7967bb": An unhandled exception was thrown by the application.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - Web.Host.Controllers.SubscriptionsController.GetSubscriptionsAsync (Web.Host). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate schema for type - Shared.Common.Response.PaginatedResult`1[Application.Subscriptions.DTOs.SubscriptionDto]. See inner exception
 ---> System.InvalidOperationException: Can't use schemaId "$SubscriptionDto" for type "$Application.Subscriptions.DTOs.SubscriptionDto". The same schemaId is already used for type "$Application.Products.DTOs.SubscriptionDto"
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaRepository.RegisterType(Type type, String schemaId)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateArraySchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__1()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForMember(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, DataProperty dataProperty)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateObjectSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__3()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.CreateResponseMediaType(Type modelType, SchemaRepository schemaRespository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.<>c__DisplayClass42_0.<GenerateResponse>b__2(String contentType)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponse(ApiDescription apiDescription, SchemaRepository schemaRepository, String statusCode, ApiResponseType apiResponseType)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponses(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Infrastructure.Common.CurrentUserMiddleware.InvokeAsync(HttpContext context, ICurrentUserInitializer currentUserInitializer) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\Common\CurrentUserMiddleware.cs:line 27
   at Infrastructure.MultiTenancy.Middleware.TenantMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\MultiTenancy\Middleware\TenantMiddleware.cs:line 155
   at Finbuckle.MultiTenant.AspNetCore.MultiTenantMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.WebTools.BrowserLink.Net.BrowserLinkMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Watch.BrowserRefresh.BrowserRefreshMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Server.IIS.Core.IISHttpContextOfT`1.ProcessRequestAsync()
[2025-06-06 12:57:10.158 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - 500 null null 580.1115ms
[2025-06-06 13:00:24.339 +05:30 INF] Executed DbCommand (54ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:00:24.742 +05:30 INF] Executed DbCommand (43ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-06 13:00:24.839 +05:30 INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-06 13:00:25.294 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-06 13:00:25.614 +05:30 INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:00:25.933 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-06 13:00:26.201 +05:30 INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:00:26.396 +05:30 INF] Connection to sleep's Database Succeeded.
[2025-06-06 13:00:26.752 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-06 13:00:26.869 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-06 13:00:26.936 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_vs/browserLink - null null
[2025-06-06 13:00:26.936 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-06 13:00:27.029 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - 200 16501 application/javascript; charset=utf-8 105.727ms
[2025-06-06 13:00:27.057 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_vs/browserLink - 200 null text/javascript; charset=UTF-8 134.7774ms
[2025-06-06 13:00:27.124 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-06 13:00:27.125 +05:30 INF] Hosting environment: dev
[2025-06-06 13:00:27.125 +05:30 INF] Content root path: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host
[2025-06-06 13:00:27.129 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - null null
[2025-06-06 13:00:27.522 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:00:27.526 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-06 13:00:27.532 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-06 13:00:27.533 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:00:27.536 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:00:27.538 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-06 13:00:27.539 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-06 13:00:27.733 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 604.8931ms
[2025-06-06 13:02:10.475 +05:30 INF] Executed DbCommand (52ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:02:11.138 +05:30 INF] Executed DbCommand (50ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-06 13:02:11.284 +05:30 INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-06 13:02:12.265 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-06 13:02:12.717 +05:30 INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:02:13.073 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-06 13:02:13.389 +05:30 INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:02:13.587 +05:30 INF] Connection to sleep's Database Succeeded.
[2025-06-06 13:02:13.891 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-06 13:02:13.977 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-06 13:02:14.030 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-06 13:02:14.030 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_vs/browserLink - null null
[2025-06-06 13:02:14.101 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - 200 16501 application/javascript; charset=utf-8 79.7829ms
[2025-06-06 13:02:14.117 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_vs/browserLink - 200 null text/javascript; charset=UTF-8 96.9666ms
[2025-06-06 13:02:14.187 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-06 13:02:14.188 +05:30 INF] Hosting environment: dev
[2025-06-06 13:02:14.188 +05:30 INF] Content root path: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host
[2025-06-06 13:02:14.216 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - null null
[2025-06-06 13:02:14.535 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:02:14.537 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-06 13:02:14.541 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-06 13:02:14.542 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:02:14.543 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:02:14.544 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-06 13:02:14.544 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-06 13:02:14.688 +05:30 ERR] Connection ID "18230571313875910678", Request ID "40000017-0005-fd00-b63f-84710c7967bb": An unhandled exception was thrown by the application.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Conflicting method/path combination "GET api/objects" for actions - Web.Host.Controllers.ObjectsController.GetObjects (Web.Host), Web.Host.Controllers.ObjectsController.GetObjectsAsync (Web.Host). Actions require a unique method/path combination for Swagger/OpenAPI 2.0 and 3.0. Use ConflictingActionsResolver as a workaround or provide your own implementation of PathGroupSelector.
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.PrepareGenerateOperation(IGrouping`2 group)
   at System.Linq.Enumerable.SelectEnumerableIterator`2.MoveNext()
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Infrastructure.Common.CurrentUserMiddleware.InvokeAsync(HttpContext context, ICurrentUserInitializer currentUserInitializer) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\Common\CurrentUserMiddleware.cs:line 27
   at Infrastructure.MultiTenancy.Middleware.TenantMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\MultiTenancy\Middleware\TenantMiddleware.cs:line 155
   at Finbuckle.MultiTenant.AspNetCore.MultiTenantMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.WebTools.BrowserLink.Net.BrowserLinkMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Watch.BrowserRefresh.BrowserRefreshMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Server.IIS.Core.IISHttpContextOfT`1.ProcessRequestAsync()
[2025-06-06 13:02:14.696 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - 500 null null 480.2852ms
[2025-06-06 13:05:36.466 +05:30 INF] Executed DbCommand (45ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:05:36.830 +05:30 INF] Executed DbCommand (41ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-06 13:05:36.923 +05:30 INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-06 13:05:37.367 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-06 13:05:37.668 +05:30 INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:05:37.976 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-06 13:05:38.252 +05:30 INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:05:38.457 +05:30 INF] Connection to sleep's Database Succeeded.
[2025-06-06 13:05:38.742 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-06 13:05:38.835 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-06 13:05:38.886 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-06 13:05:38.886 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_vs/browserLink - null null
[2025-06-06 13:05:38.969 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - 200 16501 application/javascript; charset=utf-8 90.9222ms
[2025-06-06 13:05:38.997 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_vs/browserLink - 200 null text/javascript; charset=UTF-8 121.6643ms
[2025-06-06 13:05:39.081 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - null null
[2025-06-06 13:05:39.097 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-06 13:05:39.097 +05:30 INF] Hosting environment: dev
[2025-06-06 13:05:39.097 +05:30 INF] Content root path: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host
[2025-06-06 13:05:39.447 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:05:39.449 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-06 13:05:39.452 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-06 13:05:39.453 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:05:39.454 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:05:39.455 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-06 13:05:39.455 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-06 13:05:39.641 +05:30 ERR] Connection ID "18158513715543015432", Request ID "40000009-0004-fc00-b63f-84710c7967bb": An unhandled exception was thrown by the application.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - Web.Host.Controllers.SubscriptionsController.GetSubscriptionsAsync (Web.Host). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate schema for type - Shared.Common.Response.PaginatedResult`1[Application.Subscriptions.DTOs.SubscriptionDto]. See inner exception
 ---> System.InvalidOperationException: Can't use schemaId "$SubscriptionDto" for type "$Application.Subscriptions.DTOs.SubscriptionDto". The same schemaId is already used for type "$Application.Products.DTOs.SubscriptionDto"
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaRepository.RegisterType(Type type, String schemaId)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateArraySchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__1()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForMember(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, DataProperty dataProperty)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateObjectSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__3()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.CreateResponseMediaType(Type modelType, SchemaRepository schemaRespository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.<>c__DisplayClass42_0.<GenerateResponse>b__2(String contentType)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponse(ApiDescription apiDescription, SchemaRepository schemaRepository, String statusCode, ApiResponseType apiResponseType)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponses(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Infrastructure.Common.CurrentUserMiddleware.InvokeAsync(HttpContext context, ICurrentUserInitializer currentUserInitializer) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\Common\CurrentUserMiddleware.cs:line 27
   at Infrastructure.MultiTenancy.Middleware.TenantMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\MultiTenancy\Middleware\TenantMiddleware.cs:line 155
   at Finbuckle.MultiTenant.AspNetCore.MultiTenantMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.WebTools.BrowserLink.Net.BrowserLinkMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Watch.BrowserRefresh.BrowserRefreshMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Server.IIS.Core.IISHttpContextOfT`1.ProcessRequestAsync()
[2025-06-06 13:05:39.652 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - 500 null null 572.1408ms
[2025-06-06 13:08:32.172 +05:30 INF] Executed DbCommand (47ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:08:32.606 +05:30 INF] Executed DbCommand (38ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-06 13:08:32.690 +05:30 INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-06 13:08:33.119 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-06 13:08:33.483 +05:30 INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:08:33.798 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-06 13:08:34.066 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:08:34.254 +05:30 INF] Connection to sleep's Database Succeeded.
[2025-06-06 13:08:34.472 +05:30 INF] DatabaseInitializer -> InitializeDatabasesAsync() : Error  {"ClassName":"System.ArgumentException","Message":"Format of the initialization string does not conform to specification starting at index 0.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":"   at System.Data.Common.DbConnectionOptions.GetKeyValuePair(String connectionString, Int32 currentPosition, StringBuilder buffer, Boolean useOdbcRules, String& keyname, String& keyvalue)\r\n   at System.Data.Common.DbConnectionOptions.ParseInternal(Dictionary`2 parsetable, String connectionString, Boolean buildChain, Dictionary`2 synonyms, Boolean firstKey)\r\n   at System.Data.Common.DbConnectionOptions..ctor(String connectionString, Dictionary`2 synonyms, Boolean useOdbcRules)\r\n   at System.Data.Common.DbConnectionStringBuilder.set_ConnectionString(String value)\r\n   at Npgsql.NpgsqlConnectionStringBuilder..ctor(String connectionString)\r\n   at Npgsql.NpgsqlDataSourceBuilder..ctor(String connectionString)\r\n   at Infrastructure.Database.Startup.<>c.<AddPersistence>b__0_0(IServiceProvider serviceProvider, DbContextOptionsBuilder options) in C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Infrastructure\\Database\\Startup.cs:line 43\r\n   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.DbContextOptionsConfiguration`1.Configure(IServiceProvider serviceProvider, DbContextOptionsBuilder optionsBuilder)\r\n   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.CreateDbContextOptions[TContext](IServiceProvider applicationServiceProvider)\r\n   at ResolveService(ILEmitResolverBuilderRuntimeContext, ServiceProviderEngineScope)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)\r\n   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.<>c__19`1.<AddCoreServices>b__19_0(IServiceProvider p)\r\n   at ResolveService(ILEmitResolverBuilderRuntimeContext, ServiceProviderEngineScope)\r\n   at ResolveService(ILEmitResolverBuilderRuntimeContext, ServiceProviderEngineScope)\r\n   at ResolveService(ILEmitResolverBuilderRuntimeContext, ServiceProviderEngineScope)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)\r\n   at Infrastructure.Database.DatabaseInitializer.InitializeApplicationDbForTenantAsync(AppTenantInfo tenant, CancellationToken cancellationToken) in C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Infrastructure\\Database\\DatabaseInitializer.cs:line 79\r\n   at Infrastructure.Database.DatabaseInitializer.InitializeDatabasesAsync(CancellationToken cancellationToken) in C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Infrastructure\\Database\\DatabaseInitializer.cs:line 52","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-**********,"Source":"System.Data.Common","WatsonBuckets":null,"ParamName":null}
[2025-06-06 13:08:34.746 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-06 13:08:34.841 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-06 13:08:34.896 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_vs/browserLink - null null
[2025-06-06 13:08:34.896 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-06 13:08:34.984 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - 200 16501 application/javascript; charset=utf-8 94.9272ms
[2025-06-06 13:08:34.996 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_vs/browserLink - 200 null text/javascript; charset=UTF-8 109.4975ms
[2025-06-06 13:08:35.072 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-06 13:08:35.072 +05:30 INF] Hosting environment: dev
[2025-06-06 13:08:35.072 +05:30 INF] Content root path: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host
[2025-06-06 13:08:35.082 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - null null
[2025-06-06 13:08:35.432 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:08:35.437 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-06 13:08:35.441 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-06 13:08:35.442 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:08:35.443 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:08:35.444 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-06 13:08:35.445 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-06 13:08:35.656 +05:30 ERR] Connection ID "18302628890733969423", Request ID "40000010-0001-fe00-b63f-84710c7967bb": An unhandled exception was thrown by the application.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - Web.Host.Controllers.SubscriptionsController.GetSubscriptionsAsync (Web.Host). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate schema for type - Shared.Common.Response.PaginatedResult`1[Application.Subscriptions.DTOs.SubscriptionDto]. See inner exception
 ---> System.InvalidOperationException: Can't use schemaId "$SubscriptionDto" for type "$Application.Subscriptions.DTOs.SubscriptionDto". The same schemaId is already used for type "$Application.Products.DTOs.SubscriptionDto"
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaRepository.RegisterType(Type type, String schemaId)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateArraySchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__1()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForMember(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, DataProperty dataProperty)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateObjectSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__3()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.CreateResponseMediaType(Type modelType, SchemaRepository schemaRespository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.<>c__DisplayClass42_0.<GenerateResponse>b__2(String contentType)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponse(ApiDescription apiDescription, SchemaRepository schemaRepository, String statusCode, ApiResponseType apiResponseType)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponses(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Infrastructure.Common.CurrentUserMiddleware.InvokeAsync(HttpContext context, ICurrentUserInitializer currentUserInitializer) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\Common\CurrentUserMiddleware.cs:line 27
   at Infrastructure.MultiTenancy.Middleware.TenantMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\MultiTenancy\Middleware\TenantMiddleware.cs:line 155
   at Finbuckle.MultiTenant.AspNetCore.MultiTenantMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.WebTools.BrowserLink.Net.BrowserLinkMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Watch.BrowserRefresh.BrowserRefreshMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Server.IIS.Core.IISHttpContextOfT`1.ProcessRequestAsync()
[2025-06-06 13:08:35.664 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - 500 null null 582.9901ms
[2025-06-06 13:11:28.380 +05:30 INF] Executed DbCommand (62ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:11:28.804 +05:30 INF] Executed DbCommand (53ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-06 13:11:28.900 +05:30 INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-06 13:11:29.319 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-06 13:11:30.125 +05:30 INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:11:30.462 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-06 13:11:30.783 +05:30 INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:11:31.012 +05:30 INF] Connection to sleep's Database Succeeded.
[2025-06-06 13:11:31.313 +05:30 INF] DatabaseInitializer -> InitializeDatabasesAsync() : Error  {"ClassName":"System.ArgumentException","Message":"Format of the initialization string does not conform to specification starting at index 0.","Data":null,"InnerException":null,"HelpURL":null,"StackTraceString":"   at System.Data.Common.DbConnectionOptions.GetKeyValuePair(String connectionString, Int32 currentPosition, StringBuilder buffer, Boolean useOdbcRules, String& keyname, String& keyvalue)\r\n   at System.Data.Common.DbConnectionOptions.ParseInternal(Dictionary`2 parsetable, String connectionString, Boolean buildChain, Dictionary`2 synonyms, Boolean firstKey)\r\n   at System.Data.Common.DbConnectionOptions..ctor(String connectionString, Dictionary`2 synonyms, Boolean useOdbcRules)\r\n   at System.Data.Common.DbConnectionStringBuilder.set_ConnectionString(String value)\r\n   at Npgsql.NpgsqlConnectionStringBuilder..ctor(String connectionString)\r\n   at Npgsql.NpgsqlDataSourceBuilder..ctor(String connectionString)\r\n   at Infrastructure.Database.Startup.<>c.<AddPersistence>b__0_0(IServiceProvider serviceProvider, DbContextOptionsBuilder options) in C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Infrastructure\\Database\\Startup.cs:line 43\r\n   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.DbContextOptionsConfiguration`1.Configure(IServiceProvider serviceProvider, DbContextOptionsBuilder optionsBuilder)\r\n   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.CreateDbContextOptions[TContext](IServiceProvider applicationServiceProvider)\r\n   at ResolveService(ILEmitResolverBuilderRuntimeContext, ServiceProviderEngineScope)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)\r\n   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.<>c__19`1.<AddCoreServices>b__19_0(IServiceProvider p)\r\n   at ResolveService(ILEmitResolverBuilderRuntimeContext, ServiceProviderEngineScope)\r\n   at ResolveService(ILEmitResolverBuilderRuntimeContext, ServiceProviderEngineScope)\r\n   at ResolveService(ILEmitResolverBuilderRuntimeContext, ServiceProviderEngineScope)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)\r\n   at Infrastructure.Database.DatabaseInitializer.InitializeApplicationDbForTenantAsync(AppTenantInfo tenant, CancellationToken cancellationToken) in C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Infrastructure\\Database\\DatabaseInitializer.cs:line 79\r\n   at Infrastructure.Database.DatabaseInitializer.InitializeDatabasesAsync(CancellationToken cancellationToken) in C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Infrastructure\\Database\\DatabaseInitializer.cs:line 52","RemoteStackTraceString":null,"RemoteStackIndex":0,"ExceptionMethod":null,"HResult":-**********,"Source":"System.Data.Common","WatsonBuckets":null,"ParamName":null}
[2025-06-06 13:11:31.603 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-06 13:11:31.689 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-06 13:11:31.741 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-06 13:11:31.741 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_vs/browserLink - null null
[2025-06-06 13:11:31.824 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - 200 16501 application/javascript; charset=utf-8 91.0117ms
[2025-06-06 13:11:31.913 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-06 13:11:31.914 +05:30 INF] Hosting environment: dev
[2025-06-06 13:11:31.914 +05:30 INF] Content root path: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host
[2025-06-06 13:11:32.368 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_vs/browserLink - 200 null text/javascript; charset=UTF-8 637.6322ms
[2025-06-06 13:11:32.401 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - null null
[2025-06-06 13:11:32.613 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:11:32.615 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-06 13:11:32.618 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-06 13:11:32.619 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:11:32.620 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:11:32.621 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-06 13:11:32.621 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-06 13:11:32.762 +05:30 ERR] Connection ID "18230571300991008786", Request ID "40000013-0002-fd00-b63f-84710c7967bb": An unhandled exception was thrown by the application.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - Web.Host.Controllers.SubscriptionsController.GetSubscriptionsAsync (Web.Host). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate schema for type - Shared.Common.Response.PaginatedResult`1[Application.Subscriptions.DTOs.SubscriptionDto]. See inner exception
 ---> System.InvalidOperationException: Can't use schemaId "$SubscriptionDto" for type "$Application.Subscriptions.DTOs.SubscriptionDto". The same schemaId is already used for type "$Application.Products.DTOs.SubscriptionDto"
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaRepository.RegisterType(Type type, String schemaId)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateArraySchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__1()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForMember(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, DataProperty dataProperty)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateObjectSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__3()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.CreateResponseMediaType(Type modelType, SchemaRepository schemaRespository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.<>c__DisplayClass42_0.<GenerateResponse>b__2(String contentType)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponse(ApiDescription apiDescription, SchemaRepository schemaRepository, String statusCode, ApiResponseType apiResponseType)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponses(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Infrastructure.Common.CurrentUserMiddleware.InvokeAsync(HttpContext context, ICurrentUserInitializer currentUserInitializer) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\Common\CurrentUserMiddleware.cs:line 27
   at Infrastructure.MultiTenancy.Middleware.TenantMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\MultiTenancy\Middleware\TenantMiddleware.cs:line 155
   at Finbuckle.MultiTenant.AspNetCore.MultiTenantMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.WebTools.BrowserLink.Net.BrowserLinkMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Watch.BrowserRefresh.BrowserRefreshMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Server.IIS.Core.IISHttpContextOfT`1.ProcessRequestAsync()
[2025-06-06 13:11:32.772 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - 500 null null 371.4381ms
[2025-06-06 13:13:27.641 +05:30 INF] Executed DbCommand (66ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:13:28.086 +05:30 INF] Executed DbCommand (40ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-06 13:13:28.189 +05:30 INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-06 13:13:28.644 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-06 13:13:28.973 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:13:29.300 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-06 13:13:29.578 +05:30 INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:13:29.771 +05:30 INF] Connection to sleep's Database Succeeded.
[2025-06-06 13:13:30.052 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-06 13:13:30.139 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-06 13:13:30.187 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_vs/browserLink - null null
[2025-06-06 13:13:30.187 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-06 13:13:30.257 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - 200 16501 application/javascript; charset=utf-8 79.865ms
[2025-06-06 13:13:30.275 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_vs/browserLink - 200 null text/javascript; charset=UTF-8 99.1131ms
[2025-06-06 13:13:30.361 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - null null
[2025-06-06 13:13:30.375 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-06 13:13:30.376 +05:30 INF] Hosting environment: dev
[2025-06-06 13:13:30.376 +05:30 INF] Content root path: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host
[2025-06-06 13:13:30.717 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:13:30.720 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-06 13:13:30.723 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-06 13:13:30.723 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:13:30.724 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:13:30.725 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-06 13:13:30.725 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-06 13:13:31.032 +05:30 ERR] Connection ID "18374686510541701146", Request ID "4000001b-0007-ff00-b63f-84710c7967bb": An unhandled exception was thrown by the application.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - Web.Host.Controllers.SubscriptionsController.GetSubscriptionsAsync (Web.Host). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate schema for type - Shared.Common.Response.PaginatedResult`1[Application.Subscriptions.DTOs.SubscriptionDto]. See inner exception
 ---> System.InvalidOperationException: Can't use schemaId "$SubscriptionDto" for type "$Application.Subscriptions.DTOs.SubscriptionDto". The same schemaId is already used for type "$Application.Products.DTOs.SubscriptionDto"
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaRepository.RegisterType(Type type, String schemaId)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateArraySchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__1()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForMember(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, DataProperty dataProperty)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateObjectSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__3()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.CreateResponseMediaType(Type modelType, SchemaRepository schemaRespository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.<>c__DisplayClass42_0.<GenerateResponse>b__2(String contentType)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponse(ApiDescription apiDescription, SchemaRepository schemaRepository, String statusCode, ApiResponseType apiResponseType)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponses(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Infrastructure.Common.CurrentUserMiddleware.InvokeAsync(HttpContext context, ICurrentUserInitializer currentUserInitializer) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\Common\CurrentUserMiddleware.cs:line 27
   at Infrastructure.MultiTenancy.Middleware.TenantMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\MultiTenancy\Middleware\TenantMiddleware.cs:line 155
   at Finbuckle.MultiTenant.AspNetCore.MultiTenantMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.WebTools.BrowserLink.Net.BrowserLinkMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Watch.BrowserRefresh.BrowserRefreshMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Server.IIS.Core.IISHttpContextOfT`1.ProcessRequestAsync()
[2025-06-06 13:13:31.044 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - 500 null null 682.3343ms
[2025-06-06 13:14:56.060 +05:30 INF] Executed DbCommand (43ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:14:56.433 +05:30 INF] Executed DbCommand (42ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-06 13:14:56.529 +05:30 INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-06 13:14:56.978 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-06 13:14:57.328 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:14:57.634 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-06 13:14:57.918 +05:30 INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:14:58.117 +05:30 INF] Connection to sleep's Database Succeeded.
[2025-06-06 13:14:58.414 +05:30 INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:14:58.608 +05:30 INF] Connection to neon's Database Succeeded.
[2025-06-06 13:14:59.103 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-06 13:14:59.175 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-06 13:14:59.223 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_vs/browserLink - null null
[2025-06-06 13:14:59.223 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-06 13:14:59.285 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - 200 16501 application/javascript; charset=utf-8 67.8241ms
[2025-06-06 13:14:59.308 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_vs/browserLink - 200 null text/javascript; charset=UTF-8 93.4547ms
[2025-06-06 13:14:59.370 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-06 13:14:59.371 +05:30 INF] Hosting environment: dev
[2025-06-06 13:14:59.371 +05:30 INF] Content root path: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host
[2025-06-06 13:14:59.501 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - null null
[2025-06-06 13:14:59.756 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:14:59.757 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-06 13:14:59.761 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-06 13:14:59.761 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:14:59.762 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:14:59.763 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-06 13:14:59.763 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-06 13:14:59.878 +05:30 ERR] Connection ID "18230571292401074192", Request ID "40000011-0000-fd00-b63f-84710c7967bb": An unhandled exception was thrown by the application.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - Web.Host.Controllers.SubscriptionsController.GetSubscriptionsAsync (Web.Host). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate schema for type - Shared.Common.Response.PaginatedResult`1[Application.Subscriptions.DTOs.SubscriptionDto]. See inner exception
 ---> System.InvalidOperationException: Can't use schemaId "$SubscriptionDto" for type "$Application.Subscriptions.DTOs.SubscriptionDto". The same schemaId is already used for type "$Application.Products.DTOs.SubscriptionDto"
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaRepository.RegisterType(Type type, String schemaId)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateArraySchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__1()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForMember(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, DataProperty dataProperty)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateObjectSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__3()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.CreateResponseMediaType(Type modelType, SchemaRepository schemaRespository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.<>c__DisplayClass42_0.<GenerateResponse>b__2(String contentType)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponse(ApiDescription apiDescription, SchemaRepository schemaRepository, String statusCode, ApiResponseType apiResponseType)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponses(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Infrastructure.Common.CurrentUserMiddleware.InvokeAsync(HttpContext context, ICurrentUserInitializer currentUserInitializer) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\Common\CurrentUserMiddleware.cs:line 27
   at Infrastructure.MultiTenancy.Middleware.TenantMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\MultiTenancy\Middleware\TenantMiddleware.cs:line 155
   at Finbuckle.MultiTenant.AspNetCore.MultiTenantMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.WebTools.BrowserLink.Net.BrowserLinkMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Watch.BrowserRefresh.BrowserRefreshMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Server.IIS.Core.IISHttpContextOfT`1.ProcessRequestAsync()
[2025-06-06 13:14:59.891 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - 500 null null 390.7498ms
[2025-06-06 13:16:07.153 +05:30 INF] Executed DbCommand (41ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:16:07.616 +05:30 INF] Executed DbCommand (43ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-06 13:16:07.715 +05:30 INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-06 13:16:08.444 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-06 13:16:08.817 +05:30 INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:16:09.139 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-06 13:16:09.414 +05:30 INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:16:09.619 +05:30 INF] Connection to sleep's Database Succeeded.
[2025-06-06 13:16:09.943 +05:30 INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:16:10.169 +05:30 INF] Connection to neon's Database Succeeded.
[2025-06-06 13:16:10.661 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-06 13:16:10.753 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-06 13:16:10.820 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-06 13:16:10.820 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_vs/browserLink - null null
[2025-06-06 13:16:10.886 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - 200 16501 application/javascript; charset=utf-8 72.5654ms
[2025-06-06 13:16:10.910 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_vs/browserLink - 200 null text/javascript; charset=UTF-8 98.5204ms
[2025-06-06 13:16:10.990 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-06 13:16:10.993 +05:30 INF] Hosting environment: dev
[2025-06-06 13:16:10.993 +05:30 INF] Content root path: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host
[2025-06-06 13:16:10.994 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - null null
[2025-06-06 13:16:11.324 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:16:11.327 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-06 13:16:11.330 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-06 13:16:11.331 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:16:11.332 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:16:11.333 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-06 13:16:11.333 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-06 13:16:11.554 +05:30 ERR] Connection ID "18374686510541701150", Request ID "4000001f-0007-ff00-b63f-84710c7967bb": An unhandled exception was thrown by the application.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - Web.Host.Controllers.SubscriptionsController.GetSubscriptionsAsync (Web.Host). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate schema for type - Shared.Common.Response.PaginatedResult`1[Application.Subscriptions.DTOs.SubscriptionDto]. See inner exception
 ---> System.InvalidOperationException: Can't use schemaId "$SubscriptionDto" for type "$Application.Subscriptions.DTOs.SubscriptionDto". The same schemaId is already used for type "$Application.Products.DTOs.SubscriptionDto"
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaRepository.RegisterType(Type type, String schemaId)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateArraySchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__1()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForMember(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, DataProperty dataProperty)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateObjectSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__3()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.CreateResponseMediaType(Type modelType, SchemaRepository schemaRespository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.<>c__DisplayClass42_0.<GenerateResponse>b__2(String contentType)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponse(ApiDescription apiDescription, SchemaRepository schemaRepository, String statusCode, ApiResponseType apiResponseType)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponses(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Infrastructure.Common.CurrentUserMiddleware.InvokeAsync(HttpContext context, ICurrentUserInitializer currentUserInitializer) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\Common\CurrentUserMiddleware.cs:line 27
   at Infrastructure.MultiTenancy.Middleware.TenantMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\MultiTenancy\Middleware\TenantMiddleware.cs:line 155
   at Finbuckle.MultiTenant.AspNetCore.MultiTenantMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.WebTools.BrowserLink.Net.BrowserLinkMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Watch.BrowserRefresh.BrowserRefreshMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Server.IIS.Core.IISHttpContextOfT`1.ProcessRequestAsync()
[2025-06-06 13:16:11.571 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - 500 null null 577.2527ms
[2025-06-06 13:24:22.223 +05:30 INF] Executed DbCommand (50ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:24:22.656 +05:30 INF] Executed DbCommand (38ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-06 13:24:22.752 +05:30 INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-06 13:24:23.213 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-06 13:24:23.531 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:24:23.847 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-06 13:24:24.173 +05:30 INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:24:24.370 +05:30 INF] Connection to sleep's Database Succeeded.
[2025-06-06 13:24:24.693 +05:30 INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:24:24.882 +05:30 INF] Connection to neon's Database Succeeded.
[2025-06-06 13:24:25.153 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-06 13:24:25.234 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-06 13:24:25.287 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_vs/browserLink - null null
[2025-06-06 13:24:25.287 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-06 13:24:25.361 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - 200 16501 application/javascript; charset=utf-8 80.5078ms
[2025-06-06 13:24:25.379 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_vs/browserLink - 200 null text/javascript; charset=UTF-8 99.9586ms
[2025-06-06 13:24:25.444 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-06 13:24:25.445 +05:30 INF] Hosting environment: dev
[2025-06-06 13:24:25.445 +05:30 INF] Content root path: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host
[2025-06-06 13:24:25.473 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - null null
[2025-06-06 13:24:25.793 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:24:25.796 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-06 13:24:25.799 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-06 13:24:25.800 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:24:25.801 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:24:25.801 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-06 13:24:25.802 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-06 13:24:25.993 +05:30 ERR] Connection ID "18158513715543015438", Request ID "4000000f-0004-fc00-b63f-84710c7967bb": An unhandled exception was thrown by the application.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - Web.Host.Controllers.SubscriptionsController.GetSubscriptionsAsync (Web.Host). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate schema for type - Shared.Common.Response.PaginatedResult`1[Application.Subscriptions.DTOs.SubscriptionDto]. See inner exception
 ---> System.InvalidOperationException: Can't use schemaId "$SubscriptionDto" for type "$Application.Subscriptions.DTOs.SubscriptionDto". The same schemaId is already used for type "$Application.Products.DTOs.SubscriptionDto"
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaRepository.RegisterType(Type type, String schemaId)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateArraySchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__1()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForMember(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, DataProperty dataProperty)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateObjectSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__3()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.CreateResponseMediaType(Type modelType, SchemaRepository schemaRespository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.<>c__DisplayClass42_0.<GenerateResponse>b__2(String contentType)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponse(ApiDescription apiDescription, SchemaRepository schemaRepository, String statusCode, ApiResponseType apiResponseType)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponses(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Infrastructure.Common.CurrentUserMiddleware.InvokeAsync(HttpContext context, ICurrentUserInitializer currentUserInitializer) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\Common\CurrentUserMiddleware.cs:line 27
   at Infrastructure.MultiTenancy.Middleware.TenantMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\MultiTenancy\Middleware\TenantMiddleware.cs:line 155
   at Finbuckle.MultiTenant.AspNetCore.MultiTenantMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.WebTools.BrowserLink.Net.BrowserLinkMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Watch.BrowserRefresh.BrowserRefreshMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Server.IIS.Core.IISHttpContextOfT`1.ProcessRequestAsync()
[2025-06-06 13:24:26.008 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - 500 null null 535.4439ms
[2025-06-06 13:25:52.088 +05:30 INF] Executed DbCommand (52ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:25:52.500 +05:30 INF] Executed DbCommand (67ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-06 13:25:52.615 +05:30 INF] Executed DbCommand (44ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-06 13:25:53.046 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-06 13:25:53.475 +05:30 INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:25:53.807 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-06 13:25:54.107 +05:30 INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:25:54.313 +05:30 INF] Connection to sleep's Database Succeeded.
[2025-06-06 13:25:54.679 +05:30 INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:25:54.902 +05:30 INF] Connection to neon's Database Succeeded.
[2025-06-06 13:25:55.184 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-06 13:25:55.262 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-06 13:25:55.320 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_vs/browserLink - null null
[2025-06-06 13:25:55.320 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-06 13:25:55.395 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - 200 16501 application/javascript; charset=utf-8 86.3485ms
[2025-06-06 13:25:55.415 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_vs/browserLink - 200 null text/javascript; charset=UTF-8 108.2106ms
[2025-06-06 13:25:55.497 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - null null
[2025-06-06 13:25:55.500 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-06 13:25:55.500 +05:30 INF] Hosting environment: dev
[2025-06-06 13:25:55.500 +05:30 INF] Content root path: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host
[2025-06-06 13:25:55.810 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:25:55.813 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-06 13:25:55.816 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-06 13:25:55.817 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:25:55.818 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:25:55.819 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-06 13:25:55.819 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-06 13:25:55.993 +05:30 ERR] Connection ID "18014398531762126855", Request ID "40000008-0005-fa00-b63f-84710c7967bb": An unhandled exception was thrown by the application.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - Web.Host.Controllers.SubscriptionsController.GetSubscriptionsAsync (Web.Host). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate schema for type - Shared.Common.Response.PaginatedResult`1[Application.Subscriptions.DTOs.SubscriptionDto]. See inner exception
 ---> System.InvalidOperationException: Can't use schemaId "$SubscriptionDto" for type "$Application.Subscriptions.DTOs.SubscriptionDto". The same schemaId is already used for type "$Application.Products.DTOs.SubscriptionDto"
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaRepository.RegisterType(Type type, String schemaId)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateArraySchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__1()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForMember(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, DataProperty dataProperty)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateObjectSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__3()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.CreateResponseMediaType(Type modelType, SchemaRepository schemaRespository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.<>c__DisplayClass42_0.<GenerateResponse>b__2(String contentType)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponse(ApiDescription apiDescription, SchemaRepository schemaRepository, String statusCode, ApiResponseType apiResponseType)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponses(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Infrastructure.Common.CurrentUserMiddleware.InvokeAsync(HttpContext context, ICurrentUserInitializer currentUserInitializer) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\Common\CurrentUserMiddleware.cs:line 27
   at Infrastructure.MultiTenancy.Middleware.TenantMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\MultiTenancy\Middleware\TenantMiddleware.cs:line 155
   at Finbuckle.MultiTenant.AspNetCore.MultiTenantMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.WebTools.BrowserLink.Net.BrowserLinkMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Watch.BrowserRefresh.BrowserRefreshMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Server.IIS.Core.IISHttpContextOfT`1.ProcessRequestAsync()
[2025-06-06 13:25:56.003 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - 500 null null 506.5133ms
[2025-06-06 13:28:11.653 +05:30 INF] Executed DbCommand (41ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:28:12.045 +05:30 INF] Executed DbCommand (48ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-06 13:28:12.154 +05:30 INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-06 13:28:12.607 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-06 13:28:12.929 +05:30 INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:28:13.214 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-06 13:28:13.491 +05:30 INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:28:13.670 +05:30 INF] Connection to sleep's Database Succeeded.
[2025-06-06 13:28:13.975 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:28:14.169 +05:30 INF] Connection to neon's Database Succeeded.
[2025-06-06 13:28:14.456 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-06 13:28:14.547 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-06 13:28:14.598 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-06 13:28:14.598 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_vs/browserLink - null null
[2025-06-06 13:28:14.679 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - 200 16501 application/javascript; charset=utf-8 88.5654ms
[2025-06-06 13:28:14.688 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_vs/browserLink - 200 null text/javascript; charset=UTF-8 99.6824ms
[2025-06-06 13:28:14.764 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-06 13:28:14.764 +05:30 INF] Hosting environment: dev
[2025-06-06 13:28:14.764 +05:30 INF] Content root path: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host
[2025-06-06 13:28:14.771 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - null null
[2025-06-06 13:28:15.100 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:28:15.102 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-06 13:28:15.105 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-06 13:28:15.106 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:28:15.108 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:28:15.109 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-06 13:28:15.109 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-06 13:28:15.308 +05:30 ERR] Connection ID "18374686493361831950", Request ID "4000000f-0003-ff00-b63f-84710c7967bb": An unhandled exception was thrown by the application.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - Web.Host.Controllers.SubscriptionsController.GetSubscriptionsAsync (Web.Host). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate schema for type - Shared.Common.Response.PaginatedResult`1[Application.Subscriptions.DTOs.SubscriptionDto]. See inner exception
 ---> System.InvalidOperationException: Can't use schemaId "$SubscriptionDto" for type "$Application.Subscriptions.DTOs.SubscriptionDto". The same schemaId is already used for type "$Application.Products.DTOs.SubscriptionDto"
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaRepository.RegisterType(Type type, String schemaId)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateArraySchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__1()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForMember(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, DataProperty dataProperty)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateObjectSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__3()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.CreateResponseMediaType(Type modelType, SchemaRepository schemaRespository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.<>c__DisplayClass42_0.<GenerateResponse>b__2(String contentType)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponse(ApiDescription apiDescription, SchemaRepository schemaRepository, String statusCode, ApiResponseType apiResponseType)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponses(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Infrastructure.Common.CurrentUserMiddleware.InvokeAsync(HttpContext context, ICurrentUserInitializer currentUserInitializer) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\Common\CurrentUserMiddleware.cs:line 27
   at Infrastructure.MultiTenancy.Middleware.TenantMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\MultiTenancy\Middleware\TenantMiddleware.cs:line 155
   at Finbuckle.MultiTenant.AspNetCore.MultiTenantMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.WebTools.BrowserLink.Net.BrowserLinkMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Watch.BrowserRefresh.BrowserRefreshMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Server.IIS.Core.IISHttpContextOfT`1.ProcessRequestAsync()
[2025-06-06 13:28:15.337 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - 500 null null 566.0646ms
[2025-06-06 13:28:42.305 +05:30 INF] Executed DbCommand (42ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:28:42.722 +05:30 INF] Executed DbCommand (47ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-06 13:28:42.822 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-06 13:28:43.268 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-06 13:28:43.605 +05:30 INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:28:43.880 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-06 13:28:44.168 +05:30 INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:28:44.382 +05:30 INF] Connection to sleep's Database Succeeded.
[2025-06-06 13:28:44.690 +05:30 INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:28:44.884 +05:30 INF] Connection to neon's Database Succeeded.
[2025-06-06 13:28:45.169 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-06 13:28:45.249 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-06 13:28:45.300 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_vs/browserLink - null null
[2025-06-06 13:28:45.300 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-06 13:28:45.369 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - 200 16501 application/javascript; charset=utf-8 75.0936ms
[2025-06-06 13:28:45.382 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_vs/browserLink - 200 null text/javascript; charset=UTF-8 90.4348ms
[2025-06-06 13:28:45.450 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-06 13:28:45.451 +05:30 INF] Hosting environment: dev
[2025-06-06 13:28:45.451 +05:30 INF] Content root path: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host
[2025-06-06 13:28:45.470 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - null null
[2025-06-06 13:28:45.788 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:28:45.791 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-06 13:28:45.794 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-06 13:28:45.795 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:28:45.796 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:28:45.796 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-06 13:28:45.796 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-06 13:28:45.941 +05:30 ERR] Connection ID "18302628899323904008", Request ID "40000009-0003-fe00-b63f-84710c7967bb": An unhandled exception was thrown by the application.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - Web.Host.Controllers.SubscriptionsController.GetSubscriptionsAsync (Web.Host). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate schema for type - Shared.Common.Response.PaginatedResult`1[Application.Subscriptions.DTOs.SubscriptionDto]. See inner exception
 ---> System.InvalidOperationException: Can't use schemaId "$SubscriptionDto" for type "$Application.Subscriptions.DTOs.SubscriptionDto". The same schemaId is already used for type "$Application.Products.DTOs.SubscriptionDto"
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaRepository.RegisterType(Type type, String schemaId)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateArraySchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__1()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForMember(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, DataProperty dataProperty)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateObjectSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__3()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.CreateResponseMediaType(Type modelType, SchemaRepository schemaRespository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.<>c__DisplayClass42_0.<GenerateResponse>b__2(String contentType)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponse(ApiDescription apiDescription, SchemaRepository schemaRepository, String statusCode, ApiResponseType apiResponseType)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponses(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Infrastructure.Common.CurrentUserMiddleware.InvokeAsync(HttpContext context, ICurrentUserInitializer currentUserInitializer) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\Common\CurrentUserMiddleware.cs:line 27
   at Infrastructure.MultiTenancy.Middleware.TenantMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\MultiTenancy\Middleware\TenantMiddleware.cs:line 155
   at Finbuckle.MultiTenant.AspNetCore.MultiTenantMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.WebTools.BrowserLink.Net.BrowserLinkMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Watch.BrowserRefresh.BrowserRefreshMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Server.IIS.Core.IISHttpContextOfT`1.ProcessRequestAsync()
[2025-06-06 13:28:45.951 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - 500 null null 480.3769ms
[2025-06-06 13:29:12.893 +05:30 INF] Executed DbCommand (47ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:29:13.255 +05:30 INF] Executed DbCommand (41ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-06 13:29:13.343 +05:30 INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-06 13:29:13.755 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-06 13:29:14.089 +05:30 INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:29:14.388 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-06 13:29:14.695 +05:30 INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:29:14.893 +05:30 INF] Connection to sleep's Database Succeeded.
[2025-06-06 13:29:15.199 +05:30 INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:29:15.388 +05:30 INF] Connection to neon's Database Succeeded.
[2025-06-06 13:29:15.699 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-06 13:29:15.791 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-06 13:29:15.844 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-06 13:29:15.844 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_vs/browserLink - null null
[2025-06-06 13:29:15.906 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - 200 16501 application/javascript; charset=utf-8 71.0486ms
[2025-06-06 13:29:15.925 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_vs/browserLink - 200 null text/javascript; charset=UTF-8 92.4865ms
[2025-06-06 13:29:16.006 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-06 13:29:16.007 +05:30 INF] Hosting environment: dev
[2025-06-06 13:29:16.007 +05:30 INF] Content root path: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host
[2025-06-06 13:29:16.016 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - null null
[2025-06-06 13:29:16.328 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:29:16.330 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-06 13:29:16.333 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-06 13:29:16.334 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:29:16.334 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:29:16.335 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-06 13:29:16.335 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-06 13:29:16.491 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 474.8346ms
[2025-06-06 13:30:27.378 +05:30 INF] Executed DbCommand (49ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:30:27.776 +05:30 INF] Executed DbCommand (40ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-06 13:30:27.867 +05:30 INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-06 13:30:28.282 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-06 13:30:28.626 +05:30 INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:30:28.979 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-06 13:30:29.261 +05:30 INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:30:29.437 +05:30 INF] Connection to sleep's Database Succeeded.
[2025-06-06 13:30:29.753 +05:30 INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:30:29.975 +05:30 INF] Connection to neon's Database Succeeded.
[2025-06-06 13:30:30.255 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-06 13:30:30.330 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-06 13:30:30.382 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_vs/browserLink - null null
[2025-06-06 13:30:30.382 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-06 13:30:30.457 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - 200 16501 application/javascript; charset=utf-8 82.667ms
[2025-06-06 13:30:30.474 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_vs/browserLink - 200 null text/javascript; charset=UTF-8 101.2103ms
[2025-06-06 13:30:30.540 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-06 13:30:30.540 +05:30 INF] Hosting environment: dev
[2025-06-06 13:30:30.540 +05:30 INF] Content root path: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host
[2025-06-06 13:30:30.577 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - null null
[2025-06-06 13:30:30.881 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:30:30.884 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-06 13:30:30.888 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-06 13:30:30.889 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:30:30.890 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:30:30.891 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-06 13:30:30.892 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-06 13:30:31.074 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 497.5239ms
[2025-06-06 13:30:57.588 +05:30 INF] Executed DbCommand (40ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:30:58.021 +05:30 INF] Executed DbCommand (42ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-06 13:30:58.111 +05:30 INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-06 13:30:58.539 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-06 13:30:58.888 +05:30 INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:30:59.158 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-06 13:30:59.487 +05:30 INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:30:59.708 +05:30 INF] Connection to sleep's Database Succeeded.
[2025-06-06 13:31:00.085 +05:30 INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-06 13:31:00.302 +05:30 INF] Connection to neon's Database Succeeded.
[2025-06-06 13:31:00.593 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-06 13:31:00.668 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-06 13:31:00.719 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-06 13:31:00.719 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_vs/browserLink - null null
[2025-06-06 13:31:00.796 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - 200 16501 application/javascript; charset=utf-8 83.3562ms
[2025-06-06 13:31:00.806 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_vs/browserLink - 200 null text/javascript; charset=UTF-8 95.7052ms
[2025-06-06 13:31:00.870 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-06 13:31:00.870 +05:30 INF] Hosting environment: dev
[2025-06-06 13:31:00.871 +05:30 INF] Content root path: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host
[2025-06-06 13:31:00.894 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - null null
[2025-06-06 13:31:01.211 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:31:01.214 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-06 13:31:01.218 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-06 13:31:01.219 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:31:01.220 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-06 13:31:01.221 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-06 13:31:01.221 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-06 13:31:01.389 +05:30 ERR] Connection ID "18374686506246733869", Request ID "4000002e-0006-ff00-b63f-84710c7967bb": An unhandled exception was thrown by the application.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - Web.Host.Controllers.SubscriptionsController.GetSubscriptionsAsync (Web.Host). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate schema for type - Shared.Common.Response.PaginatedResult`1[Application.Subscriptions.DTOs.SubscriptionDto]. See inner exception
 ---> System.InvalidOperationException: Can't use schemaId "$SubscriptionDto" for type "$Application.Subscriptions.DTOs.SubscriptionDto". The same schemaId is already used for type "$Application.Products.DTOs.SubscriptionDto"
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaRepository.RegisterType(Type type, String schemaId)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateArraySchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__1()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForMember(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, DataProperty dataProperty)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateObjectSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__3()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.CreateResponseMediaType(Type modelType, SchemaRepository schemaRespository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.<>c__DisplayClass42_0.<GenerateResponse>b__2(String contentType)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponse(ApiDescription apiDescription, SchemaRepository schemaRepository, String statusCode, ApiResponseType apiResponseType)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponses(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Infrastructure.Common.CurrentUserMiddleware.InvokeAsync(HttpContext context, ICurrentUserInitializer currentUserInitializer) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\Common\CurrentUserMiddleware.cs:line 27
   at Infrastructure.MultiTenancy.Middleware.TenantMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\MultiTenancy\Middleware\TenantMiddleware.cs:line 155
   at Finbuckle.MultiTenant.AspNetCore.MultiTenantMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.WebTools.BrowserLink.Net.BrowserLinkMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Watch.BrowserRefresh.BrowserRefreshMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Server.IIS.Core.IISHttpContextOfT`1.ProcessRequestAsync()
[2025-06-06 13:31:01.403 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - 500 null null 509.9173ms
