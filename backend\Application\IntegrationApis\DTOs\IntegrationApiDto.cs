namespace Application.IntegrationApis.DTOs;

/// <summary>
/// Integration API DTO (renamed to ViewIntegrationApiDto for consistency)
/// </summary>
public class ViewIntegrationApiDto
{
    /// <summary>
    /// Integration API ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Product ID this integration API belongs to
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Product name
    /// </summary>
    public string ProductName { get; set; } = string.Empty;

    /// <summary>
    /// API name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// API endpoint URL
    /// </summary>
    public string EndpointUrl { get; set; } = string.Empty;

    /// <summary>
    /// API schema definition stored as JSON
    /// </summary>
    public string? Schema { get; set; }

    /// <summary>
    /// Whether the API is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Created at
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by
    /// </summary>
    public Guid? CreatedBy { get; set; }

    /// <summary>
    /// Modified at
    /// </summary>
    public DateTime ModifiedAt { get; set; }

    /// <summary>
    /// Modified by
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}

/// <summary>
/// Create Integration API DTO
/// </summary>
public class CreateIntegrationApiDto
{
    /// <summary>
    /// Product ID this integration API belongs to
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// API name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// API endpoint URL
    /// </summary>
    public string EndpointUrl { get; set; } = string.Empty;

    /// <summary>
    /// API schema definition stored as JSON
    /// </summary>
    public string? Schema { get; set; }

    /// <summary>
    /// Whether the API is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Update Integration API DTO
/// </summary>
public class UpdateIntegrationApiDto
{
    /// <summary>
    /// Integration API ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Product ID this integration API belongs to
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// API name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// API endpoint URL
    /// </summary>
    public string EndpointUrl { get; set; } = string.Empty;

    /// <summary>
    /// API schema definition stored as JSON
    /// </summary>
    public string? Schema { get; set; }

    /// <summary>
    /// Whether the API is active
    /// </summary>
    public bool IsActive { get; set; }
}
