using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Object entity - stores object types/templates like Tower, Floor, Unit
/// </summary>
public class Object : AuditableEntity, IAggregateRoot
{
    /// <summary>
    /// Feature ID this object belongs to
    /// </summary>
    public Guid FeatureId { get; set; }

    /// <summary>
    /// Parent object ID for hierarchical structure
    /// </summary>
    public Guid? ParentObjectId { get; set; }

    /// <summary>
    /// Object name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Object description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Whether the object is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    // Navigation properties
    /// <summary>
    /// Feature this object belongs to
    /// </summary>
    public virtual Feature Feature { get; set; } = null!;

    /// <summary>
    /// Parent object
    /// </summary>
    public virtual Object? ParentObject { get; set; }

    /// <summary>
    /// Child objects
    /// </summary>
    public virtual ICollection<Object> ChildObjects { get; set; } = new List<Object>();

    /// <summary>
    /// Object metadata links
    /// </summary>
    public virtual ICollection<ObjectMetadata> ObjectMetadata { get; set; } = new List<ObjectMetadata>();

    /// <summary>
    /// Integration configurations for this object
    /// </summary>
    public virtual ICollection<IntegrationConfiguration> IntegrationConfigurations { get; set; } = new List<IntegrationConfiguration>();

    /// <summary>
    /// Conflict resolutions for this object
    /// </summary>
    public virtual ICollection<ConflictResolution> ConflictResolutions { get; set; } = new List<ConflictResolution>();

    /// <summary>
    /// Sync histories for this object
    /// </summary>
    public virtual ICollection<SyncHistory> SyncHistories { get; set; } = new List<SyncHistory>();
}
