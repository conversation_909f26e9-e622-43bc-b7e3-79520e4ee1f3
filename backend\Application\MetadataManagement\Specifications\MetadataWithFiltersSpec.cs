using Ardalis.Specification;
using Domain.Entities;

namespace Application.MetadataManagement.Specifications;

/// <summary>
/// Specification to get Metadata with filters
/// </summary>
public class MetadataWithFiltersSpec : Specification<Domain.Entities.Metadata>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public MetadataWithFiltersSpec(string? searchTerm, Guid? dataTypeId, bool? isVisible, string? orderBy, int skip = 0, int take = 0)
    {
        Query.Where(m => !m.IsDeleted);

        if (!string.IsNullOrEmpty(searchTerm))
        {
            Query.Where(m => m.MetadataKey.Contains(searchTerm) || 
                             (m.DisplayLabel != null && m.DisplayLabel.Contains(searchTerm)));
        }

        if (dataTypeId.HasValue)
        {
            Query.Where(m => m.DataTypeId == dataTypeId.Value);
        }

        if (isVisible.HasValue)
        {
            Query.Where(m => m.IsVisible == isVisible.Value);
        }

        // Include related data
        Query.Include(m => m.DataType);

        // Apply ordering
        if (!string.IsNullOrEmpty(orderBy))
        {
            switch (orderBy.ToLower())
            {
                case "key":
                    Query.OrderBy(m => m.MetadataKey);
                    break;
                case "key_desc":
                    Query.OrderByDescending(m => m.MetadataKey);
                    break;
                case "displayLabel":
                    Query.OrderBy(m => m.DisplayLabel);
                    break;
                case "displayLabel_desc":
                    Query.OrderByDescending(m => m.DisplayLabel);
                    break;
                case "created":
                    Query.OrderBy(m => m.CreatedAt);
                    break;
                case "created_desc":
                    Query.OrderByDescending(m => m.CreatedAt);
                    break;
                default:
                    Query.OrderBy(m => m.MetadataKey);
                    break;
            }
        }
        else
        {
            Query.OrderBy(m => m.MetadataKey);
        }

        // Apply pagination
        if (skip > 0)
        {
            Query.Skip(skip);
        }

        if (take > 0)
        {
            Query.Take(take);
        }
    }
}
