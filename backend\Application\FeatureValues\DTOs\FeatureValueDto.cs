namespace Application.FeatureValues.DTOs;

/// <summary>
/// FeatureValue DTO
/// </summary>
public class FeatureValueDto
{
    /// <summary>
    /// FeatureValue ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Feature metadata ID
    /// </summary>
    public Guid FeatureMetadataId { get; set; }

    /// <summary>
    /// Feature name
    /// </summary>
    public string? FeatureName { get; set; }

    /// <summary>
    /// Metadata key
    /// </summary>
    public string? MetadataKey { get; set; }

    /// <summary>
    /// Reference ID for grouping related values
    /// </summary>
    public Guid? RefId { get; set; }

    /// <summary>
    /// Parent feature value ID for hierarchical structure
    /// </summary>
    public Guid? ParentFeatureValueId { get; set; }

    /// <summary>
    /// Parent feature value
    /// </summary>
    public string? ParentFeatureValue { get; set; }

    /// <summary>
    /// The actual value as text
    /// </summary>
    public string? Value { get; set; }

    /// <summary>
    /// Number of child values
    /// </summary>
    public int ChildValuesCount { get; set; }

    /// <summary>
    /// Created at
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by
    /// </summary>
    public Guid CreatedBy { get; set; }

    /// <summary>
    /// Modified at
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Modified by
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}
