using Application.Features.DTOs;
using Application.Features.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.Features.Queries;

/// <summary>
/// Get feature by ID query handler
/// </summary>
public class GetFeatureByIdQueryHandler : IRequestHandler<GetFeatureByIdQuery, Result<FeatureDto>>
{
    private readonly IReadRepository<Feature> _featureRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetFeatureByIdQueryHandler(IReadRepository<Feature> featureRepository)
    {
        _featureRepository = featureRepository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<FeatureDto>> Handle(GetFeatureByIdQuery request, CancellationToken cancellationToken)
    {
        // Get feature using specification (tenant isolation handled by Finbuckle.MultiTenant)
        var featureSpec = new FeatureByIdSpec(request.Id);
        var feature = await _featureRepository.GetBySpecAsync(featureSpec, cancellationToken);

        if (feature == null)
        {
            return Result<FeatureDto>.Failure("Feature not found.");
        }

        var dto = feature.Adapt<FeatureDto>();
        return Result<FeatureDto>.Success(dto);
    }
}
