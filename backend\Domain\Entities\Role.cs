using Domain.Common.Contracts;
using Finbuckle.MultiTenant;
using Microsoft.AspNetCore.Identity;

namespace Domain.Entities;

/// <summary>
/// Role entity - combines Identity authorization with business logic
/// </summary>
public class Role : IdentityRole<Guid>, IAggregateRoot
{
    /// <summary>
    /// Product ID this role belongs to
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Role description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Whether this is a system role
    /// </summary>
    public bool IsSystemRole { get; set; } = false;

    /// <summary>
    /// Permissions as JSON
    /// </summary>
    public string Permissions { get; set; } = "{}";

    /// <summary>
    /// Whether the role is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// When the role was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Who created the role
    /// </summary>
    public Guid? CreatedBy { get; set; }

    /// <summary>
    /// When the role was last modified
    /// </summary>
    public DateTime ModifiedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Who last modified the role
    /// </summary>
    public Guid? ModifiedBy { get; set; }

    /// <summary>
    /// Whether the role is soft deleted
    /// </summary>
    public bool IsDeleted { get; set; } = false;

    // Navigation properties
    /// <summary>
    /// Product this role belongs to
    /// </summary>
    public virtual Product Product { get; set; } = null!;

    /// <summary>
    /// User role mappings
    /// </summary>
    public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();

    /// <summary>
    /// Role metadata links
    /// </summary>
    public virtual ICollection<RoleMetadata> RoleMetadata { get; set; } = new List<RoleMetadata>();

    /// <summary>
    /// Field mappings associated with this role
    /// </summary>
    public virtual ICollection<FieldMapping> FieldMappings { get; set; } = new List<FieldMapping>();
}
