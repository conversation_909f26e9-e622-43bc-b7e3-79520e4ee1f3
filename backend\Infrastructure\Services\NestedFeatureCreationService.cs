using Abstraction.Database.Repositories;
using Application.Features.DTOs;
using Application.Features.Interfaces;
using Ardalis.Specification;
using Domain.Entities;
using Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;
using System.Globalization;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace Infrastructure.Services;

/// <summary>
/// Service for creating features with deeply nested data structures
/// </summary>
public class NestedFeatureCreationService : INestedFeatureCreationService
{
    private readonly IRepository<Feature> _featureRepository;
    private readonly IRepository<Product> _productRepository;
    private readonly IRepository<Domain.Entities.Object> _objectRepository;
    private readonly IRepository<Metadata> _metadataRepository;
    private readonly IRepository<DataType> _dataTypeRepository;
    private readonly IRepository<FeatureMetadata> _featureMetadataRepository;
    private readonly IRepository<FeatureValue> _featureValueRepository;
    private readonly IRepository<ObjectMetadata> _objectMetadataRepository;
    private readonly IRepository<ObjectValue> _objectValueRepository;
    private readonly ApplicationDbContext _dbContext;
    private readonly ILogger<NestedFeatureCreationService> _logger;

    // Cache for DataType lookups to avoid repeated database queries
    private readonly Dictionary<string, Guid> _dataTypeCache = new();

    /// <summary>
    /// Constructor
    /// </summary>
    public NestedFeatureCreationService(
        IRepository<Feature> featureRepository,
        IRepository<Product> productRepository,
        IRepository<Domain.Entities.Object> objectRepository,
        IRepository<Metadata> metadataRepository,
        IRepository<DataType> dataTypeRepository,
        IRepository<FeatureMetadata> featureMetadataRepository,
        IRepository<FeatureValue> featureValueRepository,
        IRepository<ObjectMetadata> objectMetadataRepository,
        IRepository<ObjectValue> objectValueRepository,
        ApplicationDbContext dbContext,
        ILogger<NestedFeatureCreationService> logger)
    {
        _featureRepository = featureRepository;
        _productRepository = productRepository;
        _objectRepository = objectRepository;
        _metadataRepository = metadataRepository;
        _dataTypeRepository = dataTypeRepository;
        _featureMetadataRepository = featureMetadataRepository;
        _featureValueRepository = featureValueRepository;
        _objectMetadataRepository = objectMetadataRepository;
        _objectValueRepository = objectValueRepository;
        _dbContext = dbContext;
        _logger = logger;
    }

    /// <summary>
    /// Create a feature with all nested objects, metadata, and values
    /// </summary>
    public async Task<Result<CreateFeatureWithNestedDataResponseDto>> CreateFeatureWithNestedDataAsync(
        FeatureCreationDto featureData,
        string tenantId,
        string userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating feature with nested data: {FeatureName}", featureData.Name);

            var response = new CreateFeatureWithNestedDataResponseDto();
            var warnings = new List<string>();
            var dataTypeMappings = new Dictionary<string, Guid>();

            // Start transaction
            using var transaction = await _dbContext.Database.BeginTransactionAsync(cancellationToken);

            try
            {
                // Step 1: Validate product exists
                var productValidation = await ValidateProductAccessAsync(featureData.ProductId, tenantId, cancellationToken);
                if (!productValidation.Succeeded)
                {
                    return Result<CreateFeatureWithNestedDataResponseDto>.Failure(productValidation.Message ?? "Product validation failed");
                }

                // Step 2: Create or get existing feature
                Feature feature;
                bool isNewFeature = false;

                if (featureData.FeatureId.HasValue)
                {
                    feature = await _featureRepository.GetByIdAsync(featureData.FeatureId.Value, cancellationToken);
                    if (feature == null || feature.IsDeleted)
                    {
                        return Result<CreateFeatureWithNestedDataResponseDto>.Failure("Feature not found");
                    }
                    _logger.LogInformation("Using existing feature: {FeatureId}", feature.Id);
                }
                else
                {
                    // Check if feature with same name exists
                    var existingFeature = await _featureRepository.FirstOrDefaultAsync(
                        new FeatureByProductAndNameSpec(featureData.ProductId, featureData.Name),
                        cancellationToken);

                    if (existingFeature != null)
                    {
                        return Result<CreateFeatureWithNestedDataResponseDto>.Failure($"Feature with name '{featureData.Name}' already exists for this product");
                    }

                    feature = new Feature
                    {
                        ProductId = featureData.ProductId,
                        Name = featureData.Name,
                        Description = featureData.Description,
                        IsDefault = featureData.IsDefault,
                        IsActive = featureData.IsActive
                    };

                    await _featureRepository.AddAsync(feature, cancellationToken);
                    isNewFeature = true;
                    _logger.LogInformation("Created new feature: {FeatureId}", feature.Id);
                }

                response.Feature = new FeatureCreationResultDto
                {
                    Id = feature.Id,
                    Name = feature.Name,
                    ProductId = feature.ProductId,
                    IsNewFeature = isNewFeature
                };

                // Step 3: Create feature metadata and values
                if (featureData.MetaJson.HasValue || featureData.Values.Any())
                {
                    var featureMetaResult = await CreateFeatureMetadataAndValuesAsync(
                        feature.Id,
                        featureData.MetaJson,
                        featureData.Values,
                        tenantId,
                        userId,
                        cancellationToken);

                    if (featureMetaResult.Succeeded)
                    {
                        response.Feature.FeatureMetadataCreated = featureMetaResult.Data.MetadataCreated;
                        response.Feature.FeatureValuesCreated = featureMetaResult.Data.ValuesCreated;
                    }
                    else
                    {
                        warnings.Add($"Failed to create feature metadata: {featureMetaResult.Message}");
                    }
                }

                // Step 4: Create objects with their metadata and values
                foreach (var objectData in featureData.Objects)
                {
                    var objectResult = await CreateObjectWithNestedDataAsync(
                        feature.Id,
                        objectData,
                        tenantId,
                        userId,
                        null, // No parent object for top-level objects
                        dataTypeMappings,
                        warnings,
                        cancellationToken);

                    if (objectResult.Succeeded)
                    {
                        response.Objects.Add(objectResult.Data!);
                        response.MetadataCreated += objectResult.Data!.MetadataCreated;
                        response.ObjectValuesCreated += objectResult.Data!.ObjectValuesCreated;
                        response.ChildObjectsCreated += objectResult.Data!.ChildObjects.Count;
                        response.ChildObjectValuesCreated += objectResult.Data!.ChildObjects.Sum(c => c.ObjectValuesCreated);
                    }
                    else
                    {
                        warnings.Add($"Failed to create object '{objectData.Name}': {objectResult.Message}");
                    }
                }

                // Commit transaction
                await transaction.CommitAsync(cancellationToken);

                response.ProcessingSummary = new ProcessingSummaryDto
                {
                    TransactionsExecuted = 1,
                    Warnings = warnings,
                    DataTypeMappings = dataTypeMappings
                };

                _logger.LogInformation("Successfully created feature with nested data: {FeatureId}, Objects: {ObjectCount}, Metadata: {MetadataCount}",
                    feature.Id, response.Objects.Count, response.MetadataCreated);

                return Result<CreateFeatureWithNestedDataResponseDto>.Success(response);
            }
            catch (Exception)
            {
                await transaction.RollbackAsync(cancellationToken);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating feature with nested data: {FeatureName}", featureData.Name);
            return Result<CreateFeatureWithNestedDataResponseDto>.Failure("An error occurred while creating the feature with nested data");
        }
    }

    /// <summary>
    /// Create an object with its nested data
    /// </summary>
    private async Task<Result<ObjectCreationResultDto>> CreateObjectWithNestedDataAsync(
        Guid featureId,
        ObjectCreationDto objectData,
        string tenantId,
        string userId,
        Guid? parentObjectId,
        Dictionary<string, Guid> dataTypeMappings,
        List<string> warnings,
        CancellationToken cancellationToken)
    {
        try
        {
            // Create the object
            var obj = new Domain.Entities.Object
            {
                FeatureId = featureId,
                ParentObjectId = parentObjectId,
                Name = objectData.Name,
                Description = objectData.Description,
                IsActive = objectData.IsActive
            };

            await _objectRepository.AddAsync(obj, cancellationToken);

            var result = new ObjectCreationResultDto
            {
                Id = obj.Id,
                Name = obj.Name,
                FeatureId = featureId
            };

            // Create object metadata from MetaJson
            if (objectData.MetaJson.HasValue)
            {
                var metadataResult = await CreateMetadataFromJsonAsync(objectData.MetaJson.Value, tenantId, userId, cancellationToken);
                if (metadataResult.Succeeded)
                {
                    // Link metadata to object
                    foreach (var metadataId in metadataResult.Data!)
                    {
                        var objectMetadata = new ObjectMetadata
                        {
                            ObjectId = obj.Id,
                            MetadataId = metadataId,
                            IsActive = true,
                            ShouldVisibleInList = true,
                            ShouldVisibleInEdit = true,
                            ShouldVisibleInCreate = true,
                            ShouldVisibleInView = true,
                            IsCalculate = false
                        };

                        await _objectMetadataRepository.AddAsync(objectMetadata, cancellationToken);
                        result.MetadataCreated++;
                    }
                }
                else
                {
                    warnings.Add($"Failed to create metadata for object '{objectData.Name}': {metadataResult.Message}");
                }
            }

            // Create object values from MetaValues
            if (objectData.MetaValues.Any())
            {
                var valuesResult = await CreateObjectValuesFromMetaValuesAsync(
                    obj.Id, objectData.MetaValues, tenantId, userId, null, cancellationToken);

                if (valuesResult.Succeeded)
                {
                    result.ObjectValuesCreated = valuesResult.Data;
                }
                else
                {
                    warnings.Add($"Failed to create values for object '{objectData.Name}': {valuesResult.Message}");
                }
            }

            // Create child objects
            foreach (var childObjectData in objectData.ChildObjects)
            {
                var childResult = await CreateChildObjectWithDataAsync(
                    obj.Id, childObjectData, tenantId, userId, dataTypeMappings, warnings, cancellationToken);

                if (childResult.Succeeded)
                {
                    result.ChildObjects.Add(childResult.Data!);
                }
                else
                {
                    warnings.Add($"Failed to create child object '{childObjectData.Name}': {childResult.Message}");
                }
            }

            return Result<ObjectCreationResultDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating object with nested data: {ObjectName}", objectData.Name);
            return Result<ObjectCreationResultDto>.Failure($"Error creating object: {ex.Message}");
        }
    }

    /// <summary>
    /// Create a child object with its data
    /// </summary>
    private async Task<Result<ChildObjectCreationResultDto>> CreateChildObjectWithDataAsync(
        Guid parentObjectId,
        ChildObjectCreationDto childObjectData,
        string tenantId,
        string userId,
        Dictionary<string, Guid> dataTypeMappings,
        List<string> warnings,
        CancellationToken cancellationToken)
    {
        try
        {
            // Get parent object to get feature ID
            var parentObject = await _objectRepository.GetByIdAsync(parentObjectId, cancellationToken);
            if (parentObject == null)
            {
                return Result<ChildObjectCreationResultDto>.Failure("Parent object not found");
            }

            // Create the child object
            var childObj = new Domain.Entities.Object
            {
                FeatureId = parentObject.FeatureId,
                ParentObjectId = parentObjectId,
                Name = childObjectData.Name,
                Description = childObjectData.Description,
                IsActive = childObjectData.IsActive
            };

            await _objectRepository.AddAsync(childObj, cancellationToken);

            var result = new ChildObjectCreationResultDto
            {
                Id = childObj.Id,
                Name = childObj.Name,
                ParentObjectId = parentObjectId
            };

            // Create child object metadata from MetaJson
            if (childObjectData.MetaJson.HasValue)
            {
                var metadataResult = await CreateMetadataFromJsonAsync(childObjectData.MetaJson.Value, tenantId, userId, cancellationToken);
                if (metadataResult.Succeeded)
                {
                    // Link metadata to child object
                    foreach (var metadataId in metadataResult.Data!)
                    {
                        var objectMetadata = new ObjectMetadata
                        {
                            ObjectId = childObj.Id,
                            MetadataId = metadataId,
                            IsActive = true,
                            ShouldVisibleInList = true,
                            ShouldVisibleInEdit = true,
                            ShouldVisibleInCreate = true,
                            ShouldVisibleInView = true,
                            IsCalculate = false
                        };

                        await _objectMetadataRepository.AddAsync(objectMetadata, cancellationToken);
                        result.MetadataCreated++;
                    }
                }
                else
                {
                    warnings.Add($"Failed to create metadata for child object '{childObjectData.Name}': {metadataResult.Message}");
                }
            }

            // Create child object values from MetaValues
            if (childObjectData.MetaValues.Any())
            {
                var valuesResult = await CreateObjectValuesFromMetaValuesAsync(
                    childObj.Id, childObjectData.MetaValues, tenantId, userId, null, cancellationToken);

                if (valuesResult.Succeeded)
                {
                    result.ObjectValuesCreated = valuesResult.Data;
                }
                else
                {
                    warnings.Add($"Failed to create values for child object '{childObjectData.Name}': {valuesResult.Message}");
                }
            }

            return Result<ChildObjectCreationResultDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating child object: {ChildObjectName}", childObjectData.Name);
            return Result<ChildObjectCreationResultDto>.Failure($"Error creating child object: {ex.Message}");
        }
    }

    /// <summary>
    /// Infer data type from a value and get the corresponding DataType ID from database
    /// </summary>
    public async Task<Guid> InferDataTypeAsync(object value, string fieldName, CancellationToken cancellationToken = default)
    {
        try
        {
            string dataTypeName = DetermineDataTypeName(value, fieldName);

            // Check cache first
            if (_dataTypeCache.TryGetValue(dataTypeName, out var cachedId))
            {
                return cachedId;
            }

            // Query database for the data type
            var dataType = await _dataTypeRepository.FirstOrDefaultAsync(
                new DataTypeByNameSpec(dataTypeName),
                cancellationToken);

            if (dataType != null)
            {
                _dataTypeCache[dataTypeName] = dataType.Id;
                return dataType.Id;
            }

            // If specific type not found, try to get a generic text type
            var textDataType = await _dataTypeRepository.FirstOrDefaultAsync(
                new DataTypeByNameSpec("text"),
                cancellationToken);

            if (textDataType != null)
            {
                _dataTypeCache["text"] = textDataType.Id;
                return textDataType.Id;
            }

            // If no text type found, get the first available data type
            var firstDataType = await _dataTypeRepository.FirstOrDefaultAsync(
                new ActiveDataTypesSpec(),
                cancellationToken);

            if (firstDataType != null)
            {
                _logger.LogWarning("Using first available data type {DataTypeName} for field {FieldName}",
                    firstDataType.Name, fieldName);
                return firstDataType.Id;
            }

            throw new InvalidOperationException("No DataTypes found in the database. Please ensure DataTypes are properly seeded.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error inferring data type for field {FieldName}", fieldName);
            throw;
        }
    }

    /// <summary>
    /// Determine the data type name based on value and field name
    /// </summary>
    private static string DetermineDataTypeName(object value, string fieldName)
    {
        if (value == null)
            return "text";

        var valueStr = value.ToString();
        if (string.IsNullOrEmpty(valueStr))
            return "text";

        var fieldNameLower = fieldName.ToLowerInvariant();

        // Check for specific patterns based on field name
        if (fieldNameLower.Contains("email"))
            return "email";

        if (fieldNameLower.Contains("url") || fieldNameLower.Contains("link"))
            return "url";

        if (fieldNameLower.Contains("phone") || fieldNameLower.Contains("mobile") || fieldNameLower.Contains("contact"))
            return "phone";

        if (fieldNameLower.Contains("date") && !fieldNameLower.Contains("time"))
        {
            if (DateTime.TryParse(valueStr, out _))
                return "date";
        }

        if (fieldNameLower.Contains("time") || fieldNameLower.Contains("datetime") || fieldNameLower.Contains("createdon") || fieldNameLower.Contains("modifiedon"))
        {
            if (DateTime.TryParse(valueStr, out _))
                return "datetime";
        }

        // Check value patterns
        if (bool.TryParse(valueStr, out _))
            return "boolean";

        if (decimal.TryParse(valueStr, out var decimalValue))
        {
            if (decimalValue % 1 == 0)
                return "number";
            else
                return "decimal";
        }

        if (DateTime.TryParse(valueStr, out _))
            return "datetime";

        if (Regex.IsMatch(valueStr, @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"))
            return "email";

        if (Regex.IsMatch(valueStr, @"^https?://"))
            return "url";

        // Check if it's a GUID
        if (Guid.TryParse(valueStr, out _))
            return "text"; // GUIDs are stored as text

        // Default to text
        return "text";
    }

    /// <summary>
    /// Create metadata from JSON structure
    /// </summary>
    public async Task<Result<List<Guid>>> CreateMetadataFromJsonAsync(
        JsonElement metaJson,
        string tenantId,
        string userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var metadataIds = new List<Guid>();

            if (metaJson.ValueKind == JsonValueKind.Object)
            {
                foreach (var property in metaJson.EnumerateObject())
                {
                    var metadataResult = await GetOrCreateMetadataAsync(
                        property.Name,
                        property.Value,
                        tenantId,
                        userId,
                        cancellationToken);

                    if (metadataResult.Succeeded)
                    {
                        metadataIds.Add(metadataResult.Data);
                    }
                    else
                    {
                        _logger.LogWarning("Failed to create metadata for field {FieldName}: {Error}",
                            property.Name, metadataResult.Message);
                    }
                }
            }

            return Result<List<Guid>>.Success(metadataIds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating metadata from JSON");
            return Result<List<Guid>>.Failure("Error creating metadata from JSON");
        }
    }

    /// <summary>
    /// Create object values from metadata values array
    /// </summary>
    public async Task<Result<int>> CreateObjectValuesFromMetaValuesAsync(
        Guid objectId,
        List<Dictionary<string, object>> metaValues,
        string tenantId,
        string userId,
        Guid? parentObjectValueId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            int valuesCreated = 0;

            foreach (var metaValueSet in metaValues)
            {
                var refId = Guid.NewGuid(); // Group related values with same RefId

                foreach (var kvp in metaValueSet)
                {
                    var metadataResult = await GetOrCreateMetadataAsync(
                        kvp.Key,
                        kvp.Value,
                        tenantId,
                        userId,
                        cancellationToken);

                    if (!metadataResult.Succeeded)
                    {
                        _logger.LogWarning("Failed to get/create metadata for field {FieldName}: {Error}",
                            kvp.Key, metadataResult.Message);
                        continue;
                    }

                    // Get or create ObjectMetadata link
                    var objectMetadata = await _objectMetadataRepository.FirstOrDefaultAsync(
                        new ObjectMetadataByObjectAndMetadataSpec(objectId, metadataResult.Data),
                        cancellationToken);

                    if (objectMetadata == null)
                    {
                        objectMetadata = new ObjectMetadata
                        {
                            ObjectId = objectId,
                            MetadataId = metadataResult.Data,
                            IsActive = true,
                            ShouldVisibleInList = true,
                            ShouldVisibleInEdit = true,
                            ShouldVisibleInCreate = true,
                            ShouldVisibleInView = true,
                            IsCalculate = false
                        };

                        await _objectMetadataRepository.AddAsync(objectMetadata, cancellationToken);
                    }

                    // Create ObjectValue
                    var objectValue = new ObjectValue
                    {
                        ObjectMetadataId = objectMetadata.Id,
                        RefId = refId,
                        ParentObjectValueId = parentObjectValueId,
                        Value = kvp.Value?.ToString()
                    };

                    await _objectValueRepository.AddAsync(objectValue, cancellationToken);
                    valuesCreated++;
                }
            }

            return Result<int>.Success(valuesCreated);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating object values from meta values");
            return Result<int>.Failure("Error creating object values");
        }
    }

    /// <summary>
    /// Create feature metadata and values from feature data
    /// </summary>
    public async Task<Result<(int MetadataCreated, int ValuesCreated)>> CreateFeatureMetadataAndValuesAsync(
        Guid featureId,
        JsonElement? metaJson,
        Dictionary<string, object> values,
        string tenantId,
        string userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            int metadataCreated = 0;
            int valuesCreated = 0;

            // Create metadata from MetaJson
            if (metaJson.HasValue)
            {
                var metadataResult = await CreateMetadataFromJsonAsync(metaJson.Value, tenantId, userId, cancellationToken);
                if (metadataResult.Succeeded)
                {
                    // Link metadata to feature
                    foreach (var metadataId in metadataResult.Data!)
                    {
                        var featureMetadata = new FeatureMetadata
                        {
                            FeatureId = featureId,
                            MetadataId = metadataId,
                            IsActive = true,
                            ShouldVisibleInList = true,
                            ShouldVisibleInEdit = true,
                            ShouldVisibleInCreate = true,
                            ShouldVisibleInView = true,
                            IsCalculate = false
                        };

                        await _featureMetadataRepository.AddAsync(featureMetadata, cancellationToken);
                        metadataCreated++;
                    }
                }
            }

            // Create feature values
            if (values.Any())
            {
                var refId = Guid.NewGuid();

                foreach (var kvp in values)
                {
                    var metadataResult = await GetOrCreateMetadataAsync(
                        kvp.Key,
                        kvp.Value,
                        tenantId,
                        userId,
                        cancellationToken);

                    if (!metadataResult.Succeeded)
                    {
                        _logger.LogWarning("Failed to get/create metadata for feature field {FieldName}: {Error}",
                            kvp.Key, metadataResult.Message);
                        continue;
                    }

                    // Get or create FeatureMetadata link
                    var featureMetadata = await _featureMetadataRepository.FirstOrDefaultAsync(
                        new FeatureMetadataByFeatureAndMetadataSpec(featureId, metadataResult.Data),
                        cancellationToken);

                    if (featureMetadata == null)
                    {
                        featureMetadata = new FeatureMetadata
                        {
                            FeatureId = featureId,
                            MetadataId = metadataResult.Data,
                            IsActive = true,
                            ShouldVisibleInList = true,
                            ShouldVisibleInEdit = true,
                            ShouldVisibleInCreate = true,
                            ShouldVisibleInView = true,
                            IsCalculate = false
                        };

                        await _featureMetadataRepository.AddAsync(featureMetadata, cancellationToken);
                        metadataCreated++;
                    }

                    // Create FeatureValue
                    var featureValue = new FeatureValue
                    {
                        FeatureMetadataId = featureMetadata.Id,
                        RefId = refId,
                        Value = kvp.Value?.ToString()
                    };

                    await _featureValueRepository.AddAsync(featureValue, cancellationToken);
                    valuesCreated++;
                }
            }

            return Result<(int MetadataCreated, int ValuesCreated)>.Success((metadataCreated, valuesCreated));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating feature metadata and values");
            return Result<(int MetadataCreated, int ValuesCreated)>.Failure("Error creating feature metadata and values");
        }
    }

    /// <summary>
    /// Validate that the product exists and is accessible
    /// </summary>
    public async Task<Result<bool>> ValidateProductAccessAsync(
        Guid productId,
        string tenantId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var product = await _productRepository.GetByIdAsync(productId, cancellationToken);
            if (product == null || product.IsDeleted)
            {
                return Result<bool>.Failure("Product not found or has been deleted");
            }

            if (!product.IsActive)
            {
                return Result<bool>.Failure("Product is not active");
            }

            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating product access: {ProductId}", productId);
            return Result<bool>.Failure("Error validating product access");
        }
    }

    /// <summary>
    /// Get or create metadata for a field
    /// </summary>
    public async Task<Result<Guid>> GetOrCreateMetadataAsync(
        string fieldName,
        object value,
        string tenantId,
        string userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if metadata already exists
            var existingMetadata = await _metadataRepository.FirstOrDefaultAsync(
                new MetadataByKeySpec(fieldName),
                cancellationToken);

            if (existingMetadata != null)
            {
                return Result<Guid>.Success(existingMetadata.Id);
            }

            // Infer data type
            var dataTypeId = await InferDataTypeAsync(value, fieldName, cancellationToken);

            // Create new metadata
            var metadata = new Metadata
            {
                MetadataKey = fieldName,
                DataTypeId = dataTypeId,
                DisplayLabel = FormatDisplayLabel(fieldName),
                HelpText = $"Auto-generated metadata for {fieldName}",
                IsVisible = true,
                CustomIsRequired = false,
                FieldOrder = null
            };

            await _metadataRepository.AddAsync(metadata, cancellationToken);

            _logger.LogInformation("Created new metadata: {MetadataKey} with DataType: {DataTypeId}",
                fieldName, dataTypeId);

            return Result<Guid>.Success(metadata.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting or creating metadata for field: {FieldName}", fieldName);
            return Result<Guid>.Failure($"Error getting or creating metadata for field: {fieldName}");
        }
    }

    /// <summary>
    /// Format field name into a display label
    /// </summary>
    private static string FormatDisplayLabel(string fieldName)
    {
        if (string.IsNullOrEmpty(fieldName))
            return fieldName;

        // Convert camelCase/PascalCase to Title Case
        var result = Regex.Replace(fieldName, "([a-z])([A-Z])", "$1 $2");

        // Capitalize first letter
        return CultureInfo.CurrentCulture.TextInfo.ToTitleCase(result.ToLowerInvariant());
    }
}

/// <summary>
/// Specification to find feature by product and name
/// </summary>
public class FeatureByProductAndNameSpec : Specification<Feature>
{
    public FeatureByProductAndNameSpec(Guid productId, string name)
    {
        Query.Where(f => f.ProductId == productId && f.Name == name && !f.IsDeleted);
    }
}

/// <summary>
/// Specification to find ObjectMetadata by object and metadata
/// </summary>
public class ObjectMetadataByObjectAndMetadataSpec : Specification<ObjectMetadata>
{
    public ObjectMetadataByObjectAndMetadataSpec(Guid objectId, Guid metadataId)
    {
        Query.Where(om => om.ObjectId == objectId && om.MetadataId == metadataId && !om.IsDeleted);
    }
}

/// <summary>
/// Specification to find FeatureMetadata by feature and metadata
/// </summary>
public class FeatureMetadataByFeatureAndMetadataSpec : Specification<FeatureMetadata>
{
    public FeatureMetadataByFeatureAndMetadataSpec(Guid featureId, Guid metadataId)
    {
        Query.Where(fm => fm.FeatureId == featureId && fm.MetadataId == metadataId && !fm.IsDeleted);
    }
}

/// <summary>
/// Specification to find Metadata by key
/// </summary>
public class MetadataByKeySpec : Specification<Metadata>
{
    public MetadataByKeySpec(string metadataKey)
    {
        Query.Where(m => m.MetadataKey == metadataKey && !m.IsDeleted);
    }
}

/// <summary>
/// Specification to find DataType by name
/// </summary>
public class DataTypeByNameSpec : Specification<DataType>
{
    public DataTypeByNameSpec(string name)
    {
        Query.Where(dt => dt.Name == name && dt.IsActive && !dt.IsDeleted);
    }
}

/// <summary>
/// Specification to get active DataTypes
/// </summary>
public class ActiveDataTypesSpec : Specification<DataType>
{
    public ActiveDataTypesSpec()
    {
        Query.Where(dt => dt.IsActive && !dt.IsDeleted)
             .OrderBy(dt => dt.Name);
    }
}
