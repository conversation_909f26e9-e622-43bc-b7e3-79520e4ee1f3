using Ardalis.Specification;
using Domain.Entities;

namespace Application.Integrations.Specifications;

/// <summary>
/// Specification to get integrations with filters and pagination
/// </summary>
public class IntegrationsWithFiltersSpec : Specification<Integration>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public IntegrationsWithFiltersSpec(
        string? searchTerm,
        bool? isActive,
        Guid? productId,
        string? authType,
        int skip,
        int take)
    {
        Query.Where(i => true); // Base query

        if (!string.IsNullOrEmpty(searchTerm))
        {
            Query.Where(i => i.Name.Contains(searchTerm));
        }

        if (isActive.HasValue)
        {
            Query.Where(i => i.IsActive == isActive.Value);
        }

        if (productId.HasValue)
        {
            Query.Where(i => i.ProductId == productId.Value);
        }

        if (!string.IsNullOrEmpty(authType))
        {
            Query.Where(i => i.AuthType == authType);
        }

        Query.OrderBy(i => i.Name)
             .Skip(skip)
             .Take(take);
    }
}
