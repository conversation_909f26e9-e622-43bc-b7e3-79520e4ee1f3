using Ardalis.Specification;
using Domain.Entities;

namespace Application.ObjectValues.Specifications;

/// <summary>
/// Specification to get Object by Id
/// </summary>
public class ObjectByIdSpec : Specification<Domain.Entities.Object>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ObjectByIdSpec(Guid objectId)
    {
        Query.Where(o => o.Id == objectId);

        // Include parent object
        Query.Include(o => o.ParentObject);

        // Take only one record
        Query.Take(1);
    }
}
