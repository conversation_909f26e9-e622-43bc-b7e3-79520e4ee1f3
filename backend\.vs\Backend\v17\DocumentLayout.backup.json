{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\source\\repos\\this-applications\\backend\\web.host\\controllers\\subscriptionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\controllers\\subscriptionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\source\\repos\\this-applications\\backend\\web.host\\controllers\\objectvaluescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\controllers\\objectvaluescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\source\\repos\\this-applications\\backend\\web.host\\controllers\\objectmetadatacontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\controllers\\objectmetadatacontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\source\\repos\\this-applications\\backend\\web.host\\controllers\\featurevaluescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\controllers\\featurevaluescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\source\\repos\\this-applications\\backend\\web.host\\controllers\\featurescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\controllers\\featurescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 3, "Children": [{"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:128:0:{75188d03-9892-4ae2-abf1-207126247ce5}"}, {"$type": "Bookmark", "Name": "ST:130:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "SubscriptionsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Web.Host\\Controllers\\SubscriptionsController.cs", "RelativeDocumentMoniker": "Web.Host\\Controllers\\SubscriptionsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Web.Host\\Controllers\\SubscriptionsController.cs*", "RelativeToolTip": "Web.Host\\Controllers\\SubscriptionsController.cs*", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T07:58:58.569Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "ObjectMetadataController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Web.Host\\Controllers\\ObjectMetadataController.cs", "RelativeDocumentMoniker": "Web.Host\\Controllers\\ObjectMetadataController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Web.Host\\Controllers\\ObjectMetadataController.cs", "RelativeToolTip": "Web.Host\\Controllers\\ObjectMetadataController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T07:57:51.387Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "FeatureValuesController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Web.Host\\Controllers\\FeatureValuesController.cs", "RelativeDocumentMoniker": "Web.Host\\Controllers\\FeatureValuesController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Web.Host\\Controllers\\FeatureValuesController.cs", "RelativeToolTip": "Web.Host\\Controllers\\FeatureValuesController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACAAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T07:55:23.986Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "FeaturesController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Web.Host\\Controllers\\FeaturesController.cs", "RelativeDocumentMoniker": "Web.Host\\Controllers\\FeaturesController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Web.Host\\Controllers\\FeaturesController.cs", "RelativeToolTip": "Web.Host\\Controllers\\FeaturesController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T07:55:20.496Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "ObjectValuesController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Web.Host\\Controllers\\ObjectValuesController.cs", "RelativeDocumentMoniker": "Web.Host\\Controllers\\ObjectValuesController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Web.Host\\Controllers\\ObjectValuesController.cs", "RelativeToolTip": "Web.Host\\Controllers\\ObjectValuesController.cs", "ViewState": "AgIAAD8AAAAAAAAAAAAAAFEAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T07:36:50.329Z", "EditorCaption": ""}]}]}]}