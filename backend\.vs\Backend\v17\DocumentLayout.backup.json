{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\source\\repos\\this-applications\\backend\\web.host\\controllers\\objectvaluescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\controllers\\objectvaluescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|c:\\users\\<USER>\\source\\repos\\this-applications\\backend\\web.host\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{B286FAEF-55A4-45BC-B811-1D5FE0FB95ED}|Web.Host\\Web.Host.csproj|solutionrelative:web.host\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\GenerateControllers.ps1||{3B902123-F8A7-4915-9F01-361F908088D0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:GenerateControllers.ps1||{3B902123-F8A7-4915-9F01-361F908088D0}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\source\\repos\\this-applications\\backend\\application\\productmetadata\\queries\\getproductmetadataquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\productmetadata\\queries\\getproductmetadataquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|c:\\users\\<USER>\\source\\repos\\this-applications\\backend\\application\\productmetadata\\queries\\getproductmetadatabyidquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA12C854-A9F0-4206-A374-C296DAA78E9F}|Application\\Application.csproj|solutionrelative:application\\productmetadata\\queries\\getproductmetadatabyidquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|c:\\users\\<USER>\\source\\repos\\this-applications\\backend\\domain\\entities\\productmetadata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1E87F41E-C849-48E1-91DF-EA5849C41F93}|Domain\\Domain.csproj|solutionrelative:domain\\entities\\productmetadata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 3, "Children": [{"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:128:0:{75188d03-9892-4ae2-abf1-207126247ce5}"}, {"$type": "Bookmark", "Name": "ST:130:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "ObjectValuesController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Web.Host\\Controllers\\ObjectValuesController.cs", "RelativeDocumentMoniker": "Web.Host\\Controllers\\ObjectValuesController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Web.Host\\Controllers\\ObjectValuesController.cs", "RelativeToolTip": "Web.Host\\Controllers\\ObjectValuesController.cs", "ViewState": "AgIAADcAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-05T13:41:21.863Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "launchSettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Web.Host\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "Web.Host\\Properties\\launchSettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Web.Host\\Properties\\launchSettings.json", "RelativeToolTip": "Web.Host\\Properties\\launchSettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-05T13:38:43.199Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "GenerateControllers.ps1", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\GenerateControllers.ps1", "RelativeDocumentMoniker": "GenerateControllers.ps1", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\GenerateControllers.ps1", "RelativeToolTip": "GenerateControllers.ps1", "ViewState": "AgIAAE0BAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-06-05T12:37:38.486Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "GetProductMetadataByIdQuery.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Application\\ProductMetadata\\Queries\\GetProductMetadataByIdQuery.cs", "RelativeDocumentMoniker": "Application\\ProductMetadata\\Queries\\GetProductMetadataByIdQuery.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Application\\ProductMetadata\\Queries\\GetProductMetadataByIdQuery.cs", "RelativeToolTip": "Application\\ProductMetadata\\Queries\\GetProductMetadataByIdQuery.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-05T12:19:34.194Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "GetProductMetadataQuery.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Application\\ProductMetadata\\Queries\\GetProductMetadataQuery.cs", "RelativeDocumentMoniker": "Application\\ProductMetadata\\Queries\\GetProductMetadataQuery.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Application\\ProductMetadata\\Queries\\GetProductMetadataQuery.cs", "RelativeToolTip": "Application\\ProductMetadata\\Queries\\GetProductMetadataQuery.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-05T12:19:31.361Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "ProductMetadata.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Domain\\Entities\\ProductMetadata.cs", "RelativeDocumentMoniker": "Domain\\Entities\\ProductMetadata.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\this-applications\\backend\\Domain\\Entities\\ProductMetadata.cs", "RelativeToolTip": "Domain\\Entities\\ProductMetadata.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-05T12:19:05.728Z", "EditorCaption": ""}]}]}]}