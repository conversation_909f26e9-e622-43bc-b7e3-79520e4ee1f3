using Domain.Entities;
using Infrastructure.Database.Configuration;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Finbuckle.MultiTenant;
using Finbuckle.MultiTenant.EntityFrameworkCore;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for IntegrationConfiguration entity
/// </summary>
public class IntegrationConfigurationConfig : IEntityTypeConfiguration<IntegrationConfiguration>
{
    public void Configure(EntityTypeBuilder<IntegrationConfiguration> builder)
    {

        builder.ToTable("IntegrationConfiguration", "Genp");

        builder.IsMultiTenant();

        // Properties
        builder.Property(e => e.IntegrationId)
            .IsRequired();

        builder.Property(e => e.IntegrationApiId)
            .IsRequired();

        builder.Property(e => e.ObjectId)
            .IsRequired();

        builder.Property(e => e.Direction)
            .HasMaxLength(10);

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        // Indexes
        builder.HasIndex(e => e.IntegrationId)
            .HasDatabaseName("IX_IntegrationConfiguration_IntegrationId");

        builder.HasIndex(e => e.IntegrationApiId)
            .HasDatabaseName("IX_IntegrationConfiguration_IntegrationApiId");

        builder.HasIndex(e => e.ObjectId)
            .HasDatabaseName("IX_IntegrationConfiguration_ObjectId");

        builder.HasIndex(e => e.Direction)
            .HasDatabaseName("IX_IntegrationConfiguration_Direction");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_IntegrationConfiguration_IsActive")
            .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

        // Unique constraint
        builder.HasIndex(e => new { e.IntegrationId, e.IntegrationApiId, e.ObjectId })
            .IsUnique()
            .HasDatabaseName("IX_IntegrationConfiguration_IntegrationId_IntegrationApiId_ObjectId");

        // Relationships
        builder.HasOne(e => e.Integration)
            .WithMany(e => e.IntegrationConfigurations)
            .HasForeignKey(e => e.IntegrationId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.IntegrationApi)
            .WithMany(e => e.IntegrationConfigurations)
            .HasForeignKey(e => e.IntegrationApiId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(e => e.Object)
            .WithMany(e => e.IntegrationConfigurations)
            .HasForeignKey(e => e.ObjectId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
