using MediatR;
using Shared.Common.Response;

namespace Application.Objects.Commands;

/// <summary>
/// Delete Object command
/// </summary>
public class DeleteObjectCommand : IRequest<Result<bool>>
{
    /// <summary>
    /// Object ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteObjectCommand(Guid id)
    {
        Id = id;
    }
}
