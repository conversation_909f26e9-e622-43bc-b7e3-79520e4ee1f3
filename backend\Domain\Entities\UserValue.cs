using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Actual user instance data
/// </summary>
public class UserValue : AuditableEntity, IAggregateRoot
{

    /// <summary>
    /// User metadata ID
    /// </summary>
    public Guid UserMetadataId { get; set; }

    /// <summary>
    /// Reference ID for grouping related values
    /// </summary>
    public Guid? RefId { get; set; }

    /// <summary>
    /// The actual value as text
    /// </summary>
    public string? Value { get; set; }

    // Navigation properties
    /// <summary>
    /// User metadata link
    /// </summary>
    public virtual UserMetadata UserMetadata { get; set; } = null!;
}
