using Application.FeatureValues.DTOs;
using Application.FeatureValues.Queries;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;

namespace Web.Host.Controllers;

/// <summary>
/// FeatureValues controller
/// </summary>
[Route("api/[controller]")]
public class FeatureValuesController : BaseApiController
{
    /// <summary>
    /// Get all feature values with pagination
    /// </summary>
    [HttpGet]
    [TenantIdHeader]
    public async Task<ActionResult<PaginatedResult<FeatureValueDto>>> GetFeatureValuesAsync([FromQuery] GetFeatureValuesQuery query)
    {
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get feature value by ID
    /// </summary>
    [HttpGet("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<FeatureValueDto>>> GetFeatureValueByIdAsync(Guid id)
    {
        return Ok(await Mediator.Send(new GetFeatureValueByIdQuery(id)));
    }
}
