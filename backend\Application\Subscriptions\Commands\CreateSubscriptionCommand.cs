using Application.Subscriptions.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Subscriptions.Commands;

/// <summary>
/// Create Subscription command
/// </summary>
public class CreateSubscriptionCommand : IRequest<Result<SubscriptionDto>>
{
    /// <summary>
    /// Product ID this subscription is for
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Subscription type
    /// </summary>
    public string SubscriptionType { get; set; } = "standard";

    /// <summary>
    /// Subscription status
    /// </summary>
    public string Status { get; set; } = "active";

    /// <summary>
    /// Subscription start date
    /// </summary>
    public DateTime StartDate { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Subscription end date
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Whether auto-renewal is enabled
    /// </summary>
    public bool AutoRenew { get; set; } = true;

    /// <summary>
    /// Pricing tier
    /// </summary>
    public string? PricingTier { get; set; }

    /// <summary>
    /// Whether the subscription is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
