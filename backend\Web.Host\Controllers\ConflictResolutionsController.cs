using Application.ConflictResolutions.Commands;
using Application.ConflictResolutions.DTOs;
// using Application.ConflictResolutions.Queries;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;

namespace Web.Host.Controllers;

/// <summary>
/// Conflict Resolutions controller
/// </summary>
//[Route("api/[controller]")]
//public class ConflictResolutionsController : BaseApiController
//{
//    // TODO: Implement CRUD operations for ConflictResolution
//    // Temporarily commented out until Query and Command classes are implemented

   
//    /// <summary>
//    /// Get all conflict resolutions with pagination
//    /// </summary>
//    [HttpGet]
//    [TenantIdHeader]
//    public async Task<ActionResult<PaginatedResult<ViewConflictResolutionDto>>> GetConflictResolutions([FromQuery] GetConflictResolutionsQuery query)
//    {
//        return Ok(await Mediator.Send(query));
//    }

//    /// <summary>
//    /// Get conflict resolution by ID
//    /// </summary>
//    [HttpGet("{id}")]
//    [TenantIdHeader]
//    public async Task<ActionResult<Result<ViewConflictResolutionDto>>> GetConflictResolutionById(Guid id)
//    {
//        return Ok(await Mediator.Send(new GetConflictResolutionByIdQuery(id)));
//    }

//    /// <summary>
//    /// Create a new conflict resolution
//    /// </summary>
//    [HttpPost]
//    [TenantIdHeader]
//    public async Task<ActionResult<Result<ViewConflictResolutionDto>>> CreateConflictResolution(CreateConflictResolutionCommand command)
//    {
//        return Ok(await Mediator.Send(command));
//    }

//    /// <summary>
//    /// Update an existing conflict resolution
//    /// </summary>
//    [HttpPut("{id}")]
//    [TenantIdHeader]
//    public async Task<ActionResult<Result<ViewConflictResolutionDto>>> UpdateConflictResolution(Guid id, UpdateConflictResolutionCommand command)
//    {
//        command.Id = id;
//        return Ok(await Mediator.Send(command));
//    }

//    /// <summary>
//    /// Delete a conflict resolution
//    /// </summary>
//    [HttpDelete("{id}")]
//    [TenantIdHeader]
//    public async Task<ActionResult<Result<bool>>> DeleteConflictResolution(Guid id)
//    {
//        return Ok(await Mediator.Send(new DeleteConflictResolutionCommand(id)));
//    }

//    /// <summary>
//    /// Create multiple conflict resolutions in bulk
//    /// </summary>
//    [HttpPost("bulk")]
//    [TenantIdHeader]
//    public async Task<ActionResult<Result<List<ViewConflictResolutionDto>>>> CreateConflictResolutions(CreateConflictResolutionsCommand command)
//    {
//        return Ok(await Mediator.Send(command));
//    }
   
//}
