using MediatR;
using Shared.Common.Response;

namespace Application.ObjectValues.Commands;

/// <summary>
/// Delete ObjectValue command
/// </summary>
public class DeleteObjectValueCommand : IRequest<Result<bool>>
{
    /// <summary>
    /// ObjectValue ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteObjectValueCommand(Guid id)
    {
        Id = id;
    }
}
