using Application.Features.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Features.Commands.UpsertObjectWithMetadata;

/// <summary>
/// Command to upsert an object with metadata and values
/// </summary>
public class UpsertObjectWithMetadataCommand : IRequest<Result<UpsertObjectWithMetadataResponseDto>>
{
    /// <summary>
    /// Object information to create or update
    /// </summary>
    public ObjectUpsertDto Object { get; set; } = new();

    /// <summary>
    /// Create command from DTO
    /// </summary>
    public static UpsertObjectWithMetadataCommand FromDto(UpsertObjectWithMetadataRequestDto dto)
    {
        return new UpsertObjectWithMetadataCommand
        {
            Object = dto.Object
        };
    }
}
