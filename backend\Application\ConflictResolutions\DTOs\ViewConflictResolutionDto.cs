namespace Application.ConflictResolutions.DTOs;

/// <summary>
/// View Conflict Resolution DTO
/// </summary>
public class ViewConflictResolutionDto
{
    /// <summary>
    /// Conflict Resolution ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Integration ID this conflict resolution applies to
    /// </summary>
    public Guid IntegrationId { get; set; }

    /// <summary>
    /// Integration name
    /// </summary>
    public string IntegrationName { get; set; } = string.Empty;

    /// <summary>
    /// Object ID this conflict resolution applies to
    /// </summary>
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Object name
    /// </summary>
    public string ObjectName { get; set; } = string.Empty;

    /// <summary>
    /// Resolution strategy (e.g., "SourceWins", "TargetWins", "Manual", "Merge")
    /// </summary>
    public string ResolutionStrategy { get; set; } = string.Empty;

    /// <summary>
    /// Merge rules configuration stored as JSON
    /// </summary>
    public string? MergeRules { get; set; }

    /// <summary>
    /// Created at
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by
    /// </summary>
    public Guid? CreatedBy { get; set; }

    /// <summary>
    /// Modified at
    /// </summary>
    public DateTime ModifiedAt { get; set; }

    /// <summary>
    /// Modified by
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}
