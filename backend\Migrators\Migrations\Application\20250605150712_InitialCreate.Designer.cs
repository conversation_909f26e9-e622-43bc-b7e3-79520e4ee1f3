﻿// <auto-generated />
using System;
using Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Migrators.Migrations.Application
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250605150712_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Domain.Entities.ConflictResolution", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ConflictType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("FieldName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("IntegrationId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsResolved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<string>("MergeRules")
                        .HasColumnType("text");

                    b.Property<DateTime>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ObjectId")
                        .HasColumnType("uuid");

                    b.Property<string>("ResolutionStrategy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("ResolvedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ResolvedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("ResolvedValue")
                        .HasColumnType("TEXT");

                    b.Property<string>("SourceValue")
                        .HasColumnType("TEXT");

                    b.Property<string>("TargetValue")
                        .HasColumnType("TEXT");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.HasKey("Id");

                    b.HasIndex("ConflictType")
                        .HasDatabaseName("IX_ConflictResolution_ConflictType");

                    b.HasIndex("FieldName")
                        .HasDatabaseName("IX_ConflictResolution_FieldName");

                    b.HasIndex("IntegrationId")
                        .HasDatabaseName("IX_ConflictResolution_IntegrationId");

                    b.HasIndex("IsResolved")
                        .HasDatabaseName("IX_ConflictResolution_IsResolved");

                    b.HasIndex("ObjectId")
                        .HasDatabaseName("IX_ConflictResolution_ObjectId");

                    b.HasIndex("ResolutionStrategy")
                        .HasDatabaseName("IX_ConflictResolution_ResolutionStrategy");

                    b.HasIndex("ResolvedAt")
                        .HasDatabaseName("IX_ConflictResolution_ResolvedAt");

                    b.HasIndex("ResolvedBy");

                    b.ToTable("ConflictResolution", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.DataType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AllowedFileTypes")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("AllowsCustomOptions")
                        .HasColumnType("boolean");

                    b.Property<bool>("AllowsMultiple")
                        .HasColumnType("boolean");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<int?>("DecimalPlaces")
                        .HasColumnType("integer");

                    b.Property<string>("DefaultOptions")
                        .HasColumnType("TEXT");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("FileSizeErrorMessage")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FileTypeErrorMessage")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("HtmlAttributes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("InputMask")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("InputType")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRequired")
                        .HasColumnType("boolean");

                    b.Property<long?>("MaxFileSizeBytes")
                        .HasColumnType("bigint");

                    b.Property<int?>("MaxLength")
                        .HasColumnType("integer");

                    b.Property<string>("MaxLengthErrorMessage")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int?>("MaxSelections")
                        .HasColumnType("integer");

                    b.Property<decimal?>("MaxValue")
                        .HasColumnType("numeric");

                    b.Property<string>("MaxValueErrorMessage")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int?>("MinLength")
                        .HasColumnType("integer");

                    b.Property<string>("MinLengthErrorMessage")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<decimal?>("MinValue")
                        .HasColumnType("numeric");

                    b.Property<string>("MinValueErrorMessage")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("PatternErrorMessage")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Placeholder")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("RequiredErrorMessage")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<decimal?>("StepValue")
                        .HasColumnType("numeric");

                    b.Property<string>("UiComponent")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ValidationPattern")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.HasKey("Id");

                    b.HasIndex("Category")
                        .HasDatabaseName("IX_DataTypes_Category");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_DataTypes_IsActive")
                        .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasDatabaseName("IX_DataTypes_Name");

                    b.ToTable("DataTypes", "Genp");
                });

            modelBuilder.Entity("Domain.Entities.Feature", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDefault")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.HasKey("Id");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_Features_IsActive")
                        .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

                    b.HasIndex("IsDefault")
                        .HasDatabaseName("IX_Features_IsDefault");

                    b.HasIndex("ProductId")
                        .HasDatabaseName("IX_Features_ProductId");

                    b.HasIndex("ProductId", "Name")
                        .IsUnique()
                        .HasDatabaseName("IX_Features_ProductId_Name");

                    b.ToTable("Features", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.FeatureMetadata", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid>("FeatureId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsCalculate")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsUnique")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<Guid>("MetadataId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("ShouldVisibleInCreate")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShouldVisibleInEdit")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShouldVisibleInList")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShouldVisibleInView")
                        .HasColumnType("boolean");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.HasKey("Id");

                    b.HasIndex("FeatureId")
                        .HasDatabaseName("IX_FeatureMetadata_FeatureId");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_FeatureMetadata_IsActive")
                        .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

                    b.HasIndex("IsUnique")
                        .HasDatabaseName("IX_FeatureMetadata_IsUnique");

                    b.HasIndex("MetadataId")
                        .HasDatabaseName("IX_FeatureMetadata_MetadataId");

                    b.HasIndex("FeatureId", "MetadataId")
                        .IsUnique()
                        .HasDatabaseName("IX_FeatureMetadata_FeatureId_MetadataId");

                    b.ToTable("FeatureMetadata", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.FeatureValue", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid>("FeatureMetadataId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("ModifiedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("RefId")
                        .HasColumnType("uuid");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("Value")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_FeatureValues_CreatedAt");

                    b.HasIndex("FeatureMetadataId")
                        .HasDatabaseName("IX_FeatureValues_FeatureMetadataId");

                    b.HasIndex("RefId")
                        .HasDatabaseName("IX_FeatureValues_RefId");

                    b.ToTable("FeatureValues", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.FieldMapping", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ApiName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid>("IntegrationId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Notes")
                        .HasColumnType("text");

                    b.Property<Guid>("ObjectMetadataId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("RoleId")
                        .HasColumnType("uuid");

                    b.Property<string>("SourceField")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("SourceType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("TargetObjectName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("TransformationRules")
                        .HasColumnType("TEXT");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ApiName")
                        .HasDatabaseName("IX_FieldMapping_ApiName");

                    b.HasIndex("IntegrationId")
                        .HasDatabaseName("IX_FieldMapping_IntegrationId");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_FieldMapping_IsActive")
                        .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

                    b.HasIndex("ObjectMetadataId")
                        .HasDatabaseName("IX_FieldMapping_ObjectMetadataId");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("IX_FieldMapping_RoleId");

                    b.HasIndex("SourceField")
                        .HasDatabaseName("IX_FieldMapping_SourceField");

                    b.HasIndex("SourceType")
                        .HasDatabaseName("IX_FieldMapping_SourceType");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_FieldMapping_UserId");

                    b.ToTable("FieldMapping", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.Integration", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AuthConfig")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("AuthType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastSyncAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<TimeSpan?>("SyncFrequency")
                        .HasColumnType("interval");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.HasKey("Id");

                    b.HasIndex("AuthType")
                        .HasDatabaseName("IX_Integration_AuthType");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_Integration_IsActive")
                        .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

                    b.HasIndex("ProductId")
                        .HasDatabaseName("IX_Integration_ProductId");

                    b.HasIndex("ProductId", "Name")
                        .IsUnique()
                        .HasDatabaseName("IX_Integration_ProductId_Name");

                    b.ToTable("Integrations", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.IntegrationApi", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("EndpointUrl")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<string>("Schema")
                        .HasColumnType("TEXT");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.HasKey("Id");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_IntegrationApi_IsActive")
                        .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

                    b.HasIndex("ProductId")
                        .HasDatabaseName("IX_IntegrationApi_ProductId");

                    b.HasIndex("ProductId", "Name")
                        .IsUnique()
                        .HasDatabaseName("IX_IntegrationApi_ProductId_Name");

                    b.ToTable("IntegrationApi", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.IntegrationConfiguration", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Direction")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<Guid>("IntegrationApiId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("IntegrationId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ObjectId")
                        .HasColumnType("uuid");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.HasKey("Id");

                    b.HasIndex("Direction")
                        .HasDatabaseName("IX_IntegrationConfiguration_Direction");

                    b.HasIndex("IntegrationApiId")
                        .HasDatabaseName("IX_IntegrationConfiguration_IntegrationApiId");

                    b.HasIndex("IntegrationId")
                        .HasDatabaseName("IX_IntegrationConfiguration_IntegrationId");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_IntegrationConfiguration_IsActive")
                        .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

                    b.HasIndex("ObjectId")
                        .HasDatabaseName("IX_IntegrationConfiguration_ObjectId");

                    b.HasIndex("IntegrationId", "IntegrationApiId", "ObjectId")
                        .IsUnique()
                        .HasDatabaseName("IX_IntegrationConfiguration_IntegrationId_IntegrationApiId_ObjectId");

                    b.ToTable("IntegrationConfiguration", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.Metadata", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("CustomAllowedFileTypes")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("CustomErrorMessage")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool?>("CustomIsRequired")
                        .HasColumnType("boolean");

                    b.Property<long?>("CustomMaxFileSize")
                        .HasColumnType("bigint");

                    b.Property<int?>("CustomMaxLength")
                        .HasColumnType("integer");

                    b.Property<int?>("CustomMaxSelections")
                        .HasColumnType("integer");

                    b.Property<decimal?>("CustomMaxValue")
                        .HasColumnType("numeric");

                    b.Property<int?>("CustomMinLength")
                        .HasColumnType("integer");

                    b.Property<decimal?>("CustomMinValue")
                        .HasColumnType("numeric");

                    b.Property<string>("CustomOptions")
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomPlaceholder")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("CustomValidationPattern")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<Guid>("DataTypeId")
                        .HasColumnType("uuid");

                    b.Property<string>("DisplayLabel")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int?>("FieldOrder")
                        .HasColumnType("integer");

                    b.Property<string>("HelpText")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsReadonly")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsVisible")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<string>("MetadataKey")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.HasKey("Id");

                    b.HasIndex("DataTypeId")
                        .HasDatabaseName("IX_Metadata_DataTypeId");

                    b.HasIndex("IsVisible")
                        .HasDatabaseName("IX_Metadata_IsVisible")
                        .HasFilter("\"IsVisible\" = true AND \"IsDeleted\" = false");

                    b.HasIndex("MetadataKey")
                        .IsUnique()
                        .HasDatabaseName("IX_Metadata_MetadataKey");

                    b.ToTable("Metadata", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.Object", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasColumnType("TEXT");

                    b.Property<Guid>("FeatureId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid?>("ParentObjectId")
                        .HasColumnType("uuid");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.HasKey("Id");

                    b.HasIndex("FeatureId")
                        .HasDatabaseName("IX_Objects_FeatureId");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_Objects_IsActive")
                        .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

                    b.HasIndex("ParentObjectId")
                        .HasDatabaseName("IX_Objects_ParentObjectId");

                    b.HasIndex("FeatureId", "Name")
                        .IsUnique()
                        .HasDatabaseName("IX_Objects_FeatureId_Name");

                    b.ToTable("Objects", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.ObjectMetadata", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsCalculate")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsUnique")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<Guid>("MetadataId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ObjectId")
                        .HasColumnType("uuid");

                    b.Property<bool>("ShouldVisibleInCreate")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShouldVisibleInEdit")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShouldVisibleInList")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShouldVisibleInView")
                        .HasColumnType("boolean");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.HasKey("Id");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_ObjectMetadata_IsActive")
                        .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

                    b.HasIndex("IsUnique")
                        .HasDatabaseName("IX_ObjectMetadata_IsUnique");

                    b.HasIndex("MetadataId")
                        .HasDatabaseName("IX_ObjectMetadata_MetadataId");

                    b.HasIndex("ObjectId")
                        .HasDatabaseName("IX_ObjectMetadata_ObjectId");

                    b.HasIndex("ObjectId", "MetadataId")
                        .IsUnique()
                        .HasDatabaseName("IX_ObjectMetadata_ObjectId_MetadataId");

                    b.ToTable("ObjectMetadata", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.ObjectValue", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("ModifiedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ObjectMetadataId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ParentObjectValueId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("RefId")
                        .HasColumnType("uuid");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("Value")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_ObjectValues_CreatedAt");

                    b.HasIndex("ObjectMetadataId")
                        .HasDatabaseName("IX_ObjectValues_ObjectMetadataId");

                    b.HasIndex("ParentObjectValueId")
                        .HasDatabaseName("IX_ObjectValues_ParentObjectValueId");

                    b.HasIndex("RefId")
                        .HasDatabaseName("IX_ObjectValues_RefId");

                    b.ToTable("ObjectValues", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.Product", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ApiKey")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ApplicationUrl")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasColumnType("TEXT");

                    b.Property<string>("Icon")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsOnboardCompleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsRoleAssigned")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsUserImported")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("Version")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("1.0.0");

                    b.HasKey("Id");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_Products_IsActive")
                        .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

                    b.ToTable("Products", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.ProductMetadata", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsCalculate")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsUnique")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<Guid>("MetadataId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<bool>("ShouldVisibleInCreate")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShouldVisibleInEdit")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShouldVisibleInList")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShouldVisibleInView")
                        .HasColumnType("boolean");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.HasKey("Id");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_ProductMetadata_IsActive")
                        .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

                    b.HasIndex("IsUnique")
                        .HasDatabaseName("IX_ProductMetadata_IsUnique");

                    b.HasIndex("MetadataId")
                        .HasDatabaseName("IX_ProductMetadata_MetadataId");

                    b.HasIndex("ProductId")
                        .HasDatabaseName("IX_ProductMetadata_ProductId");

                    b.HasIndex("ProductId", "MetadataId")
                        .IsUnique()
                        .HasDatabaseName("IX_ProductMetadata_ProductId_MetadataId");

                    b.ToTable("ProductMetadata", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.ProductValue", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("ModifiedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ProductMetadataId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("RefId")
                        .HasColumnType("uuid");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("Value")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_ProductValues_CreatedAt");

                    b.HasIndex("ProductMetadataId")
                        .HasDatabaseName("IX_ProductValues_ProductMetadataId");

                    b.HasIndex("RefId")
                        .HasDatabaseName("IX_ProductValues_RefId");

                    b.ToTable("ProductValues", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.Role", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsSystemRole")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime>("ModifiedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Permissions")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValue("{}");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.HasKey("Id");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_Roles_IsActive")
                        .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

                    b.HasIndex("IsSystemRole")
                        .HasDatabaseName("IX_Roles_IsSystemRole");

                    b.HasIndex("ProductId")
                        .HasDatabaseName("IX_Roles_ProductId");

                    b.HasIndex("NormalizedName", "TenantId")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("Roles", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.RoleMetadata", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsCalculate")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsUnique")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<Guid>("MetadataId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid");

                    b.Property<bool>("ShouldVisibleInCreate")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShouldVisibleInEdit")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShouldVisibleInList")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShouldVisibleInView")
                        .HasColumnType("boolean");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.HasKey("Id");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_RoleMetadata_IsActive")
                        .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

                    b.HasIndex("IsUnique")
                        .HasDatabaseName("IX_RoleMetadata_IsUnique");

                    b.HasIndex("MetadataId")
                        .HasDatabaseName("IX_RoleMetadata_MetadataId");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("IX_RoleMetadata_RoleId");

                    b.HasIndex("RoleId", "MetadataId")
                        .IsUnique()
                        .HasDatabaseName("IX_RoleMetadata_RoleId_MetadataId");

                    b.ToTable("RoleMetadata", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.RoleValue", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("ModifiedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("RefId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("RoleMetadataId")
                        .HasColumnType("uuid");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("Value")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_RoleValues_CreatedAt");

                    b.HasIndex("RefId")
                        .HasDatabaseName("IX_RoleValues_RefId");

                    b.HasIndex("RoleMetadataId")
                        .HasDatabaseName("IX_RoleValues_RoleMetadataId");

                    b.ToTable("RoleValues", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.Subscription", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AutoRenew")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("PricingTier")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("StartDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("active");

                    b.Property<string>("SubscriptionType")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("standard");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.HasKey("Id");

                    b.HasIndex("EndDate")
                        .HasDatabaseName("IX_Subscriptions_EndDate");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_Subscriptions_IsActive")
                        .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

                    b.HasIndex("ProductId")
                        .HasDatabaseName("IX_Subscriptions_ProductId");

                    b.HasIndex("StartDate")
                        .HasDatabaseName("IX_Subscriptions_StartDate");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_Subscriptions_Status");

                    b.HasIndex("SubscriptionType")
                        .HasDatabaseName("IX_Subscriptions_SubscriptionType");

                    b.ToTable("Subscriptions", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.SubscriptionMetadata", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsCalculate")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsUnique")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<Guid>("MetadataId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("ShouldVisibleInCreate")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShouldVisibleInEdit")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShouldVisibleInList")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShouldVisibleInView")
                        .HasColumnType("boolean");

                    b.Property<Guid>("SubscriptionId")
                        .HasColumnType("uuid");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.HasKey("Id");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_SubscriptionMetadata_IsActive")
                        .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

                    b.HasIndex("IsUnique")
                        .HasDatabaseName("IX_SubscriptionMetadata_IsUnique");

                    b.HasIndex("MetadataId")
                        .HasDatabaseName("IX_SubscriptionMetadata_MetadataId");

                    b.HasIndex("SubscriptionId")
                        .HasDatabaseName("IX_SubscriptionMetadata_SubscriptionId");

                    b.HasIndex("SubscriptionId", "MetadataId")
                        .IsUnique()
                        .HasDatabaseName("IX_SubscriptionMetadata_SubscriptionId_MetadataId");

                    b.ToTable("SubscriptionMetadata", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.SubscriptionValue", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("ModifiedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("RefId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("SubscriptionMetadataId")
                        .HasColumnType("uuid");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("Value")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_SubscriptionValues_CreatedAt");

                    b.HasIndex("RefId")
                        .HasDatabaseName("IX_SubscriptionValues_RefId");

                    b.HasIndex("SubscriptionMetadataId")
                        .HasDatabaseName("IX_SubscriptionValues_SubscriptionMetadataId");

                    b.ToTable("SubscriptionValues", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.SyncHistory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Direction")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("TEXT");

                    b.Property<Guid>("IntegrationId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ObjectId")
                        .HasColumnType("uuid");

                    b.Property<int>("RecordsFailed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("RecordsProcessed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("RecordsSucceeded")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<DateTime>("StartedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("SyncDetails")
                        .HasColumnType("TEXT");

                    b.Property<string>("SyncType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.HasKey("Id");

                    b.HasIndex("CompletedAt")
                        .HasDatabaseName("IX_SyncHistory_CompletedAt");

                    b.HasIndex("Direction")
                        .HasDatabaseName("IX_SyncHistory_Direction");

                    b.HasIndex("IntegrationId")
                        .HasDatabaseName("IX_SyncHistory_IntegrationId");

                    b.HasIndex("ObjectId")
                        .HasDatabaseName("IX_SyncHistory_ObjectId");

                    b.HasIndex("StartedAt")
                        .HasDatabaseName("IX_SyncHistory_StartedAt");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_SyncHistory_Status");

                    b.HasIndex("SyncType")
                        .HasDatabaseName("IX_SyncHistory_SyncType");

                    b.ToTable("SyncHistory", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.TenantInfoMetadata", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsCalculate")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsUnique")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<Guid>("MetadataId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("ShouldVisibleInCreate")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShouldVisibleInEdit")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShouldVisibleInList")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShouldVisibleInView")
                        .HasColumnType("boolean");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.HasKey("Id");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_TenantInfoMetadata_IsActive")
                        .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

                    b.HasIndex("IsUnique")
                        .HasDatabaseName("IX_TenantInfoMetadata_IsUnique");

                    b.HasIndex("MetadataId")
                        .HasDatabaseName("IX_TenantInfoMetadata_MetadataId");

                    b.ToTable("TenantInfoMetadata", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.TenantInfoValue", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("ModifiedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("RefId")
                        .HasColumnType("uuid");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<Guid>("TenantInfoMetadataId")
                        .HasColumnType("uuid");

                    b.Property<string>("Value")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_TenantInfoValues_CreatedAt");

                    b.HasIndex("RefId")
                        .HasDatabaseName("IX_TenantInfoValues_RefId");

                    b.HasIndex("TenantInfoMetadataId")
                        .HasDatabaseName("IX_TenantInfoValues_TenantInfoMetadataId");

                    b.ToTable("TenantInfoValues", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("integer");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("ExternalUserId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FirstName")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastName")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("ModifiedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("RefreshToken")
                        .HasColumnType("text");

                    b.Property<DateTime?>("RefreshTokenExpiryTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("text");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("ExternalUserId")
                        .HasDatabaseName("IX_Users_ExternalUserId");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_Users_IsActive")
                        .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

                    b.HasIndex("LastLoginAt")
                        .HasDatabaseName("IX_Users_LastLoginAt");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName", "TenantId")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.ToTable("Users", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.UserMetadata", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsCalculate")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsUnique")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<Guid>("MetadataId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("ShouldVisibleInCreate")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShouldVisibleInEdit")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShouldVisibleInList")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShouldVisibleInView")
                        .HasColumnType("boolean");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_UserMetadata_IsActive")
                        .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

                    b.HasIndex("IsUnique")
                        .HasDatabaseName("IX_UserMetadata_IsUnique");

                    b.HasIndex("MetadataId")
                        .HasDatabaseName("IX_UserMetadata_MetadataId");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_UserMetadata_UserId");

                    b.HasIndex("UserId", "MetadataId")
                        .IsUnique()
                        .HasDatabaseName("IX_UserMetadata_UserId_MetadataId");

                    b.ToTable("UserMetadata", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.UserRole", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("IX_UserRoles_RoleId");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_UserRoles_UserId");

                    b.ToTable("UserRoles", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.UserValue", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("ModifiedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<Guid?>("ModifiedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("RefId")
                        .HasColumnType("uuid");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<Guid>("UserMetadataId")
                        .HasColumnType("uuid");

                    b.Property<string>("Value")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_UserValues_CreatedAt");

                    b.HasIndex("RefId")
                        .HasDatabaseName("IX_UserValues_RefId");

                    b.HasIndex("UserMetadataId")
                        .HasDatabaseName("IX_UserValues_UserMetadataId");

                    b.ToTable("UserValues", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<System.Guid>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<System.Guid>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("UserClaims", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<System.Guid>", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("text");

                    b.Property<string>("LoginProvider")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("text");

                    b.Property<string>("ProviderKey")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.HasIndex("LoginProvider", "ProviderKey", "TenantId")
                        .IsUnique();

                    b.ToTable("UserLogins", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<System.Guid>", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("UserTokens", "Genp");

                    b.HasAnnotation("Finbuckle:MultiTenant", true);
                });

            modelBuilder.Entity("Domain.Entities.ConflictResolution", b =>
                {
                    b.HasOne("Domain.Entities.Integration", "Integration")
                        .WithMany("ConflictResolutions")
                        .HasForeignKey("IntegrationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Object", "Object")
                        .WithMany("ConflictResolutions")
                        .HasForeignKey("ObjectId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.User", "ResolvedByUser")
                        .WithMany("ConflictResolutions")
                        .HasForeignKey("ResolvedBy")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Integration");

                    b.Navigation("Object");

                    b.Navigation("ResolvedByUser");
                });

            modelBuilder.Entity("Domain.Entities.Feature", b =>
                {
                    b.HasOne("Domain.Entities.Product", "Product")
                        .WithMany("Features")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Domain.Entities.FeatureMetadata", b =>
                {
                    b.HasOne("Domain.Entities.Feature", "Feature")
                        .WithMany("FeatureMetadata")
                        .HasForeignKey("FeatureId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Metadata", "Metadata")
                        .WithMany("FeatureMetadata")
                        .HasForeignKey("MetadataId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Feature");

                    b.Navigation("Metadata");
                });

            modelBuilder.Entity("Domain.Entities.FeatureValue", b =>
                {
                    b.HasOne("Domain.Entities.FeatureMetadata", "FeatureMetadata")
                        .WithMany("FeatureValues")
                        .HasForeignKey("FeatureMetadataId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FeatureMetadata");
                });

            modelBuilder.Entity("Domain.Entities.FieldMapping", b =>
                {
                    b.HasOne("Domain.Entities.Integration", "Integration")
                        .WithMany("FieldMappings")
                        .HasForeignKey("IntegrationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Entities.ObjectMetadata", "ObjectMetadata")
                        .WithMany("FieldMappings")
                        .HasForeignKey("ObjectMetadataId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Role", "Role")
                        .WithMany("FieldMappings")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Domain.Entities.User", "User")
                        .WithMany("FieldMappings")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Integration");

                    b.Navigation("ObjectMetadata");

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Domain.Entities.Integration", b =>
                {
                    b.HasOne("Domain.Entities.Product", "Product")
                        .WithMany("Integrations")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Domain.Entities.IntegrationApi", b =>
                {
                    b.HasOne("Domain.Entities.Product", "Product")
                        .WithMany("IntegrationApis")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Domain.Entities.IntegrationConfiguration", b =>
                {
                    b.HasOne("Domain.Entities.IntegrationApi", "IntegrationApi")
                        .WithMany("IntegrationConfigurations")
                        .HasForeignKey("IntegrationApiId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Integration", "Integration")
                        .WithMany("IntegrationConfigurations")
                        .HasForeignKey("IntegrationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Object", "Object")
                        .WithMany("IntegrationConfigurations")
                        .HasForeignKey("ObjectId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Integration");

                    b.Navigation("IntegrationApi");

                    b.Navigation("Object");
                });

            modelBuilder.Entity("Domain.Entities.Metadata", b =>
                {
                    b.HasOne("Domain.Entities.DataType", "DataType")
                        .WithMany("Metadata")
                        .HasForeignKey("DataTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("DataType");
                });

            modelBuilder.Entity("Domain.Entities.Object", b =>
                {
                    b.HasOne("Domain.Entities.Feature", "Feature")
                        .WithMany("Objects")
                        .HasForeignKey("FeatureId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Object", "ParentObject")
                        .WithMany("ChildObjects")
                        .HasForeignKey("ParentObjectId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Feature");

                    b.Navigation("ParentObject");
                });

            modelBuilder.Entity("Domain.Entities.ObjectMetadata", b =>
                {
                    b.HasOne("Domain.Entities.Metadata", "Metadata")
                        .WithMany("ObjectMetadata")
                        .HasForeignKey("MetadataId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Object", "Object")
                        .WithMany("ObjectMetadata")
                        .HasForeignKey("ObjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Metadata");

                    b.Navigation("Object");
                });

            modelBuilder.Entity("Domain.Entities.ObjectValue", b =>
                {
                    b.HasOne("Domain.Entities.ObjectMetadata", "ObjectMetadata")
                        .WithMany("ObjectValues")
                        .HasForeignKey("ObjectMetadataId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Entities.ObjectValue", "ParentObjectValue")
                        .WithMany("ChildObjectValues")
                        .HasForeignKey("ParentObjectValueId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("ObjectMetadata");

                    b.Navigation("ParentObjectValue");
                });

            modelBuilder.Entity("Domain.Entities.ProductMetadata", b =>
                {
                    b.HasOne("Domain.Entities.Metadata", "Metadata")
                        .WithMany("ProductMetadata")
                        .HasForeignKey("MetadataId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Product", "Product")
                        .WithMany("ProductMetadata")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Metadata");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Domain.Entities.ProductValue", b =>
                {
                    b.HasOne("Domain.Entities.ProductMetadata", "ProductMetadata")
                        .WithMany("ProductValues")
                        .HasForeignKey("ProductMetadataId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ProductMetadata");
                });

            modelBuilder.Entity("Domain.Entities.Role", b =>
                {
                    b.HasOne("Domain.Entities.Product", "Product")
                        .WithMany("Roles")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Domain.Entities.RoleMetadata", b =>
                {
                    b.HasOne("Domain.Entities.Metadata", "Metadata")
                        .WithMany("RoleMetadata")
                        .HasForeignKey("MetadataId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Role", "Role")
                        .WithMany("RoleMetadata")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Metadata");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("Domain.Entities.RoleValue", b =>
                {
                    b.HasOne("Domain.Entities.RoleMetadata", "RoleMetadata")
                        .WithMany("RoleValues")
                        .HasForeignKey("RoleMetadataId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("RoleMetadata");
                });

            modelBuilder.Entity("Domain.Entities.Subscription", b =>
                {
                    b.HasOne("Domain.Entities.Product", "Product")
                        .WithMany("Subscriptions")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Domain.Entities.SubscriptionMetadata", b =>
                {
                    b.HasOne("Domain.Entities.Metadata", "Metadata")
                        .WithMany("SubscriptionMetadata")
                        .HasForeignKey("MetadataId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Subscription", "Subscription")
                        .WithMany("SubscriptionMetadata")
                        .HasForeignKey("SubscriptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Metadata");

                    b.Navigation("Subscription");
                });

            modelBuilder.Entity("Domain.Entities.SubscriptionValue", b =>
                {
                    b.HasOne("Domain.Entities.SubscriptionMetadata", "SubscriptionMetadata")
                        .WithMany("SubscriptionValues")
                        .HasForeignKey("SubscriptionMetadataId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SubscriptionMetadata");
                });

            modelBuilder.Entity("Domain.Entities.SyncHistory", b =>
                {
                    b.HasOne("Domain.Entities.Integration", "Integration")
                        .WithMany("SyncHistories")
                        .HasForeignKey("IntegrationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Entities.Object", "Object")
                        .WithMany("SyncHistories")
                        .HasForeignKey("ObjectId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Integration");

                    b.Navigation("Object");
                });

            modelBuilder.Entity("Domain.Entities.TenantInfoMetadata", b =>
                {
                    b.HasOne("Domain.Entities.Metadata", "Metadata")
                        .WithMany("TenantInfoMetadata")
                        .HasForeignKey("MetadataId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Metadata");
                });

            modelBuilder.Entity("Domain.Entities.TenantInfoValue", b =>
                {
                    b.HasOne("Domain.Entities.TenantInfoMetadata", "TenantInfoMetadata")
                        .WithMany("TenantInfoValues")
                        .HasForeignKey("TenantInfoMetadataId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("TenantInfoMetadata");
                });

            modelBuilder.Entity("Domain.Entities.UserMetadata", b =>
                {
                    b.HasOne("Domain.Entities.Metadata", "Metadata")
                        .WithMany("UserMetadata")
                        .HasForeignKey("MetadataId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Entities.User", "User")
                        .WithMany("UserMetadata")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Metadata");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Domain.Entities.UserRole", b =>
                {
                    b.HasOne("Domain.Entities.Role", "Role")
                        .WithMany("UserRoles")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Entities.User", "User")
                        .WithMany("UserRoles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Domain.Entities.UserValue", b =>
                {
                    b.HasOne("Domain.Entities.UserMetadata", "UserMetadata")
                        .WithMany("UserValues")
                        .HasForeignKey("UserMetadataId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("UserMetadata");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<System.Guid>", b =>
                {
                    b.HasOne("Domain.Entities.Role", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<System.Guid>", b =>
                {
                    b.HasOne("Domain.Entities.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<System.Guid>", b =>
                {
                    b.HasOne("Domain.Entities.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<System.Guid>", b =>
                {
                    b.HasOne("Domain.Entities.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Domain.Entities.DataType", b =>
                {
                    b.Navigation("Metadata");
                });

            modelBuilder.Entity("Domain.Entities.Feature", b =>
                {
                    b.Navigation("FeatureMetadata");

                    b.Navigation("Objects");
                });

            modelBuilder.Entity("Domain.Entities.FeatureMetadata", b =>
                {
                    b.Navigation("FeatureValues");
                });

            modelBuilder.Entity("Domain.Entities.Integration", b =>
                {
                    b.Navigation("ConflictResolutions");

                    b.Navigation("FieldMappings");

                    b.Navigation("IntegrationConfigurations");

                    b.Navigation("SyncHistories");
                });

            modelBuilder.Entity("Domain.Entities.IntegrationApi", b =>
                {
                    b.Navigation("IntegrationConfigurations");
                });

            modelBuilder.Entity("Domain.Entities.Metadata", b =>
                {
                    b.Navigation("FeatureMetadata");

                    b.Navigation("ObjectMetadata");

                    b.Navigation("ProductMetadata");

                    b.Navigation("RoleMetadata");

                    b.Navigation("SubscriptionMetadata");

                    b.Navigation("TenantInfoMetadata");

                    b.Navigation("UserMetadata");
                });

            modelBuilder.Entity("Domain.Entities.Object", b =>
                {
                    b.Navigation("ChildObjects");

                    b.Navigation("ConflictResolutions");

                    b.Navigation("IntegrationConfigurations");

                    b.Navigation("ObjectMetadata");

                    b.Navigation("SyncHistories");
                });

            modelBuilder.Entity("Domain.Entities.ObjectMetadata", b =>
                {
                    b.Navigation("FieldMappings");

                    b.Navigation("ObjectValues");
                });

            modelBuilder.Entity("Domain.Entities.ObjectValue", b =>
                {
                    b.Navigation("ChildObjectValues");
                });

            modelBuilder.Entity("Domain.Entities.Product", b =>
                {
                    b.Navigation("Features");

                    b.Navigation("IntegrationApis");

                    b.Navigation("Integrations");

                    b.Navigation("ProductMetadata");

                    b.Navigation("Roles");

                    b.Navigation("Subscriptions");
                });

            modelBuilder.Entity("Domain.Entities.ProductMetadata", b =>
                {
                    b.Navigation("ProductValues");
                });

            modelBuilder.Entity("Domain.Entities.Role", b =>
                {
                    b.Navigation("FieldMappings");

                    b.Navigation("RoleMetadata");

                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("Domain.Entities.RoleMetadata", b =>
                {
                    b.Navigation("RoleValues");
                });

            modelBuilder.Entity("Domain.Entities.Subscription", b =>
                {
                    b.Navigation("SubscriptionMetadata");
                });

            modelBuilder.Entity("Domain.Entities.SubscriptionMetadata", b =>
                {
                    b.Navigation("SubscriptionValues");
                });

            modelBuilder.Entity("Domain.Entities.TenantInfoMetadata", b =>
                {
                    b.Navigation("TenantInfoValues");
                });

            modelBuilder.Entity("Domain.Entities.User", b =>
                {
                    b.Navigation("ConflictResolutions");

                    b.Navigation("FieldMappings");

                    b.Navigation("UserMetadata");

                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("Domain.Entities.UserMetadata", b =>
                {
                    b.Navigation("UserValues");
                });
#pragma warning restore 612, 618
        }
    }
}
