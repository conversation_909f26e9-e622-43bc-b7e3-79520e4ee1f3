using Application.ObjectValues.Commands;
using Application.ObjectValues.DTOs;
using Application.ObjectValues.Queries.GetObjectValue;
using Application.ObjectValues.Queries.GetObjectValuesByRefId;
using Application.ObjectValues.Queries.GetRefIdsByObjectId;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;
using Swashbuckle.AspNetCore.Annotations;

namespace Web.Host.Controllers;

/// <summary>
/// ObjectValues controller
/// </summary>
[Route("api/[controller]")]
public class ObjectValuesController : BaseApiController
{
    /// <summary>
    /// Get object values by feature with full metadata information
    /// </summary>
    [HttpGet("by-feature/{featureId}")]
    [TenantIdHeader]
    [SwaggerOperation(OperationId = "GetObjectValuesByFeature")]
    public async Task<ActionResult<Result<List<ObjectValueResponseDto>>>> GetObjectValuesByFeature(
        Guid featureId,
        [FromQuery] Guid? objectId = null,
        [FromQuery] bool includeChildValues = true,
        [FromQuery] bool onlyVisibleFields = false,
        [FromQuery] string? orderBy = null)
    {
        var query = new GetObjectValuesByFeatureQuery(featureId)
        {
            ObjectId = objectId,
            IncludeChildValues = includeChildValues,
            OnlyVisibleFields = onlyVisibleFields,
            OrderBy = orderBy
        };
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get object values with advanced filtering and pagination
    /// </summary>
    [HttpGet]
    [TenantIdHeader]
    [SwaggerOperation(OperationId = "GetObjectValuesWithFilters")]
    public async Task<ActionResult<PaginatedResult<ObjectValueResponseDto>>> GetObjectValuesWithFilters([FromQuery] GetObjectValuesWithFiltersQuery query)
    {
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get object value by ID with full metadata information
    /// </summary>
    [HttpGet("{id}")]
    [TenantIdHeader]
    [SwaggerOperation(OperationId = "GetObjectValueById")]
    public async Task<ActionResult<Result<ObjectValueResponseDto>>> GetObjectValueById(
        Guid id,
        [FromQuery] bool includeChildValues = true)
    {
        var query = new GetObjectValueByIdQuery(id)
        {
            IncludeChildValues = includeChildValues
        };
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get object values by reference ID
    /// </summary>
    [HttpGet("ref/{refId}")]
    [TenantIdHeader]
    [SwaggerOperation(OperationId = "GetObjectValuesByRefId")]
    public async Task<ActionResult<Result<ObjectValuesResponseDto>>> GetObjectValuesByRefIdAsync(
        Guid refId,
        [FromQuery] bool includeInactive = false,
        [FromQuery] bool onlyVisible = true)
    {
        var query = new GetObjectValuesByRefIdQuery(refId, includeInactive, onlyVisible);
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get reference IDs by object ID
    /// </summary>
    [HttpGet("object/{objectId}/refs")]
    [TenantIdHeader]
    [SwaggerOperation(OperationId = "GetRefIdsByObjectId")]
    public async Task<ActionResult<Result<PaginatedResult<ObjectValuesRefIdSummaryDto>>>> GetRefIdsByObjectIdAsync(
        Guid objectId,
        [FromQuery] string? searchTerm = null,
        [FromQuery] bool onlyActive = true,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 50)
    {
        var query = new GetRefIdsByObjectIdQuery(
            objectId,
            searchTerm,
            onlyActive,
            pageNumber,
            pageSize);
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Create new object value
    /// </summary>
    [HttpPost]
    [TenantIdHeader]
    [SwaggerOperation(OperationId = "CreateObjectValue")]
    public async Task<ActionResult<Result<ObjectValueDto>>> CreateObjectValue(CreateObjectValueCommand command)
    {
        return Ok(await Mediator.Send(command));
    }
}
