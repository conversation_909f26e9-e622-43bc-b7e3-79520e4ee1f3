using Application.ObjectValues.Commands;
using Application.ObjectValues.DTOs;
using Application.ObjectValues.Queries.GetObjectValue;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;

namespace Web.Host.Controllers;

/// <summary>
/// ObjectValues controller
/// </summary>
[Route("api/[controller]")]
public class ObjectValuesController : BaseApiController
{
    /// <summary>
    /// Get object values by feature with full metadata information
    /// </summary>
    [HttpGet("by-feature/{featureId}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<List<ObjectValueResponseDto>>>> GetObjectValuesByFeature(
        Guid featureId,
        [FromQuery] Guid? objectId = null,
        [FromQuery] bool includeChildValues = true,
        [FromQuery] bool onlyVisibleFields = false,
        [FromQuery] string? orderBy = null)
    {
        var query = new GetObjectValuesByFeatureQuery(featureId)
        {
            ObjectId = objectId,
            IncludeChildValues = includeChildValues,
            OnlyVisibleFields = onlyVisibleFields,
            OrderBy = orderBy
        };
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get object values with advanced filtering and pagination
    /// </summary>
    [HttpGet]
    [TenantIdHeader]
    public async Task<ActionResult<PaginatedResult<ObjectValueResponseDto>>> GetObjectValuesWithFilters([FromQuery] GetObjectValuesWithFiltersQuery query)
    {
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get object value by ID with full metadata information
    /// </summary>
    [HttpGet("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ObjectValueResponseDto>>> GetObjectValueById(
        Guid id,
        [FromQuery] bool includeChildValues = true)
    {
        var query = new GetObjectValueByIdQuery(id)
        {
            IncludeChildValues = includeChildValues
        };
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Create new object value
    /// </summary>
    [HttpPost]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ObjectValueDto>>> CreateObjectValue(CreateObjectValueCommand command)
    {
        return Ok(await Mediator.Send(command));
    }
}
