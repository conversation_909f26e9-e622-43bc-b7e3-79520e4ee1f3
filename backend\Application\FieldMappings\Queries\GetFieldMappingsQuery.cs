using Application.FieldMappings.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.FieldMappings.Queries;

/// <summary>
/// Get field mappings query
/// </summary>
public class GetFieldMappingsQuery : IRequest<PaginatedResult<ViewFieldMappingDto>>
{
    /// <summary>
    /// Page number
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Search term
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Filter by API name
    /// </summary>
    public string? ApiName { get; set; }

    /// <summary>
    /// Filter by source type
    /// </summary>
    public string? SourceType { get; set; }

    /// <summary>
    /// Filter by object metadata ID
    /// </summary>
    public Guid? ObjectMetadataId { get; set; }

    /// <summary>
    /// Filter by user ID
    /// </summary>
    public Guid? UserId { get; set; }

    /// <summary>
    /// Filter by role ID
    /// </summary>
    public Guid? RoleId { get; set; }

    /// <summary>
    /// Filter by target object name
    /// </summary>
    public string? TargetObjectName { get; set; }
}
