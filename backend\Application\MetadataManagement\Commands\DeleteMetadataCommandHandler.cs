using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.MetadataManagement.Commands;

/// <summary>
/// Delete Metadata command handler
/// </summary>
public class DeleteMetadataCommandHandler : IRequestHandler<DeleteMetadataCommand, Result<bool>>
{
    private readonly IRepository<Domain.Entities.Metadata> _repository;

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteMetadataCommandHandler(IRepository<Domain.Entities.Metadata> repository)
    {
        _repository = repository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<bool>> Handle(DeleteMetadataCommand request, CancellationToken cancellationToken)
    {
        var metadata = await _repository.GetByIdAsync(request.Id, cancellationToken);
        if (metadata == null)
        {
            return Result<bool>.Failure($"Metadata with ID '{request.Id}' not found.");
        }

        await _repository.DeleteAsync(metadata, cancellationToken);
        return Result<bool>.Success(true);
    }
}
