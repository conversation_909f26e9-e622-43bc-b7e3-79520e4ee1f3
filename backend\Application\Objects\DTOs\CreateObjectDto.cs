using System.ComponentModel.DataAnnotations;

namespace Application.Objects.DTOs;

/// <summary>
/// Create Object DTO
/// </summary>
public class CreateObjectDto
{
    /// <summary>
    /// Feature ID this object belongs to
    /// </summary>
    [Required]
    public Guid FeatureId { get; set; }

    /// <summary>
    /// Parent object ID for hierarchical structure
    /// </summary>
    public Guid? ParentObjectId { get; set; }

    /// <summary>
    /// Object name
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Object description
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Whether the object is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
