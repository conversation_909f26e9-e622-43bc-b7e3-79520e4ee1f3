using Ardalis.Specification;
using Domain.Entities;

namespace Application.MetadataManagement.Specifications;

/// <summary>
/// Specification to get Metadata by key
/// </summary>
public class MetadataByKeySpec : Specification<Domain.Entities.Metadata>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public MetadataByKeySpec(string metadataKey)
    {
        Query.Where(m => m.MetadataKey == metadataKey && !m.IsDeleted);
    }
}
