using Domain.Entities;
using Infrastructure.Database.Configuration;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Finbuckle.MultiTenant;
using Finbuckle.MultiTenant.EntityFrameworkCore;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for Integration entity
/// </summary>
public class IntegrationConfig : IEntityTypeConfiguration<Integration>
{
    public void Configure(EntityTypeBuilder<Integration> builder)
    {
        builder.ToTable("Integrations", "Genp");

        builder.IsMultiTenant();

        // Properties
        builder.Property(e => e.ProductId)
            .IsRequired();

        builder.Property(e => e.Name)
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(e => e.AuthType)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.AuthConfig)
            .HasColumnType("TEXT")
            .IsRequired();

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        // Indexes
        builder.HasIndex(e => e.ProductId)
            .HasDatabaseName("IX_Integration_ProductId");

        builder.HasIndex(e => e.AuthType)
            .HasDatabaseName("IX_Integration_AuthType");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_Integration_IsActive")
            .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

        // Unique constraint
        builder.HasIndex(e => new { e.ProductId, e.Name })
            .IsUnique()
            .HasDatabaseName("IX_Integration_ProductId_Name");

        // Relationships
        builder.HasOne(e => e.Product)
            .WithMany(e => e.Integrations)
            .HasForeignKey(e => e.ProductId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.IntegrationConfigurations)
            .WithOne(e => e.Integration)
            .HasForeignKey(e => e.IntegrationId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.FieldMappings)
            .WithOne(e => e.Integration)
            .HasForeignKey(e => e.IntegrationId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.ConflictResolutions)
            .WithOne(e => e.Integration)
            .HasForeignKey(e => e.IntegrationId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.SyncHistories)
            .WithOne(e => e.Integration)
            .HasForeignKey(e => e.IntegrationId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
