using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for UserRole entity
/// </summary>
public class UserRoleConfig : IEntityTypeConfiguration<UserRole>
{
    public void Configure(EntityTypeBuilder<UserRole> builder)
    {
        builder.ToTable("UserRoles", "Genp");

        // Composite primary key
        builder.HasKey(e => new { e.UserId, e.RoleId });

        builder.IsMultiTenant();

        // Properties
        builder.Property(e => e.CreatedAt)
            .HasDefaultValueSql("NOW()")
            .IsRequired();

        builder.Property(e => e.CreatedBy);

        builder.Property(e => e.ModifiedAt)
            .HasDefaultValueSql("NOW()")
            .IsRequired();

        builder.Property(e => e.ModifiedBy);

        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false);

        // Indexes
        builder.HasIndex(e => e.UserId)
            .HasDatabaseName("IX_UserRoles_UserId");

        builder.HasIndex(e => e.RoleId)
            .HasDatabaseName("IX_UserRoles_RoleId");

        // Query filter for soft delete
        builder.HasQueryFilter(e => !e.IsDeleted);

        // Multi-tenancy
        builder.IsMultiTenant();

        // Relationships
        builder.HasOne(e => e.User)
            .WithMany(e => e.UserRoles)
            .HasForeignKey(e => e.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.Role)
            .WithMany(e => e.UserRoles)
            .HasForeignKey(e => e.RoleId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
