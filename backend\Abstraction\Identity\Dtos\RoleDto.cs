namespace Abstraction.Identity.Dtos;

/// <summary>
/// Data transfer object for role information
/// </summary>
public class RoleDto
{
    /// <summary>
    /// Role ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Role name
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// Normalized role name
    /// </summary>
    public string? NormalizedName { get; set; }

    /// <summary>
    /// Concurrency stamp
    /// </summary>
    public string? ConcurrencyStamp { get; set; }

    /// <summary>
    /// Product ID this role belongs to
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Role description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Whether this is a system role
    /// </summary>
    public bool IsSystemRole { get; set; } = false;

    /// <summary>
    /// Permissions as JSON
    /// </summary>
    public string Permissions { get; set; } = "{}";

    /// <summary>
    /// Whether the role is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// When the role was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Who created the role
    /// </summary>
    public Guid? CreatedBy { get; set; }

    /// <summary>
    /// When the role was last modified
    /// </summary>
    public DateTime ModifiedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Who last modified the role
    /// </summary>
    public Guid? ModifiedBy { get; set; }

    /// <summary>
    /// Whether the role is soft deleted
    /// </summary>
    public bool IsDeleted { get; set; } = false;
}
