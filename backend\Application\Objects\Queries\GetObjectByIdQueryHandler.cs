using Application.Objects.DTOs;
using Application.Objects.Specifications;
using Abstraction.Database.Repositories;
using MediatR;
using Shared.Common.Response;
using ObjectEntity = Domain.Entities.Object;

namespace Application.Objects.Queries;

/// <summary>
/// Get Object by ID query handler
/// </summary>
public class GetObjectByIdQueryHandler : IRequestHandler<GetObjectByIdQuery, Result<ObjectDto>>
{
    private readonly IReadRepository<ObjectEntity> _objectRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetObjectByIdQueryHandler(IReadRepository<ObjectEntity> objectRepository)
    {
        _objectRepository = objectRepository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<ObjectDto>> Handle(GetObjectByIdQuery request, CancellationToken cancellationToken)
    {
        // Get object using specification (tenant isolation handled by Finbuckle.MultiTenant)
        var objectSpec = new ObjectByIdWithMetadataSpec(request.Id);
        var obj = await _objectRepository.GetBySpecAsync(objectSpec, cancellationToken);

        if (obj == null)
        {
            return Result<ObjectDto>.Failure("Object not found.");
        }

        var objectDto = new ObjectDto
        {
            Id = obj.Id,
            FeatureId = obj.FeatureId,
            FeatureName = obj.Feature?.Name,
            ParentObjectId = obj.ParentObjectId,
            ParentObjectName = obj.ParentObject?.Name,
            Name = obj.Name,
            Description = obj.Description,
            IsActive = obj.IsActive,
            ChildObjectsCount = obj.ChildObjects?.Count(co => co.IsActive && !co.IsDeleted) ?? 0,
            MetadataCount = obj.ObjectMetadata?.Count(om => om.IsActive && !om.IsDeleted) ?? 0
        };

        return Result<ObjectDto>.Success(objectDto);
    }
}
