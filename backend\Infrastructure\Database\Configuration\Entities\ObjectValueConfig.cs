using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for ObjectValue entity
/// </summary>
public class ObjectValueConfig : IEntityTypeConfiguration<ObjectValue>
{
    public void Configure(EntityTypeBuilder<ObjectValue> builder)
    {
        builder.ToTable("ObjectValues", "Genp");

        // Primary key
        builder.HasKey(e => e.Id);

        // Properties
        builder.Property(e => e.ObjectMetadataId)
            .IsRequired();

        builder.Property(e => e.RefId);

        builder.Property(e => e.ParentObjectValueId);

        builder.Property(e => e.Value)
            .HasColumnType("TEXT");

        builder.Property(e => e.CreatedAt)
            .HasDefaultValueSql("NOW()")
            .IsRequired();

        builder.Property(e => e.CreatedBy);

        builder.Property(e => e.ModifiedAt)
            .HasDefaultValueSql("NOW()")
            .IsRequired();

        builder.Property(e => e.ModifiedBy);

        // Indexes
        builder.HasIndex(e => e.ObjectMetadataId)
            .HasDatabaseName("IX_ObjectValues_ObjectMetadataId");

        builder.HasIndex(e => e.RefId)
            .HasDatabaseName("IX_ObjectValues_RefId");

        builder.HasIndex(e => e.ParentObjectValueId)
            .HasDatabaseName("IX_ObjectValues_ParentObjectValueId");

        builder.HasIndex(e => e.CreatedAt)
            .HasDatabaseName("IX_ObjectValues_CreatedAt");

        // Multi-tenancy
        builder.IsMultiTenant();

        // Relationships
        builder.HasOne(e => e.ObjectMetadata)
            .WithMany(e => e.ObjectValues)
            .HasForeignKey(e => e.ObjectMetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.ParentObjectValue)
            .WithMany(e => e.ChildObjectValues)
            .HasForeignKey(e => e.ParentObjectValueId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(e => e.ChildObjectValues)
            .WithOne(e => e.ParentObjectValue)
            .HasForeignKey(e => e.ParentObjectValueId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
