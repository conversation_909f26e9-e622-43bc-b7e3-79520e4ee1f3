namespace Application.ComprehensiveEntityData.DTOs;

/// <summary>
/// Comprehensive entity data response DTO
/// </summary>
public class ComprehensiveEntityDataResponseDto
{
    /// <summary>
    /// List of products with their complete hierarchical data
    /// </summary>
    public List<ProductComprehensiveDto> Products { get; set; } = new();
}

/// <summary>
/// Product comprehensive DTO with all related data
/// </summary>
public class ProductComprehensiveDto
{
    /// <summary>
    /// Product information
    /// </summary>
    public ProductInfoDto ProductInfo { get; set; } = new();
    
    /// <summary>
    /// Product metadata and values
    /// </summary>
    public List<MetadataWithValuesDto> ProductMetadata { get; set; } = new();
    
    /// <summary>
    /// Features with their complete data
    /// </summary>
    public List<FeatureComprehensiveDto> Features { get; set; } = new();
}

/// <summary>
/// Feature comprehensive DTO with all related data
/// </summary>
public class FeatureComprehensiveDto
{
    /// <summary>
    /// Feature information
    /// </summary>
    public FeatureInfoDto FeatureInfo { get; set; } = new();
    
    /// <summary>
    /// Feature metadata and values
    /// </summary>
    public List<MetadataWithValuesDto> FeatureMetadata { get; set; } = new();
    
    /// <summary>
    /// Objects with their complete data
    /// </summary>
    public List<ObjectComprehensiveDto> Objects { get; set; } = new();
}

/// <summary>
/// Object comprehensive DTO with all related data
/// </summary>
public class ObjectComprehensiveDto
{
    /// <summary>
    /// Object information
    /// </summary>
    public ObjectInfoDto ObjectInfo { get; set; } = new();
    
    /// <summary>
    /// Parent object information (if exists)
    /// </summary>
    public ParentObjectInfoDto? ParentObject { get; set; }
    
    /// <summary>
    /// Object metadata and values
    /// </summary>
    public List<MetadataWithValuesDto> ObjectMetadata { get; set; } = new();
    
    /// <summary>
    /// Child objects (for hierarchical structure)
    /// </summary>
    public List<ObjectComprehensiveDto> ChildObjects { get; set; } = new();
}

/// <summary>
/// Product information DTO
/// </summary>
public class ProductInfoDto
{
    public Guid Id { get; set; }
    public string? TenantId { get; set; }
    public string? Name { get; set; }
    public string? Description { get; set; }
    public string? Version { get; set; }
    public bool IsActive { get; set; }
}

/// <summary>
/// Feature information DTO
/// </summary>
public class FeatureInfoDto
{
    public Guid Id { get; set; }
    public string? Name { get; set; }
    public string? Description { get; set; }
    public bool IsActive { get; set; }
}

/// <summary>
/// Object information DTO
/// </summary>
public class ObjectInfoDto
{
    public Guid Id { get; set; }
    public Guid? ParentObjectId { get; set; }
    public string? Name { get; set; }
    public string? Description { get; set; }
    public bool IsActive { get; set; }
}

/// <summary>
/// Parent object information DTO
/// </summary>
public class ParentObjectInfoDto
{
    public Guid Id { get; set; }
    public string? Name { get; set; }
    public string? Description { get; set; }
}

/// <summary>
/// Metadata with values DTO
/// </summary>
public class MetadataWithValuesDto
{
    /// <summary>
    /// Metadata information (includes nested dataType and metadataLink)
    /// </summary>
    public MetadataInfoDto Metadata { get; set; } = new();

    /// <summary>
    /// Values for this metadata
    /// </summary>
    public List<ValueInfoDto> Values { get; set; } = new();
}

/// <summary>
/// Metadata information DTO
/// </summary>
public class MetadataInfoDto
{
    public Guid Id { get; set; }
    public string? MetadataKey { get; set; }
    public string? DisplayLabel { get; set; }
    public string? HelpText { get; set; }
    public int? FieldOrder { get; set; }
    public bool? IsVisible { get; set; }
    public bool? IsReadonly { get; set; }
    public string? CustomPlaceholder { get; set; }
    public string? CustomOptions { get; set; }
    public bool? CustomIsRequired { get; set; }

    /// <summary>
    /// Data type information (nested)
    /// </summary>
    public DataTypeInfoDto? DataType { get; set; }

    /// <summary>
    /// Metadata link information (nested)
    /// </summary>
    public MetadataLinkInfoDto? MetadataLink { get; set; }
}

/// <summary>
/// Data type information DTO
/// </summary>
public class DataTypeInfoDto
{
    public Guid Id { get; set; }
    public string? Name { get; set; }
    public string? DisplayName { get; set; }
    public string? Category { get; set; }
    public string? UiComponent { get; set; }
    public string? InputType { get; set; }
    public string? InputMask { get; set; }
    public string? Placeholder { get; set; }
    public string? DefaultOptions { get; set; }
    public string? ValidationPattern { get; set; }
    public int? MinLength { get; set; }
    public int? MaxLength { get; set; }
    public decimal? MinValue { get; set; }
    public decimal? MaxValue { get; set; }
    public int? DecimalPlaces { get; set; }
    public decimal? StepValue { get; set; }
    public bool? IsRequired { get; set; }
    public bool? AllowsMultiple { get; set; }
    public bool? AllowsCustomOptions { get; set; }
    public string? HtmlAttributes { get; set; }
}

/// <summary>
/// Metadata link information DTO
/// </summary>
public class MetadataLinkInfoDto
{
    public Guid Id { get; set; }
    public bool IsUnique { get; set; }
    public bool IsActive { get; set; }
}

/// <summary>
/// Value information DTO
/// </summary>
public class ValueInfoDto
{
    public Guid Id { get; set; }
    public Guid? RefId { get; set; }
    public string? Value { get; set; }
}
