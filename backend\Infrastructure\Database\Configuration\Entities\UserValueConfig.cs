using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for UserValue entity
/// </summary>
public class UserValueConfig : IEntityTypeConfiguration<UserValue>
{
    public void Configure(EntityTypeBuilder<UserValue> builder)
    {
        builder.ToTable("UserValues", "Genp");

        // Primary key
        builder.HasKey(e => e.Id);

        // Properties
        builder.Property(e => e.UserMetadataId)
            .IsRequired();

        builder.Property(e => e.RefId);

        builder.Property(e => e.Value)
            .HasColumnType("TEXT");

        builder.Property(e => e.CreatedAt)
            .HasDefaultValueSql("NOW()")
            .IsRequired();

        builder.Property(e => e.CreatedBy);

        builder.Property(e => e.ModifiedAt)
            .HasDefaultValueSql("NOW()")
            .IsRequired();

        builder.Property(e => e.ModifiedBy);

        // Indexes
        builder.HasIndex(e => e.UserMetadataId)
            .HasDatabaseName("IX_UserValues_UserMetadataId");

        builder.HasIndex(e => e.RefId)
            .HasDatabaseName("IX_UserValues_RefId");

        builder.HasIndex(e => e.CreatedAt)
            .HasDatabaseName("IX_UserValues_CreatedAt");

        // Multi-tenancy
        builder.IsMultiTenant();

        // Relationships
        builder.HasOne(e => e.UserMetadata)
            .WithMany(e => e.UserValues)
            .HasForeignKey(e => e.UserMetadataId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
