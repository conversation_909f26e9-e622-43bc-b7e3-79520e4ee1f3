using Application.Products.DTOs;
using Application.Products.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.Products.Queries;

/// <summary>
/// Get products query handler
/// </summary>
public class GetProductsQueryHandler : IRequestHandler<GetProductsQuery, PaginatedResult<ProductDto>>
{
    private readonly IReadRepository<Product> _productRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetProductsQueryHandler(IReadRepository<Product> productRepository)
    {
        _productRepository = productRepository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<PaginatedResult<ProductDto>> Handle(GetProductsQuery request, CancellationToken cancellationToken)
    {
        // Calculate pagination
        var skip = (request.PageNumber - 1) * request.PageSize;

        // Create specifications for data and count
        var dataSpec = new ProductsWithFiltersSpec(
            request.SearchTerm,
            request.IsActive,
            skip,
            request.PageSize);

        var countSpec = new ProductsCountSpec(
            request.SearchTerm,
            request.IsActive);

        // Get data and count using specifications (tenant isolation handled by Finbuckle.MultiTenant)
        var items = await _productRepository.ListAsync(dataSpec, cancellationToken);
        var totalCount = await _productRepository.CountAsync(countSpec, cancellationToken);

        var dtos = items.Adapt<List<ProductDto>>();

        return new PaginatedResult<ProductDto>(dtos, request.PageNumber, request.PageSize, totalCount);
    }
}
