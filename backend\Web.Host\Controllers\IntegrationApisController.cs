using Application.IntegrationApis.Commands;
using Application.IntegrationApis.DTOs;
using Application.IntegrationApis.Queries;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;

namespace Web.Host.Controllers;

/// <summary>
/// Integration APIs controller
/// </summary>
[Route("api/[controller]")]
[AllowAnonymous]
public class IntegrationApisController : BaseApiController
{
    /// <summary>
    /// Get all integration APIs with pagination
    /// </summary>
    [HttpGet]
    [TenantIdHeader]
    public async Task<ActionResult<PaginatedResult<ViewIntegrationApiDto>>> GetIntegrationApis([FromQuery] GetIntegrationApisQuery query)
    {
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get integration API by ID
    /// </summary>
    [HttpGet("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ViewIntegrationApiDto>>> GetIntegrationApiById(Guid id)
    {
        return Ok(await Mediator.Send(new GetIntegrationApiByIdQuery(id)));
    }

    /// <summary>
    /// Create a new integration API
    /// </summary>
    [HttpPost]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ViewIntegrationApiDto>>> CreateIntegrationApi(CreateIntegrationApiCommand command)
    {
        return Ok(await Mediator.Send(command));
    }

    /// <summary>
    /// Update an existing integration API
    /// </summary>
    [HttpPut("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ViewIntegrationApiDto>>> UpdateIntegrationApi(Guid id, UpdateIntegrationApiCommand command)
    {
        command.Id = id;
        return Ok(await Mediator.Send(command));
    }

    /// <summary>
    /// Delete an integration API
    /// </summary>
    [HttpDelete("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<bool>>> DeleteIntegrationApi(Guid id)
    {
        return Ok(await Mediator.Send(new DeleteIntegrationApiCommand(id)));
    }

    /// <summary>
    /// Create multiple integration APIs in bulk
    /// </summary>
    [HttpPost("bulk")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<List<ViewIntegrationApiDto>>>> CreateIntegrationApis(CreateIntegrationApisCommand command)
    {
        return Ok(await Mediator.Send(command));
    }
}
