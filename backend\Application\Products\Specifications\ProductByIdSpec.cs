using Ardalis.Specification;
using Domain.Entities;

namespace Application.Products.Specifications;

/// <summary>
/// Specification for getting a product by ID
/// </summary>
public class ProductByIdSpec : Specification<Product>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ProductByIdSpec(Guid id)
    {
        Query.Where(p => p.Id == id);
    }
}

/// <summary>
/// Specification for getting a product by blueprint product ID
/// </summary>
public class ProductByBlueprintIdSpec : Specification<Product>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ProductByBlueprintIdSpec(Guid blueprintProductId)
    {
        Query.Where(p => p.Name == blueprintProductId.ToString());
    }
}

/// <summary>
/// Specification for getting products with filters
/// </summary>
public class ProductsWithFiltersSpec : Specification<Product>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ProductsWithFiltersSpec(string? searchTerm = null, bool? isActive = null, int skip = 0, int take = 10)
    {
        Query.Where(p => true); // Base query

        if (!string.IsNullOrEmpty(searchTerm))
        {
            Query.Where(p => p.Name.Contains(searchTerm) ||
                           (p.Description != null && p.Description.Contains(searchTerm)));
        }

        if (isActive.HasValue)
        {
            Query.Where(p => p.IsActive == isActive.Value);
        }

        Query.OrderBy(p => p.Name)
             .Skip(skip)
             .Take(take);
    }
}

/// <summary>
/// Specification for counting products with filters
/// </summary>
public class ProductsCountSpec : Specification<Product>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ProductsCountSpec(string? searchTerm = null, bool? isActive = null)
    {
        Query.Where(p => true); // Base query

        if (!string.IsNullOrEmpty(searchTerm))
        {
            Query.Where(p => p.Name.Contains(searchTerm) ||
                           (p.Description != null && p.Description.Contains(searchTerm)));
        }

        if (isActive.HasValue)
        {
            Query.Where(p => p.IsActive == isActive.Value);
        }
    }
}
