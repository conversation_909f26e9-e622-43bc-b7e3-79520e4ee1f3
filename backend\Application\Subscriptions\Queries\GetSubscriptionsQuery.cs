using Application.Subscriptions.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Subscriptions.Queries;

/// <summary>
/// Get Subscriptions query
/// </summary>
public class GetSubscriptionsQuery : IRequest<PaginatedResult<SubscriptionDto>>
{
    /// <summary>
    /// Page number
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Search term
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Product ID filter
    /// </summary>
    public Guid? ProductId { get; set; }

    /// <summary>
    /// Status filter
    /// </summary>
    public string? Status { get; set; }

    /// <summary>
    /// Is active filter
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Order by
    /// </summary>
    public string? OrderBy { get; set; }
}
