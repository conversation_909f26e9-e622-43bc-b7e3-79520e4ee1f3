using System.ComponentModel.DataAnnotations;

namespace Application.Subscriptions.DTOs;

/// <summary>
/// Create Subscription DTO
/// </summary>
public class CreateSubscriptionDto
{
    /// <summary>
    /// Product ID this subscription is for
    /// </summary>
    [Required]
    public Guid ProductId { get; set; }

    /// <summary>
    /// Subscription type
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string SubscriptionType { get; set; } = "standard";

    /// <summary>
    /// Subscription status
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string Status { get; set; } = "active";

    /// <summary>
    /// Subscription start date
    /// </summary>
    public DateTime StartDate { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Subscription end date
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Whether auto-renewal is enabled
    /// </summary>
    public bool AutoRenew { get; set; } = true;

    /// <summary>
    /// Pricing tier
    /// </summary>
    [MaxLength(100)]
    public string? PricingTier { get; set; }

    /// <summary>
    /// Whether the subscription is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
