namespace Application.SyncHistories.DTOs;

/// <summary>
/// View Sync History DTO
/// </summary>
public class ViewSyncHistoryDto
{
    /// <summary>
    /// Sync History ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Integration ID this sync history belongs to
    /// </summary>
    public Guid IntegrationId { get; set; }

    /// <summary>
    /// Integration name
    /// </summary>
    public string IntegrationName { get; set; } = string.Empty;

    /// <summary>
    /// Object ID this sync operation was performed on
    /// </summary>
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Object name
    /// </summary>
    public string ObjectName { get; set; } = string.Empty;

    /// <summary>
    /// Number of records processed in this sync operation
    /// </summary>
    public int RecordCount { get; set; }

    /// <summary>
    /// Sync operation status (e.g., "Success", "Failed", "Partial", "InProgress")
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// When the sync operation started
    /// </summary>
    public DateTime StartedAt { get; set; }

    /// <summary>
    /// When the sync operation completed (null if still in progress)
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// Error log details if the sync operation failed
    /// </summary>
    public string? ErrorLog { get; set; }

    /// <summary>
    /// Duration of the sync operation
    /// </summary>
    public TimeSpan? Duration => CompletedAt.HasValue ? CompletedAt.Value - StartedAt : null;

    /// <summary>
    /// Created at
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by
    /// </summary>
    public Guid? CreatedBy { get; set; }

    /// <summary>
    /// Modified at
    /// </summary>
    public DateTime ModifiedAt { get; set; }

    /// <summary>
    /// Modified by
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}
