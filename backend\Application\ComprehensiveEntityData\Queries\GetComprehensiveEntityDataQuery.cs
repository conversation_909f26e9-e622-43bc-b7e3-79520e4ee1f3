using Application.ComprehensiveEntityData.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.ComprehensiveEntityData.Queries;

/// <summary>
/// Get comprehensive entity data query
/// </summary>
public class GetComprehensiveEntityDataQuery : IRequest<Result<ComprehensiveEntityDataResponseDto>>
{
    /// <summary>
    /// Product ID filter
    /// </summary>
    public Guid? ProductId { get; set; }

    /// <summary>
    /// Feature ID filter
    /// </summary>
    public Guid? FeatureId { get; set; }

    /// <summary>
    /// Object ID filter
    /// </summary>
    public Guid? ObjectId { get; set; }

    /// <summary>
    /// Search term for names and descriptions
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Filter by active status
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Include only visible metadata
    /// </summary>
    public bool OnlyVisibleMetadata { get; set; } = true;

    /// <summary>
    /// Include only active metadata
    /// </summary>
    public bool OnlyActiveMetadata { get; set; } = true;



    /// <summary>
    /// Page number for pagination
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size for pagination
    /// </summary>
    public int PageSize { get; set; } = 50;

    /// <summary>
    /// Order by field
    /// </summary>
    public string? OrderBy { get; set; }

    /// <summary>
    /// Order direction (asc/desc)
    /// </summary>
    public string OrderDirection { get; set; } = "asc";
}
