using Application.DataTypes.Commands;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;
using Swashbuckle.AspNetCore.Annotations;

namespace Web.Host.Controllers;

/// <summary>
/// DataTypes bulk operations controller
/// </summary>
[Route("api/datatypes")]
public class DataTypesBulkController : BaseApiController
{
    /// <summary>
    /// Bulk create data types
    /// </summary>
    /// <param name="command">Bulk create command containing list of data types to create</param>
    /// <returns>Result with bulk creation response including success/failure counts and details</returns>
    /// <response code="200">Returns the bulk creation result</response>
    /// <response code="400">If the request is invalid</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpPost("bulk-create")]
    [TenantIdHeader]
    [ProducesResponseType(typeof(Result<BulkCreateDataTypesResponse>), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [SwaggerOperation(OperationId = "BulkCreateDataTypes")]
    public async Task<ActionResult<Result<BulkCreateDataTypesResponse>>> BulkCreateDataTypes(BulkCreateDataTypesCommand command)
    {
        return Ok(await Mediator.Send(command));
    }

    /// <summary>
    /// Bulk update data types
    /// </summary>
    /// <param name="command">Bulk update command containing list of data types to update</param>
    /// <returns>Result with bulk update response including success/failure counts and details</returns>
    /// <response code="200">Returns the bulk update result</response>
    /// <response code="400">If the request is invalid</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpPut("bulk-update")]
    [TenantIdHeader]
    [ProducesResponseType(typeof(Result<BulkUpdateDataTypesResponse>), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [SwaggerOperation(OperationId = "BulkUpdateDataTypes")]
    public async Task<ActionResult<Result<BulkUpdateDataTypesResponse>>> BulkUpdateDataTypes(BulkUpdateDataTypesCommand command)
    {
        return Ok(await Mediator.Send(command));
    }
}
