using Abstraction.Common;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Infrastructure.Database.Repositories;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Infrastructure.Extensions;

/// <summary>
/// Startup configuration for dynamic operations
/// </summary>
public static class DynamicOperationsStartup
{
    /// <summary>
    /// Add dynamic operations services
    /// </summary>
    public static IServiceCollection AddDynamicOperations(this IServiceCollection services, IConfiguration configuration)
    {
        // Register dynamic operation configuration
        services.Configure<DynamicOperationConfig>(configuration.GetSection("DynamicOperations"));
        
        // Register dynamic operation configuration as singleton
        services.AddSingleton(provider =>
        {
            var config = new DynamicOperationConfig();
            configuration.GetSection("DynamicOperations").Bind(config);
            return config;
        });

        // Register dynamic repositories for core entities
        services.AddScoped<IDynamicRepository<Product>, DynamicRepository<Product>>();
        services.AddScoped<IDynamicRepository<Feature>, DynamicRepository<Feature>>();
        services.AddScoped<IDynamicRepository<Domain.Entities.Object>, DynamicRepository<Domain.Entities.Object>>();
        services.AddScoped<IDynamicRepository<Subscription>, DynamicRepository<Subscription>>();

        // Register dynamic repositories for metadata entities
        services.AddScoped<IDynamicRepository<ProductMetadata>, DynamicRepository<ProductMetadata>>();
        services.AddScoped<IDynamicRepository<FeatureMetadata>, DynamicRepository<FeatureMetadata>>();
        services.AddScoped<IDynamicRepository<ObjectMetadata>, DynamicRepository<ObjectMetadata>>();
        services.AddScoped<IDynamicRepository<SubscriptionMetadata>, DynamicRepository<SubscriptionMetadata>>();
        services.AddScoped<IDynamicRepository<UserMetadata>, DynamicRepository<UserMetadata>>();
        services.AddScoped<IDynamicRepository<RoleMetadata>, DynamicRepository<RoleMetadata>>();
        services.AddScoped<IDynamicRepository<TenantInfoMetadata>, DynamicRepository<TenantInfoMetadata>>();

        // Register dynamic repositories for value entities
        services.AddScoped<IDynamicRepository<ProductValue>, DynamicRepository<ProductValue>>();
        services.AddScoped<IDynamicRepository<FeatureValue>, DynamicRepository<FeatureValue>>();
        services.AddScoped<IDynamicRepository<ObjectValue>, DynamicRepository<ObjectValue>>();
        services.AddScoped<IDynamicRepository<SubscriptionValue>, DynamicRepository<SubscriptionValue>>();
        services.AddScoped<IDynamicRepository<UserValue>, DynamicRepository<UserValue>>();
        services.AddScoped<IDynamicRepository<RoleValue>, DynamicRepository<RoleValue>>();
        services.AddScoped<IDynamicRepository<TenantInfoValue>, DynamicRepository<TenantInfoValue>>();

        // Register dynamic repositories for integration entities
        services.AddScoped<IDynamicRepository<Integration>, DynamicRepository<Integration>>();
        services.AddScoped<IDynamicRepository<IntegrationApi>, DynamicRepository<IntegrationApi>>();
        services.AddScoped<IDynamicRepository<IntegrationConfiguration>, DynamicRepository<IntegrationConfiguration>>();
        services.AddScoped<IDynamicRepository<FieldMapping>, DynamicRepository<FieldMapping>>();
        services.AddScoped<IDynamicRepository<ConflictResolution>, DynamicRepository<ConflictResolution>>();
        services.AddScoped<IDynamicRepository<SyncHistory>, DynamicRepository<SyncHistory>>();

        // Register dynamic repositories for core system entities
        services.AddScoped<IDynamicRepository<DataType>, DynamicRepository<DataType>>();
        services.AddScoped<IDynamicRepository<Metadata>, DynamicRepository<Metadata>>();

        return services;
    }

    /// <summary>
    /// Add bulk operation extensions (for future EF Core bulk extensions integration)
    /// </summary>
    public static IServiceCollection AddBulkOperationExtensions(this IServiceCollection services, IConfiguration configuration)
    {
        // Future: Add EF Core bulk extensions configuration
        // services.AddDbContextBulkExtensions();
        
        return services;
    }

    /// <summary>
    /// Add dynamic operation validation services
    /// </summary>
    public static IServiceCollection AddDynamicOperationValidation(this IServiceCollection services)
    {
        // Future: Add validation services for dynamic operations
        // services.AddScoped<IDynamicOperationValidator, DynamicOperationValidator>();
        
        return services;
    }

    /// <summary>
    /// Add dynamic operation caching services
    /// </summary>
    public static IServiceCollection AddDynamicOperationCaching(this IServiceCollection services, IConfiguration configuration)
    {
        // Future: Add caching for dynamic operations
        // services.AddMemoryCache();
        // services.AddStackExchangeRedisCache(options =>
        // {
        //     options.Configuration = configuration.GetConnectionString("Redis");
        // });
        
        return services;
    }

    /// <summary>
    /// Add dynamic operation monitoring and metrics
    /// </summary>
    public static IServiceCollection AddDynamicOperationMonitoring(this IServiceCollection services)
    {
        // Future: Add monitoring and metrics for dynamic operations
        // services.AddScoped<IDynamicOperationMetrics, DynamicOperationMetrics>();
        
        return services;
    }

    /// <summary>
    /// Add all dynamic operation services
    /// </summary>
    public static IServiceCollection AddAllDynamicOperationServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddDynamicOperations(configuration);
        services.AddBulkOperationExtensions(configuration);
        services.AddDynamicOperationValidation();
        services.AddDynamicOperationCaching(configuration);
        services.AddDynamicOperationMonitoring();

        return services;
    }
}
