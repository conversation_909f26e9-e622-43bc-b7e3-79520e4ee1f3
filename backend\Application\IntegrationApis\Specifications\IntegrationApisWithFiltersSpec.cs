using Ardalis.Specification;
using Domain.Entities;

namespace Application.IntegrationApis.Specifications;

/// <summary>
/// Specification to get integration APIs with filters and pagination
/// </summary>
public class IntegrationApisWithFiltersSpec : Specification<IntegrationApi>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public IntegrationApisWithFiltersSpec(
        string? searchTerm,
        bool? isActive,
        Guid? productId,
        int skip,
        int take)
    {
        Query.Where(ia => !ia.IsDeleted); // Base query - exclude deleted

        if (!string.IsNullOrEmpty(searchTerm))
        {
            Query.Where(ia => ia.Name.Contains(searchTerm) ||
                             ia.EndpointUrl.Contains(searchTerm));
        }

        if (isActive.HasValue)
        {
            Query.Where(ia => ia.IsActive == isActive.Value);
        }

        if (productId.HasValue)
        {
            Query.Where(ia => ia.ProductId == productId.Value);
        }

        Query.Include(ia => ia.Product)
             .OrderBy(ia => ia.Name)
             .Skip(skip)
             .Take(take);
    }
}

/// <summary>
/// Specification to count integration APIs with filters
/// </summary>
public class IntegrationApisCountSpec : Specification<IntegrationApi>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public IntegrationApisCountSpec(
        string? searchTerm,
        bool? isActive,
        Guid? productId)
    {
        Query.Where(ia => !ia.IsDeleted); // Base query - exclude deleted

        if (!string.IsNullOrEmpty(searchTerm))
        {
            Query.Where(ia => ia.Name.Contains(searchTerm) ||
                             ia.EndpointUrl.Contains(searchTerm));
        }

        if (isActive.HasValue)
        {
            Query.Where(ia => ia.IsActive == isActive.Value);
        }

        if (productId.HasValue)
        {
            Query.Where(ia => ia.ProductId == productId.Value);
        }
    }
}
