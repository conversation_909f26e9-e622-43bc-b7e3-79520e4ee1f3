using Domain.Common.Contracts;
using Microsoft.AspNetCore.Identity;

namespace Domain.Entities;

/// <summary>
/// User role mapping entity - extends Identity's IdentityUserRole
/// </summary>
public class UserRole : IdentityUserRole<Guid>, IAggregateRoot
{
    // Navigation properties
    /// <summary>
    /// User
    /// </summary>
    public virtual User User { get; set; } = null!;

    /// <summary>
    /// Role
    /// </summary>
    public virtual Role Role { get; set; } = null!;
}
