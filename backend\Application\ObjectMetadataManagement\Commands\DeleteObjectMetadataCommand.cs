using MediatR;
using Shared.Common.Response;

namespace Application.ObjectMetadataManagement.Commands;

/// <summary>
/// Delete ObjectMetadata command
/// </summary>
public class DeleteObjectMetadataCommand : IRequest<Result<bool>>
{
    /// <summary>
    /// ObjectMetadata ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteObjectMetadataCommand(Guid id)
    {
        Id = id;
    }
}
