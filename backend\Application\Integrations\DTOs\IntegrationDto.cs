namespace Application.Integrations.DTOs;

/// <summary>
/// Integration DTO
/// </summary>
public class IntegrationDto
{
    /// <summary>
    /// Integration ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Product ID this integration belongs to
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Integration name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Authentication type
    /// </summary>
    public string AuthType { get; set; } = string.Empty;

    /// <summary>
    /// Authentication configuration stored as JSON
    /// </summary>
    public string AuthConfig { get; set; } = string.Empty;

    /// <summary>
    /// Whether the integration is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Sync frequency for automatic synchronization
    /// </summary>
    public TimeSpan? SyncFrequency { get; set; }

    /// <summary>
    /// Last synchronization timestamp
    /// </summary>
    public DateTime? LastSyncAt { get; set; }

    /// <summary>
    /// Created at
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by
    /// </summary>
    public Guid? CreatedBy { get; set; }

    /// <summary>
    /// Modified at
    /// </summary>
    public DateTime ModifiedAt { get; set; }

    /// <summary>
    /// Modified by
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}
