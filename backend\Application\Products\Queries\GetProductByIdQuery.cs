using Application.Products.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Products.Queries;

/// <summary>
/// Get product by ID query
/// </summary>
public class GetProductByIdQuery : IRequest<Result<ProductDto>>
{
    /// <summary>
    /// Product ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public GetProductByIdQuery(Guid id)
    {
        Id = id;
    }
}
