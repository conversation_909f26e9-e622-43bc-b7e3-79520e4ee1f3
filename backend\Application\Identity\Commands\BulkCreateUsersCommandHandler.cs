using Application.Identity.DTOs;
using Abstraction.Identity;
using Abstraction.Identity.Dtos;
using MediatR;
using Shared.Common.Response;
using Mapster;
using Abstraction.Database.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace Application.Identity.Commands;

/// <summary>
/// Handler for BulkCreateUsersCommand
/// </summary>
public class BulkCreateUsersCommandHandler : IRequestHandler<BulkCreateUsersCommand, ApiResponse<BulkCreateUsersResponse>>
{
    private readonly IIdentityService _identityService;
    private readonly IMediator _mediator;
    private readonly IRepository<Domain.Entities.Product> _repository;
    /// <summary>
    /// Constructor
    /// </summary>
    public BulkCreateUsersCommandHandler(IIdentityService identityService, IMediator mediator, IRepository<Domain.Entities.Product> repository)
    {
        _identityService = identityService;
        _mediator = mediator;
        _repository = repository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<ApiResponse<BulkCreateUsersResponse>> Handle(BulkCreateUsersCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var response = new BulkCreateUsersResponse
            {
                TotalRequested = request.Users.Count,
                Message = "Bulk user creation completed."
            };

            if (!request.Users.Any())
            {
                return new ApiResponse<BulkCreateUsersResponse>(false, "No users provided for creation.", response);
            }

            // Validate all users first if requested
            if (request.ValidateBeforeCreate)
            {
                var validationErrors = await ValidateUsersAsync(request.Users);
                if (validationErrors.Any())
                {
                    response.Errors.AddRange(validationErrors);
                    response.Failed = validationErrors.Count;
                    return new ApiResponse<BulkCreateUsersResponse>(false,
                        $"Validation failed for {validationErrors.Count} users. No users were created.", response);
                }
            }

            // Process each user
            for (int i = 0; i < request.Users.Count; i++)
            {
                var userCommand = request.Users[i];

                try
                {
                    // Use the existing CreateUserCommandHandler logic
                    var createResult = await _mediator.Send(userCommand, cancellationToken);

                    if (createResult.Succeeded && createResult.Data != null)
                    {
                        response.CreatedUsers.Add(createResult.Data);
                        response.SuccessfullyCreated++;
                    }
                    else
                    {
                        response.Errors.Add(new BulkUserCreationError
                        {
                            UserIndex = i,
                            Email = userCommand.Email,
                            UserName = userCommand.UserName,
                            ErrorMessage = createResult.Message ?? "Unknown error occurred",
                            ErrorDetails = createResult.Message
                        });
                        response.Failed++;

                        // Stop processing if ContinueOnError is false
                        if (!request.ContinueOnError)
                        {
                            response.Message = $"Bulk creation stopped at user {i + 1} due to error. {response.SuccessfullyCreated} users created successfully.";
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    response.Errors.Add(new BulkUserCreationError
                    {
                        UserIndex = i,
                        Email = userCommand.Email,
                        UserName = userCommand.UserName,
                        ErrorMessage = $"Exception occurred: {ex.Message}",
                        ErrorDetails = ex.ToString()
                    });
                    response.Failed++;

                    // Stop processing if ContinueOnError is false
                    if (!request.ContinueOnError)
                    {
                        response.Message = $"Bulk creation stopped at user {i + 1} due to exception. {response.SuccessfullyCreated} users created successfully.";
                        break;
                    }
                }
            }

            // Update final message
            if (response.Failed == 0)
            {
                var product = await _repository.GetByIdAsync(request.ProductId);
                if (product !=  null)
                {
                    product.IsUserImported = true;
                    await _repository.UpdateAsync(product);
                }
                response.Message = $"All {response.SuccessfullyCreated} users created successfully.";
                return new ApiResponse<BulkCreateUsersResponse>(true, response.Message, response);
            }
            else if (response.SuccessfullyCreated > 0)
            {
                response.Message = $"Partial success: {response.SuccessfullyCreated} users created, {response.Failed} failed.";
                return new ApiResponse<BulkCreateUsersResponse>(true, response.Message, response);
            }
            else
            {
                response.Message = $"All {response.Failed} user creations failed.";
                return new ApiResponse<BulkCreateUsersResponse>(false, response.Message, response);
            }
        }
        catch (Exception ex)
        {
            var errorResponse = new BulkCreateUsersResponse
            {
                TotalRequested = request.Users.Count,
                Failed = request.Users.Count,
                Message = $"Bulk user creation failed: {ex.Message}"
            };

            return new ApiResponse<BulkCreateUsersResponse>(false, errorResponse.Message, errorResponse);
        }
    }

    /// <summary>
    /// Validate users before creation
    /// </summary>
    private async Task<List<BulkUserCreationError>> ValidateUsersAsync(List<CreateUserCommand> users)
    {
        var errors = new List<BulkUserCreationError>();

        for (int i = 0; i < users.Count; i++)
        {
            var user = users[i];
            var userErrors = new List<string>();

            // Basic validation
            if (string.IsNullOrWhiteSpace(user.UserName))
                userErrors.Add("Username is required");

            if (string.IsNullOrWhiteSpace(user.Email))
                userErrors.Add("Email is required");

            if (string.IsNullOrWhiteSpace(user.Password))
                userErrors.Add("Password is required");

            if (user.Password != user.ConfirmPassword)
                userErrors.Add("Passwords do not match");

            // Check for duplicates in the batch
            var duplicateEmails = users.Where((u, index) => index != i && u.Email.Equals(user.Email, StringComparison.OrdinalIgnoreCase)).Any();
            if (duplicateEmails)
                userErrors.Add("Duplicate email found in the batch");

            var duplicateUserNames = users.Where((u, index) => index != i && u.UserName.Equals(user.UserName, StringComparison.OrdinalIgnoreCase)).Any();
            if (duplicateUserNames)
                userErrors.Add("Duplicate username found in the batch");

            // Check if user already exists in the system
            try
            {
                if (!string.IsNullOrWhiteSpace(user.Email) && await _identityService.ExistsWithEmailAsync(user.Email))
                    userErrors.Add("User with this email already exists");

                if (!string.IsNullOrWhiteSpace(user.UserName) && await _identityService.ExistsWithNameAsync(user.UserName))
                    userErrors.Add("User with this username already exists");
            }
            catch (Exception ex)
            {
                userErrors.Add($"Error checking user existence: {ex.Message}");
            }

            if (userErrors.Any())
            {
                errors.Add(new BulkUserCreationError
                {
                    UserIndex = i,
                    Email = user.Email,
                    UserName = user.UserName,
                    ErrorMessage = string.Join("; ", userErrors)
                });
            }
        }

        return errors;
    }
}
