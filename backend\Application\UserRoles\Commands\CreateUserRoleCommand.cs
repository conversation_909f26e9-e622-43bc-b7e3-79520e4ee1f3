using Application.UserRoles.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.UserRoles.Commands;

/// <summary>
/// Create UserRole command
/// </summary>
public class CreateUserRoleCommand : IRequest<Result<UserRoleDto>>
{
    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Role ID
    /// </summary>
    public Guid RoleId { get; set; }
}
