using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for Subscription entity
/// </summary>
public class SubscriptionConfig : IEntityTypeConfiguration<Subscription>
{
    public void Configure(EntityTypeBuilder<Subscription> builder)
    {
        builder.ToTable("Subscriptions", "Genp");

        // Properties
        builder.Property(e => e.ProductId)
            .IsRequired();

        builder.Property(e => e.SubscriptionType)
            .HasMaxLength(50)
            .HasDefaultValue("standard")
            .IsRequired();

        builder.Property(e => e.Status)
            .HasMaxLength(50)
            .HasDefaultValue("active")
            .IsRequired();

        builder.Property(e => e.StartDate)
            .HasDefaultValueSql("NOW()")
            .IsRequired();

        builder.Property(e => e.EndDate);

        builder.Property(e => e.AutoRenew)
            .HasDefaultValue(true);

        builder.Property(e => e.PricingTier)
            .HasMaxLength(50);

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        // Indexes
        builder.HasIndex(e => e.ProductId)
            .HasDatabaseName("IX_Subscriptions_ProductId");

        builder.HasIndex(e => e.SubscriptionType)
            .HasDatabaseName("IX_Subscriptions_SubscriptionType");

        builder.HasIndex(e => e.Status)
            .HasDatabaseName("IX_Subscriptions_Status");

        builder.HasIndex(e => e.StartDate)
            .HasDatabaseName("IX_Subscriptions_StartDate");

        builder.HasIndex(e => e.EndDate)
            .HasDatabaseName("IX_Subscriptions_EndDate");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_Subscriptions_IsActive")
            .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

        // Multi-tenancy
        builder.IsMultiTenant();

        // Relationships
        builder.HasOne(e => e.Product)
            .WithMany(e => e.Subscriptions)
            .HasForeignKey(e => e.ProductId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(e => e.SubscriptionMetadata)
            .WithOne(e => e.Subscription)
            .HasForeignKey(e => e.SubscriptionId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
