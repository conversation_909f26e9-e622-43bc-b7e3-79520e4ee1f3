namespace Application.UserValues.DTOs;

/// <summary>
/// Response DTO for user data creation
/// </summary>
public class UserDataResponseDto
{
    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Number of metadata entries created
    /// </summary>
    public int MetadataCreated { get; set; }

    /// <summary>
    /// Number of user metadata links created
    /// </summary>
    public int UserMetadataCreated { get; set; }

    /// <summary>
    /// Number of user values created
    /// </summary>
    public int UserValuesCreated { get; set; }

    /// <summary>
    /// List of created metadata keys
    /// </summary>
    public List<string> CreatedMetadataKeys { get; set; } = new();

    /// <summary>
    /// Success message
    /// </summary>
    public string Message { get; set; } = string.Empty;
}
