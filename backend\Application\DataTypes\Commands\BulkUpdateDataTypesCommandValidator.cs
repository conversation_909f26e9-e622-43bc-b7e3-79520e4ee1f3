using FluentValidation;

namespace Application.DataTypes.Commands;

/// <summary>
/// Validator for bulk update data types command
/// </summary>
public class BulkUpdateDataTypesCommandValidator : AbstractValidator<BulkUpdateDataTypesCommand>
{
    public BulkUpdateDataTypesCommandValidator()
    {
        RuleFor(x => x.DataTypes)
            .NotNull()
            .WithMessage("DataTypes list cannot be null")
            .NotEmpty()
            .WithMessage("DataTypes list cannot be empty")
            .Must(x => x.Count <= 1000)
            .WithMessage("Cannot update more than 1000 data types in a single bulk operation");

        RuleForEach(x => x.DataTypes)
            .NotNull()
            .WithMessage("Individual data type commands cannot be null");

        // Validate for duplicate IDs within the request
        RuleFor(x => x.DataTypes)
            .Must(HaveUniqueIds)
            .WithMessage("Duplicate data type IDs found within the request. Each data type must have a unique ID.");
    }

    private static bool HaveUniqueIds(List<UpdateDataTypeCommand> dataTypes)
    {
        if (dataTypes == null || !dataTypes.Any())
            return true;

        var ids = dataTypes.Select(dt => dt.Id).ToList();
        return ids.Count == ids.Distinct().Count();
    }
}
