namespace Application.RoleMetadata.DTOs;

/// <summary>
/// RoleMetadata DTO
/// </summary>
public class RoleMetadataDto
{
    /// <summary>
    /// RoleMetadata ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Role ID
    /// </summary>
    public Guid RoleId { get; set; }

    /// <summary>
    /// Role name
    /// </summary>
    public string? RoleName { get; set; }

    /// <summary>
    /// Metadata ID
    /// </summary>
    public Guid MetadataId { get; set; }

    /// <summary>
    /// Metadata key
    /// </summary>
    public string? MetadataKey { get; set; }

    /// <summary>
    /// Whether this metadata is unique for the role
    /// </summary>
    public bool IsUnique { get; set; }

    /// <summary>
    /// Whether this metadata link is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Created at
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by
    /// </summary>
    public Guid CreatedBy { get; set; }

    /// <summary>
    /// Modified at
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Modified by
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}
