using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Feature entity - stores feature types/templates
/// </summary>
public class Feature : AuditableEntity, IAggregateRoot
{
    /// <summary>
    /// Product ID this feature belongs to
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Feature name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Feature description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Whether this is the default feature
    /// </summary>
    public bool IsDefault { get; set; } = false;

    /// <summary>
    /// Whether the feature is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    // Navigation properties
    /// <summary>
    /// Product this feature belongs to
    /// </summary>
    public virtual Product Product { get; set; } = null!;

    /// <summary>
    /// Objects belonging to this feature
    /// </summary>
    public virtual ICollection<Object> Objects { get; set; } = new List<Object>();

    /// <summary>
    /// Feature metadata links
    /// </summary>
    public virtual ICollection<FeatureMetadata> FeatureMetadata { get; set; } = new List<FeatureMetadata>();
}
