namespace Application.UserValues.DTOs;

/// <summary>
/// UserValue DTO
/// </summary>
public class UserValueDto
{
    /// <summary>
    /// UserValue ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// User metadata ID
    /// </summary>
    public Guid UserMetadataId { get; set; }

    /// <summary>
    /// User name
    /// </summary>
    public string? UserName { get; set; }

    /// <summary>
    /// Metadata key
    /// </summary>
    public string? MetadataKey { get; set; }

    /// <summary>
    /// Reference ID for grouping related values
    /// </summary>
    public Guid? RefId { get; set; }

    /// <summary>
    /// Parent user value ID for hierarchical structure
    /// </summary>
    public Guid? ParentUserValueId { get; set; }

    /// <summary>
    /// The actual value as text
    /// </summary>
    public string? Value { get; set; }

    /// <summary>
    /// Created at
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by
    /// </summary>
    public Guid CreatedBy { get; set; }

    /// <summary>
    /// Modified at
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Modified by
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}
