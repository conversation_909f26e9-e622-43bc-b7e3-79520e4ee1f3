using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for RoleMetadata entity
/// </summary>
public class RoleMetadataConfig : IEntityTypeConfiguration<RoleMetadata>
{
    public void Configure(EntityTypeBuilder<RoleMetadata> builder)
    {
        builder.ToTable("RoleMetadata", "Genp");

        // Properties
        builder.Property(e => e.RoleId)
            .IsRequired();

        builder.Property(e => e.MetadataId)
            .IsRequired();

        builder.Property(e => e.IsUnique)
            .HasDefaultValue(false);

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        // Indexes
        builder.HasIndex(e => e.RoleId)
            .HasDatabaseName("IX_RoleMetadata_RoleId");

        builder.HasIndex(e => e.MetadataId)
            .HasDatabaseName("IX_RoleMetadata_MetadataId");

        builder.HasIndex(e => e.IsUnique)
            .HasDatabaseName("IX_RoleMetadata_IsUnique");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_RoleMetadata_IsActive")
            .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

        // Unique constraint
        builder.HasIndex(e => new { e.RoleId, e.MetadataId })
            .IsUnique()
            .HasDatabaseName("IX_RoleMetadata_RoleId_MetadataId");

        // Multi-tenancy
        builder.IsMultiTenant();

        // Relationships
        builder.HasOne(e => e.Role)
            .WithMany(e => e.RoleMetadata)
            .HasForeignKey(e => e.RoleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.Metadata)
            .WithMany(e => e.RoleMetadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.RoleValues)
            .WithOne(e => e.RoleMetadata)
            .HasForeignKey(e => e.RoleMetadataId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
