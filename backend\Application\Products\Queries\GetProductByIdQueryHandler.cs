using Application.Products.DTOs;
using Application.Products.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.Products.Queries;

/// <summary>
/// Get product by ID query handler
/// </summary>
public class GetProductByIdQueryHandler : IRequestHandler<GetProductByIdQuery, Result<ProductDto>>
{
    private readonly IReadRepository<Product> _productRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetProductByIdQueryHandler(IReadRepository<Product> productRepository)
    {
        _productRepository = productRepository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<ProductDto>> Handle(GetProductByIdQuery request, CancellationToken cancellationToken)
    {
        // Get product using specification (tenant isolation handled by Finbuckle.MultiTenant)
        var productSpec = new ProductByIdSpec(request.Id);
        var product = await _productRepository.GetBySpecAsync(productSpec, cancellationToken);

        if (product == null)
        {
            return Result<ProductDto>.Failure("Product not found.");
        }

        var dto = product.Adapt<ProductDto>();
        return Result<ProductDto>.Success(dto);
    }
}
