using Application.Identity.Commands;
using Application.Identity.DTOs;
using Abstraction.Identity;
using Abstraction.Identity.Dtos;
using MediatR;
using Shared.Common.Response;
using Mapster;

namespace Application.Identity.Handlers;

/// <summary>
/// Handler for UpdateUserRolesCommand
/// </summary>
public class UpdateUserRolesCommandHandler : IRequestHandler<UpdateUserRolesCommand, ApiResponse<string>>
{
    private readonly IIdentityService _identityService;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpdateUserRolesCommandHandler(IIdentityService identityService)
    {
        _identityService = identityService;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<ApiResponse<string>> Handle(UpdateUserRolesCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Validate the request
            if (string.IsNullOrWhiteSpace(request.UserId))
            {
                return new ApiResponse<string>(false, "User ID is required.", string.Empty);
            }

            if (request.UserRoles == null)
            {
                return new ApiResponse<string>(false, "User roles list is required.", string.Empty);
            }

            // Check if user exists
            var user = await _identityService.GetUserByIdAsync(request.UserId);
            if (user == null)
            {
                return new ApiResponse<string>(false, "User not found.", string.Empty);
            }

            // Convert UserRoleDto to UserRoleDetailsDto
            var userRoleDetails = request.UserRoles.Adapt<List<UserRoleDetailsDto>>();

            // Update user roles
            var result = await _identityService.UpdateUserRolesAsync(request.UserId, userRoleDetails);

            if (result.Succeeded)
            {
                return new ApiResponse<string>(true, "User roles updated successfully.", request.UserId);
            }
            else
            {
                return new ApiResponse<string>(false, result.Message, string.Empty);
            }
        }
        catch (Exception ex)
        {
            return new ApiResponse<string>(false, $"Error updating user roles: {ex.Message}", string.Empty);
        }
    }
}
