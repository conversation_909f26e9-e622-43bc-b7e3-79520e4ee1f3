using Abstraction.Database.Repositories;
using Dapper;
using Domain.MultiTenancy;
using Domain.Views;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Npgsql;
using System.Data;
using System.Text;

namespace Infrastructure.Database.Repositories;

/// <summary>
/// Repository implementation for comprehensive entity data view
/// </summary>
public class ComprehensiveEntityDataRepository : IComprehensiveEntityDataRepository
{
    private readonly string _connectionString;
    private readonly ILogger<ComprehensiveEntityDataRepository> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public ComprehensiveEntityDataRepository(
        IOptions<DatabaseSettings> options,
        ILogger<ComprehensiveEntityDataRepository> logger)
    {
        _connectionString = options.Value.ConnectionString;
        _logger = logger;
    }

    /// <summary>
    /// Create a new database connection
    /// </summary>
    private IDbConnection CreateConnection() => new NpgsqlConnection(_connectionString);

    /// <summary>
    /// Get comprehensive entity data with filters
    /// </summary>
    public async Task<IEnumerable<VwComprehensiveEntityData>> GetComprehensiveEntityDataAsync(
        Guid? productId = null,
        Guid? featureId = null,
        Guid? objectId = null,
        string? searchTerm = null,
        bool? isActive = null,
        bool onlyVisibleMetadata = true,
        bool onlyActiveMetadata = true,
        int pageNumber = 1,
        int pageSize = 50,
        string? orderBy = null,
        string orderDirection = "asc",
        CancellationToken cancellationToken = default)
    {
        try
        {
            var (sql, parameters) = BuildQuery(
                productId, featureId, objectId, searchTerm, isActive,
                onlyVisibleMetadata, onlyActiveMetadata, pageNumber, pageSize,
                orderBy, orderDirection, false);

            using var connection = CreateConnection();
            connection.Open();

            _logger.LogInformation("Executing comprehensive entity data query with filters: ProductId={ProductId}, FeatureId={FeatureId}, ObjectId={ObjectId}",
                productId, featureId, objectId);

            var result = await connection.QueryAsync<VwComprehensiveEntityData>(sql, parameters);
            
            _logger.LogInformation("Retrieved {Count} comprehensive entity data records", result.Count());
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving comprehensive entity data");
            throw;
        }
    }

    /// <summary>
    /// Get count of comprehensive entity data with filters
    /// </summary>
    public async Task<int> GetComprehensiveEntityDataCountAsync(
        Guid? productId = null,
        Guid? featureId = null,
        Guid? objectId = null,
        string? searchTerm = null,
        bool? isActive = null,
        bool onlyVisibleMetadata = true,
        bool onlyActiveMetadata = true,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var (sql, parameters) = BuildQuery(
                productId, featureId, objectId, searchTerm, isActive,
                onlyVisibleMetadata, onlyActiveMetadata, 1, int.MaxValue,
                null, "asc", true);

            using var connection = CreateConnection();
            connection.Open();

            var count = await connection.QuerySingleAsync<int>(sql, parameters);
            
            _logger.LogInformation("Count query returned {Count} records", count);
            
            return count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting comprehensive entity data count");
            throw;
        }
    }

    /// <summary>
    /// Build SQL query with filters
    /// </summary>
    private (string sql, object parameters) BuildQuery(
    Guid? productId, Guid? featureId, Guid? objectId, string? searchTerm, bool? isActive,
    bool onlyVisibleMetadata, bool onlyActiveMetadata, int pageNumber, int pageSize,
    string? orderBy, string orderDirection, bool isCountQuery)
    {
        var sql = new StringBuilder();

        if (isCountQuery)
        {
            sql.Append("SELECT COUNT(DISTINCT CONCAT(\"ProductId\", '-', COALESCE(\"FeatureId\"::text, ''), '-', COALESCE(\"ObjectId\"::text, ''), '-', COALESCE(\"MetadataId\"::text, ''))) ");
        }
        else
        {
            sql.Append("SELECT * ");
        }

        sql.Append("FROM \"Genp\".\"VwComprehensiveEntityData\" WHERE 1=1 ");

        var parameters = new DynamicParameters();

        // Add filters
        if (productId.HasValue)
        {
            sql.Append("AND \"ProductId\" = @ProductId ");
            parameters.Add("ProductId", productId.Value);
        }

        if (featureId.HasValue)
        {
            sql.Append("AND \"FeatureId\" = @FeatureId ");
            parameters.Add("FeatureId", featureId.Value);
        }

        if (objectId.HasValue)
        {
            sql.Append("AND \"ObjectId\" = @ObjectId ");
            parameters.Add("ObjectId", objectId.Value);
        }

        if (!string.IsNullOrEmpty(searchTerm))
        {
            sql.Append("AND (\"ProductName\" ILIKE @SearchTerm OR \"ProductDescription\" ILIKE @SearchTerm ");
            sql.Append("OR \"FeatureName\" ILIKE @SearchTerm OR \"FeatureDescription\" ILIKE @SearchTerm ");
            sql.Append("OR \"ObjectName\" ILIKE @SearchTerm OR \"ObjectDescription\" ILIKE @SearchTerm ");
            sql.Append("OR \"MetadataKey\" ILIKE @SearchTerm OR \"DisplayLabel\" ILIKE @SearchTerm) ");
            parameters.Add("SearchTerm", $"%{searchTerm}%");
        }

        if (isActive.HasValue)
        {
            sql.Append("AND \"ProductIsActive\" = @IsActive ");
            parameters.Add("IsActive", isActive.Value);
        }

        if (onlyVisibleMetadata)
        {
            sql.Append("AND (\"IsVisible\" IS NULL OR \"IsVisible\" = true) ");
        }

        if (onlyActiveMetadata)
        {
            sql.Append("AND (\"ProductMetadataIsActive\" IS NULL OR \"ProductMetadataIsActive\" = true) ");
            sql.Append("AND (\"FeatureMetadataIsActive\" IS NULL OR \"FeatureMetadataIsActive\" = true) ");
            sql.Append("AND (\"ObjectMetadataIsActive\" IS NULL OR \"ObjectMetadataIsActive\" = true) ");
        }

        if (!isCountQuery)
        {
            // Add ordering
            if (!string.IsNullOrEmpty(orderBy))
            {
                var validOrderFields = new[] { "ProductName", "FeatureName", "ObjectName", "MetadataKey", "FieldOrder" };
                if (validOrderFields.Contains(orderBy))
                {
                    sql.Append($"ORDER BY \"{orderBy}\" {(orderDirection.ToLower() == "desc" ? "DESC" : "ASC")} ");
                }
            }
            else
            {
                sql.Append("ORDER BY \"ProductName\", \"FeatureName\", \"ObjectName\", \"FieldOrder\" ");
            }

            // Add pagination (optional)
            // var offset = (pageNumber - 1) * pageSize;
            // sql.Append("OFFSET @Offset LIMIT @Limit ");
            // parameters.Add("Offset", offset);
            // parameters.Add("Limit", pageSize);
        }

        return (sql.ToString(), parameters);
    }
}
