using Application.ProductValues.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.ProductValues.Commands;

/// <summary>
/// Create ProductValue command
/// </summary>
public class CreateProductValueCommand : IRequest<Result<ProductValueDto>>
{
    /// <summary>
    /// Product metadata ID
    /// </summary>
    public Guid ProductMetadataId { get; set; }

    /// <summary>
    /// Reference ID for grouping related values
    /// </summary>
    public Guid? RefId { get; set; }

    /// <summary>
    /// Parent product value ID for hierarchical structure
    /// </summary>
    public Guid? ParentProductValueId { get; set; }

    /// <summary>
    /// The actual value as text
    /// </summary>
    public string? Value { get; set; }
}
