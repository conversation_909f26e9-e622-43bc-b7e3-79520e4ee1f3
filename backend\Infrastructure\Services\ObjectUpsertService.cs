using Abstraction.Database.Repositories;
using Application.Features.DTOs;
using Application.Features.Interfaces;
using Ardalis.Specification;
using Domain.Entities;
using Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;
using System.Text.Json;

namespace Infrastructure.Services;

/// <summary>
/// Service for upserting objects with metadata and values
/// </summary>
public class ObjectUpsertService : IObjectUpsertService
{
    private readonly IRepository<Domain.Entities.Object> _objectRepository;
    private readonly IRepository<Feature> _featureRepository;
    private readonly IRepository<Metadata> _metadataRepository;
    private readonly IRepository<ObjectMetadata> _objectMetadataRepository;
    private readonly IRepository<ObjectValue> _objectValueRepository;
    private readonly INestedFeatureCreationService _nestedFeatureService;
    private readonly ApplicationDbContext _dbContext;
    private readonly ILogger<ObjectUpsertService> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public ObjectUpsertService(
        IRepository<Domain.Entities.Object> objectRepository,
        IRepository<Feature> featureRepository,
        IRepository<Metadata> metadataRepository,
        IRepository<ObjectMetadata> objectMetadataRepository,
        IRepository<ObjectValue> objectValueRepository,
        INestedFeatureCreationService nestedFeatureService,
        ApplicationDbContext dbContext,
        ILogger<ObjectUpsertService> logger)
    {
        _objectRepository = objectRepository;
        _featureRepository = featureRepository;
        _metadataRepository = metadataRepository;
        _objectMetadataRepository = objectMetadataRepository;
        _objectValueRepository = objectValueRepository;
        _nestedFeatureService = nestedFeatureService;
        _dbContext = dbContext;
        _logger = logger;
    }

    /// <summary>
    /// Upsert an object with metadata and values
    /// </summary>
    public async Task<Result<UpsertObjectWithMetadataResponseDto>> UpsertObjectWithMetadataAsync(
        ObjectUpsertDto objectData,
        string tenantId,
        string userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting object upsert: {ObjectName}, ObjectId: {ObjectId}", 
                objectData.Name, objectData.Id);

            var response = new UpsertObjectWithMetadataResponseDto();
            var warnings = new List<string>();
            var dataTypeMappings = new Dictionary<string, Guid>();
            var metadataOperations = new List<MetadataOperationDto>();

            // Start transaction
            using var transaction = await _dbContext.Database.BeginTransactionAsync(cancellationToken);

            try
            {
                Domain.Entities.Object targetObject;
                bool isNewObject = false;

                if (objectData.Id.HasValue)
                {
                    // Update existing object
                    targetObject = await _objectRepository.GetByIdAsync(objectData.Id.Value, cancellationToken);
                    if (targetObject == null || targetObject.IsDeleted)
                    {
                        return Result<UpsertObjectWithMetadataResponseDto>.Failure("Object not found");
                    }

                    // Update object properties
                    targetObject.Name = objectData.Name;
                    targetObject.Description = objectData.Description;
                    targetObject.IsActive = objectData.IsActive;

                    await _objectRepository.UpdateAsync(targetObject, cancellationToken);
                    _logger.LogInformation("Updated existing object: {ObjectId}", targetObject.Id);
                }
                else
                {
                    // Create new object
                    if (!objectData.FeatureId.HasValue)
                    {
                        return Result<UpsertObjectWithMetadataResponseDto>.Failure("FeatureId is required for new objects");
                    }

                    // Validate feature exists
                    var feature = await _featureRepository.GetByIdAsync(objectData.FeatureId.Value, cancellationToken);
                    if (feature == null || feature.IsDeleted)
                    {
                        return Result<UpsertObjectWithMetadataResponseDto>.Failure("Feature not found");
                    }

                    targetObject = new Domain.Entities.Object
                    {
                        FeatureId = objectData.FeatureId.Value,
                        ParentObjectId = objectData.ParentObjectId,
                        Name = objectData.Name,
                        Description = objectData.Description,
                        IsActive = objectData.IsActive
                    };

                    await _objectRepository.AddAsync(targetObject, cancellationToken);
                    isNewObject = true;
                    _logger.LogInformation("Created new object: {ObjectId}", targetObject.Id);
                }

                // Process metadata from MetaJson
                int metadataProcessed = 0;
                if (objectData.MetaJson.HasValue && objectData.MetaJson.Value.ValueKind == JsonValueKind.Object)
                {
                    var metadataResult = await ProcessMetadataFromJson(
                        targetObject.Id, objectData.MetaJson.Value, tenantId, userId, 
                        metadataOperations, dataTypeMappings, warnings, cancellationToken);
                    
                    metadataProcessed += metadataResult;
                }

                // Process metadata values
                int valuesCreated = 0;
                if (objectData.MetaValues.Any())
                {
                    var valuesResult = await _nestedFeatureService.CreateObjectValuesFromMetaValuesAsync(
                        targetObject.Id, objectData.MetaValues, tenantId, userId, null, cancellationToken);

                    if (valuesResult.Succeeded)
                    {
                        valuesCreated = valuesResult.Data;
                    }
                    else
                    {
                        warnings.Add($"Failed to create some object values: {valuesResult.Message}");
                    }
                }

                // Commit transaction
                await transaction.CommitAsync(cancellationToken);

                // Build response
                response.Object = new ObjectUpsertResultDto
                {
                    Id = targetObject.Id,
                    Name = targetObject.Name,
                    Description = targetObject.Description,
                    FeatureId = targetObject.FeatureId,
                    ParentObjectId = targetObject.ParentObjectId,
                    IsNewObject = isNewObject,
                    IsActive = targetObject.IsActive,
                    MetadataLinked = metadataProcessed,
                    NewMetadataCreated = metadataOperations.Count(m => m.Operation == "Created"),
                    MetadataKeysProcessed = metadataOperations.Select(m => m.MetadataKey).Distinct().ToList()
                };

                response.MetadataProcessed = metadataProcessed;
                response.ObjectValuesCreated = valuesCreated;
                response.ObjectValuesUpdated = 0; // For now, we only create new values

                response.ProcessingSummary = new ObjectUpsertProcessingSummaryDto
                {
                    TransactionsExecuted = 1,
                    Warnings = warnings,
                    DataTypeMappings = dataTypeMappings,
                    OperationType = isNewObject ? "Create" : "Update",
                    MetadataOperations = metadataOperations
                };

                _logger.LogInformation("Successfully upserted object: {ObjectId}, Metadata: {MetadataCount}, Values: {ValuesCount}",
                    targetObject.Id, metadataProcessed, valuesCreated);

                return Result<UpsertObjectWithMetadataResponseDto>.Success(response);
            }
            catch (Exception)
            {
                await transaction.RollbackAsync(cancellationToken);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error upserting object with metadata: {ObjectName}", objectData.Name);
            return Result<UpsertObjectWithMetadataResponseDto>.Failure("An error occurred while upserting the object");
        }
    }

    /// <summary>
    /// Process metadata from JSON structure
    /// </summary>
    private async Task<int> ProcessMetadataFromJson(
        Guid objectId,
        JsonElement metaJson,
        string tenantId,
        string userId,
        List<MetadataOperationDto> metadataOperations,
        Dictionary<string, Guid> dataTypeMappings,
        List<string> warnings,
        CancellationToken cancellationToken)
    {
        int metadataProcessed = 0;

        foreach (var property in metaJson.EnumerateObject())
        {
            try
            {
                // Get or create metadata
                var metadataResult = await _nestedFeatureService.GetOrCreateMetadataAsync(
                    property.Name, property.Value, tenantId, userId, cancellationToken);

                if (!metadataResult.Succeeded)
                {
                    warnings.Add($"Failed to process metadata for field '{property.Name}': {metadataResult.Message}");
                    continue;
                }

                var metadataId = metadataResult.Data;

                // Check if ObjectMetadata link already exists
                var existingLink = await _objectMetadataRepository.FirstOrDefaultAsync(
                    new ObjectMetadataByObjectAndMetadataSpec(objectId, metadataId),
                    cancellationToken);

                if (existingLink == null)
                {
                    // Create ObjectMetadata link
                    var objectMetadata = new ObjectMetadata
                    {
                        ObjectId = objectId,
                        MetadataId = metadataId,
                        IsActive = true,
                        ShouldVisibleInList = true,
                        ShouldVisibleInEdit = true,
                        ShouldVisibleInCreate = true,
                        ShouldVisibleInView = true,
                        IsCalculate = false
                    };

                    await _objectMetadataRepository.AddAsync(objectMetadata, cancellationToken);

                    metadataOperations.Add(new MetadataOperationDto
                    {
                        MetadataKey = property.Name,
                        Operation = "Linked",
                        DataType = "auto-detected",
                        MetadataId = metadataId
                    });
                }

                metadataProcessed++;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error processing metadata field {FieldName}", property.Name);
                warnings.Add($"Error processing metadata field '{property.Name}': {ex.Message}");
            }
        }

        return metadataProcessed;
    }

    /// <summary>
    /// Validate that an object exists and is accessible
    /// </summary>
    public async Task<Result<bool>> ValidateObjectAccessAsync(
        Guid objectId,
        string tenantId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var obj = await _objectRepository.GetByIdAsync(objectId, cancellationToken);
            if (obj == null || obj.IsDeleted)
            {
                return Result<bool>.Failure("Object not found or has been deleted");
            }

            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating object access: {ObjectId}", objectId);
            return Result<bool>.Failure("Error validating object access");
        }
    }

    /// <summary>
    /// Get object information by ID
    /// </summary>
    public async Task<Result<ObjectUpsertResultDto>> GetObjectInfoAsync(
        Guid objectId,
        string tenantId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var obj = await _objectRepository.GetByIdAsync(objectId, cancellationToken);
            if (obj == null || obj.IsDeleted)
            {
                return Result<ObjectUpsertResultDto>.Failure("Object not found");
            }

            var result = new ObjectUpsertResultDto
            {
                Id = obj.Id,
                Name = obj.Name,
                Description = obj.Description,
                FeatureId = obj.FeatureId,
                ParentObjectId = obj.ParentObjectId,
                IsNewObject = false,
                IsActive = obj.IsActive
            };

            return Result<ObjectUpsertResultDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting object info: {ObjectId}", objectId);
            return Result<ObjectUpsertResultDto>.Failure("Error getting object information");
        }
    }
}
