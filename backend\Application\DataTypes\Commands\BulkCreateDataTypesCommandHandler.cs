using Abstraction.Repositories;
using Application.DataTypes.DTOs;
using Domain.Entities;
using FluentValidation;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.DataTypes.Commands;

/// <summary>
/// Handler for bulk create data types command
/// </summary>
public class BulkCreateDataTypesCommandHandler : IRequestHandler<BulkCreateDataTypesCommand, Result<BulkCreateDataTypesResponse>>
{
    private readonly IDataTypeRepository _dataTypeRepository;
    private readonly ILogger<BulkCreateDataTypesCommandHandler> _logger;
    private readonly IValidator<CreateDataTypeCommand> _createValidator;

    public BulkCreateDataTypesCommandHandler(
        IDataTypeRepository dataTypeRepository,
        ILogger<BulkCreateDataTypesCommandHandler> logger,
        IValidator<CreateDataTypeCommand> createValidator)
    {
        _dataTypeRepository = dataTypeRepository;
        _logger = logger;
        _createValidator = createValidator;
    }

    public async Task<Result<BulkCreateDataTypesResponse>> Handle(BulkCreateDataTypesCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Starting bulk create operation for {Count} data types", request.DataTypes.Count);

            var response = new BulkCreateDataTypesResponse
            {
                TotalRequested = request.DataTypes.Count
            };

            // Validate all items first if requested
            if (request.ValidateBeforeCreate)
            {
                var validationErrors = await ValidateAllItems(request.DataTypes);
                if (validationErrors.Any())
                {
                    response.Errors.AddRange(validationErrors);
                    response.Failed = validationErrors.Count;
                    response.Message = $"Validation failed for {validationErrors.Count} items";
                    return Result<BulkCreateDataTypesResponse>.Success(response);
                }
            }

            // Check for duplicates if requested
            if (request.SkipDuplicates)
            {
                await HandleDuplicates(request, response);
            }

            // Process each data type
            for (int i = 0; i < request.DataTypes.Count; i++)
            {
                var dataTypeCommand = request.DataTypes[i];
                
                try
                {
                    // Skip if already marked as duplicate
                    if (response.Errors.Any(e => e.Index == i && e.ErrorType == "Duplicate"))
                    {
                        continue;
                    }

                    // Validate individual item if not validated before
                    if (!request.ValidateBeforeCreate)
                    {
                        var validationResult = await _createValidator.ValidateAsync(dataTypeCommand, cancellationToken);
                        if (!validationResult.IsValid)
                        {
                            response.Errors.Add(new BulkDataTypeCreationError
                            {
                                Index = i,
                                DataTypeName = dataTypeCommand.Name,
                                ErrorMessage = string.Join("; ", validationResult.Errors.Select(e => e.ErrorMessage)),
                                ErrorType = "Validation"
                            });
                            response.Failed++;
                            
                            if (!request.ContinueOnError)
                                break;
                            continue;
                        }
                    }

                    // Create the data type
                    var dataType = dataTypeCommand.Adapt<DataType>();
                    dataType.Id = Guid.NewGuid();
                    dataType.CreatedAt = DateTime.UtcNow;
                    dataType.ModifiedAt = DateTime.UtcNow;

                    var createdDataType = await _dataTypeRepository.AddAsync(dataType);
                    var dataTypeDto = createdDataType.Adapt<DataTypeDto>();
                    
                    response.CreatedDataTypes.Add(dataTypeDto);
                    response.SuccessfullyCreated++;

                    _logger.LogDebug("Successfully created data type: {Name} with ID: {Id}", 
                        dataTypeCommand.Name, createdDataType.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating data type at index {Index}: {Name}", i, dataTypeCommand.Name);
                    
                    response.Errors.Add(new BulkDataTypeCreationError
                    {
                        Index = i,
                        DataTypeName = dataTypeCommand.Name,
                        ErrorMessage = ex.Message,
                        ErrorType = "Database"
                    });
                    response.Failed++;

                    if (!request.ContinueOnError)
                        break;
                }
            }

            // Generate summary message
            response.Message = GenerateSummaryMessage(response);

            _logger.LogInformation("Bulk create operation completed. Success: {Success}, Failed: {Failed}, Skipped: {Skipped}",
                response.SuccessfullyCreated, response.Failed, response.Skipped);

            return Result<BulkCreateDataTypesResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during bulk create operation");
            return Result<BulkCreateDataTypesResponse>.Failure($"Bulk create operation failed: {ex.Message}");
        }
    }

    private async Task<List<BulkDataTypeCreationError>> ValidateAllItems(List<CreateDataTypeCommand> dataTypes)
    {
        var errors = new List<BulkDataTypeCreationError>();
        
        for (int i = 0; i < dataTypes.Count; i++)
        {
            var validationResult = await _createValidator.ValidateAsync(dataTypes[i]);
            if (!validationResult.IsValid)
            {
                errors.Add(new BulkDataTypeCreationError
                {
                    Index = i,
                    DataTypeName = dataTypes[i].Name,
                    ErrorMessage = string.Join("; ", validationResult.Errors.Select(e => e.ErrorMessage)),
                    ErrorType = "Validation"
                });
            }
        }

        return errors;
    }

    private async Task HandleDuplicates(BulkCreateDataTypesCommand request, BulkCreateDataTypesResponse response)
    {
        var names = request.DataTypes.Select(dt => dt.Name?.ToLower()).Where(n => !string.IsNullOrEmpty(n)).ToList();

        // Get existing data types by names (assuming this method exists or we'll use a different approach)
        var existingDataTypes = await _dataTypeRepository.GetAllAsync();
        var existingNames = existingDataTypes
            .Where(dt => !string.IsNullOrEmpty(dt.Name))
            .Select(dt => dt.Name!.ToLower())
            .ToHashSet();

        for (int i = 0; i < request.DataTypes.Count; i++)
        {
            var dataTypeName = request.DataTypes[i].Name?.ToLower();
            if (!string.IsNullOrEmpty(dataTypeName) && existingNames.Contains(dataTypeName))
            {
                response.Errors.Add(new BulkDataTypeCreationError
                {
                    Index = i,
                    DataTypeName = request.DataTypes[i].Name,
                    ErrorMessage = $"Data type with name '{request.DataTypes[i].Name}' already exists",
                    ErrorType = "Duplicate"
                });
                response.Skipped++;
            }
        }
    }

    private static string GenerateSummaryMessage(BulkCreateDataTypesResponse response)
    {
        var parts = new List<string>();
        
        if (response.SuccessfullyCreated > 0)
            parts.Add($"{response.SuccessfullyCreated} created");
        
        if (response.Failed > 0)
            parts.Add($"{response.Failed} failed");
        
        if (response.Skipped > 0)
            parts.Add($"{response.Skipped} skipped");

        return $"Bulk create completed: {string.Join(", ", parts)}";
    }
}
