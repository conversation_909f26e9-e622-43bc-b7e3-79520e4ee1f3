using MediatR;
using Shared.Common.Response;

namespace Application.Features.Commands;

/// <summary>
/// Delete feature command
/// </summary>
public class DeleteFeatureCommand : IRequest<Result<bool>>
{
    /// <summary>
    /// Feature ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteFeatureCommand(Guid id)
    {
        Id = id;
    }
}
