using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Actual feature instance data
/// </summary>
public class FeatureValue : AuditableEntity, IAggregateRoot
{

    /// <summary>
    /// Feature metadata ID
    /// </summary>
    public Guid FeatureMetadataId { get; set; }

    /// <summary>
    /// Reference ID for grouping related values
    /// </summary>
    public Guid? RefId { get; set; }

    /// <summary>
    /// The actual value as text
    /// </summary>
    public string? Value { get; set; }

    // Navigation properties
    /// <summary>
    /// Feature metadata link
    /// </summary>
    public virtual FeatureMetadata FeatureMetadata { get; set; } = null!;
}
