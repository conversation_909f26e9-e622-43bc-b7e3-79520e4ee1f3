using Application.FeatureMetadata.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.FeatureMetadata.Commands;

/// <summary>
/// Create FeatureMetadata command
/// </summary>
public class CreateFeatureMetadataCommand : IRequest<Result<FeatureMetadataDto>>
{
    /// <summary>
    /// Feature ID
    /// </summary>
    public Guid FeatureId { get; set; }

    /// <summary>
    /// Metadata ID
    /// </summary>
    public Guid MetadataId { get; set; }

    /// <summary>
    /// Whether this metadata is unique for the feature
    /// </summary>
    public bool IsUnique { get; set; } = false;

    /// <summary>
    /// Whether this metadata link is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
