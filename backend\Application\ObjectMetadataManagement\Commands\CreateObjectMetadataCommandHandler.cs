using Application.ObjectMetadataManagement.DTOs;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.ObjectMetadataManagement.Commands;

/// <summary>
/// Create ObjectMetadata command handler
/// </summary>
public class CreateObjectMetadataCommandHandler : IRequestHandler<CreateObjectMetadataCommand, Result<ObjectMetadataDto>>
{
    private readonly IRepository<Domain.Entities.ObjectMetadata> _repository;
    private readonly IRepository<Domain.Entities.Object> _objectRepository;
    private readonly IRepository<Domain.Entities.Metadata> _metadataRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateObjectMetadataCommandHandler(
        IRepository<Domain.Entities.ObjectMetadata> repository,
        IRepository<Domain.Entities.Object> objectRepository,
        IRepository<Domain.Entities.Metadata> metadataRepository)
    {
        _repository = repository;
        _objectRepository = objectRepository;
        _metadataRepository = metadataRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<ObjectMetadataDto>> Handle(CreateObjectMetadataCommand request, CancellationToken cancellationToken)
    {
        // Validate Object exists
        var objectEntity = await _objectRepository.GetByIdAsync(request.ObjectId, cancellationToken);
        if (objectEntity == null)
        {
            return Result<ObjectMetadataDto>.Failure($"Object with ID '{request.ObjectId}' not found.");
        }

        // Validate Metadata exists
        var metadata = await _metadataRepository.GetByIdAsync(request.MetadataId, cancellationToken);
        if (metadata == null)
        {
            return Result<ObjectMetadataDto>.Failure($"Metadata with ID '{request.MetadataId}' not found.");
        }

        // Create ObjectMetadata
        var objectMetadata = new Domain.Entities.ObjectMetadata
        {
            ObjectId = request.ObjectId,
            MetadataId = request.MetadataId,
            IsUnique = request.IsUnique,
            IsActive = request.IsActive
        };

        await _repository.AddAsync(objectMetadata, cancellationToken);

        var dto = new ObjectMetadataDto
        {
            Id = objectMetadata.Id,
            ObjectId = objectMetadata.ObjectId,
            ObjectName = objectEntity.Name,
            MetadataId = objectMetadata.MetadataId,
            MetadataKey = metadata.MetadataKey,
            MetadataDisplayLabel = metadata.DisplayLabel,
            IsUnique = objectMetadata.IsUnique,
            IsActive = objectMetadata.IsActive,
            ValuesCount = 0,
            CreatedAt = objectMetadata.CreatedAt,
            CreatedBy = objectMetadata.CreatedBy ?? Guid.Empty,
            ModifiedAt = objectMetadata.ModifiedAt,
            ModifiedBy = objectMetadata.ModifiedBy
        };

        return Result<ObjectMetadataDto>.Success(dto);
    }
}
