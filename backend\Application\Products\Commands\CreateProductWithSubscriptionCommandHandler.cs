using Application.Products.DTOs;
using Application.Products.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.Products.Commands;

/// <summary>
/// Handler for creating product with subscription
/// </summary>
public class CreateProductWithSubscriptionCommandHandler : IRequestHandler<CreateProductWithSubscriptionCommand, Result<ProductWithSubscriptionDto>>
{
    private readonly IRepository<Subscription> _subscriptionRepository;
    private readonly IRepository<Product> _productRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateProductWithSubscriptionCommandHandler(
        IRepository<Subscription> subscriptionRepository,
        IRepository<Product> productRepository)
    {
        _subscriptionRepository = subscriptionRepository;
        _productRepository = productRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<ProductWithSubscriptionDto>> Handle(CreateProductWithSubscriptionCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Check if product with same name already exists (tenant isolation handled by Finbuckle.MultiTenant)
            var existingProductSpec = new ProductByNameSpec(request.Name);
            var existingProduct = await _productRepository.GetBySpecAsync(existingProductSpec, cancellationToken);

            if (existingProduct != null)
            {
                return Result<ProductWithSubscriptionDto>.Failure("Product with this name already exists.");
            }

            // Create Product first
            var product = new Product
            {
                Name = request.Name,
                Description = request.Description,
                Version = request.Version ?? "1.0.0",
                IsActive = request.IsActive
            };

            var createdProduct = await _productRepository.AddAsync(product, cancellationToken);

            // Create Subscription for the product
            var subscription = new Subscription
            {
                ProductId = createdProduct.Id,
                SubscriptionType = request.SubscriptionType ?? "standard",
                Status = "active",
                StartDate = DateTime.UtcNow,
                EndDate = request.EndDate,
                AutoRenew = request.AutoRenew,
                PricingTier = request.PricingTier,
                IsActive = true
            };

            var createdSubscription = await _subscriptionRepository.AddAsync(subscription, cancellationToken);

            // Create response DTO
            var response = new ProductWithSubscriptionDto
            {
                Product = createdProduct.Adapt<ProductDto>(),
                Subscription = createdSubscription.Adapt<SubscriptionDto>()
            };

            return Result<ProductWithSubscriptionDto>.Success(response);
        }
        catch (Exception ex)
        {
            return Result<ProductWithSubscriptionDto>.Failure($"Failed to create product with subscription: {ex.Message}");
        }
    }
}
