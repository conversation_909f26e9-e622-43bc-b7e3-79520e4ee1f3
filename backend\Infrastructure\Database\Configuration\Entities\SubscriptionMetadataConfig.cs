using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for SubscriptionMetadata entity
/// </summary>
public class SubscriptionMetadataConfig : IEntityTypeConfiguration<SubscriptionMetadata>
{
    public void Configure(EntityTypeBuilder<SubscriptionMetadata> builder)
    {
        builder.ToTable("SubscriptionMetadata", "Genp");

        // Properties
        builder.Property(e => e.SubscriptionId)
            .IsRequired();

        builder.Property(e => e.MetadataId)
            .IsRequired();

        builder.Property(e => e.IsUnique)
            .HasDefaultValue(false);

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        // Indexes
        builder.HasIndex(e => e.SubscriptionId)
            .HasDatabaseName("IX_SubscriptionMetadata_SubscriptionId");

        builder.HasIndex(e => e.MetadataId)
            .HasDatabaseName("IX_SubscriptionMetadata_MetadataId");

        builder.HasIndex(e => e.IsUnique)
            .HasDatabaseName("IX_SubscriptionMetadata_IsUnique");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_SubscriptionMetadata_IsActive")
            .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

        // Unique constraint
        builder.HasIndex(e => new { e.SubscriptionId, e.MetadataId })
            .IsUnique()
            .HasDatabaseName("IX_SubscriptionMetadata_SubscriptionId_MetadataId");

        // Multi-tenancy
        builder.IsMultiTenant();

        // Relationships
        builder.HasOne(e => e.Subscription)
            .WithMany(e => e.SubscriptionMetadata)
            .HasForeignKey(e => e.SubscriptionId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.Metadata)
            .WithMany(e => e.SubscriptionMetadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.SubscriptionValues)
            .WithOne(e => e.SubscriptionMetadata)
            .HasForeignKey(e => e.SubscriptionMetadataId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
