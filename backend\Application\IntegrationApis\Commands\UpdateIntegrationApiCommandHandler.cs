using Application.IntegrationApis.DTOs;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.IntegrationApis.Commands;

/// <summary>
/// Update integration API command handler
/// </summary>
public class UpdateIntegrationApiCommandHandler : IRequestHandler<UpdateIntegrationApiCommand, Result<ViewIntegrationApiDto>>
{
    private readonly IRepositoryWithEvents<IntegrationApi> _integrationApiRepository;
    private readonly IReadRepository<Product> _productRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpdateIntegrationApiCommandHandler(
        IRepositoryWithEvents<IntegrationApi> integrationApiRepository,
        IReadRepository<Product> productRepository)
    {
        _integrationApiRepository = integrationApiRepository;
        _productRepository = productRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<ViewIntegrationApiDto>> Handle(UpdateIntegrationApiCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Get existing integration API
            var integrationApi = await _integrationApiRepository.GetByIdAsync(request.Id, cancellationToken);
            if (integrationApi == null || integrationApi.IsDeleted)
            {
                return Result<ViewIntegrationApiDto>.Failure($"Integration API with ID {request.Id} not found.");
            }

            // Validate product exists
            var product = await _productRepository.GetByIdAsync(request.ProductId, cancellationToken);
            if (product == null)
            {
                return Result<ViewIntegrationApiDto>.Failure($"Product with ID {request.ProductId} not found.");
            }

            // Check if integration API with same name already exists for this product (excluding current)
            var existingApis = await _integrationApiRepository.ListAsync(cancellationToken);
            var existingApi = existingApis.FirstOrDefault(ia =>
                ia.ProductId == request.ProductId &&
                ia.Name == request.Name &&
                ia.Id != request.Id &&
                !ia.IsDeleted);

            if (existingApi != null)
            {
                return Result<ViewIntegrationApiDto>.Failure($"Integration API with name '{request.Name}' already exists for this product.");
            }

            // Update integration API
            integrationApi.ProductId = request.ProductId;
            integrationApi.Name = request.Name;
            integrationApi.EndpointUrl = request.EndpointUrl;
            integrationApi.Schema = request.Schema;
            integrationApi.IsActive = request.IsActive;
            integrationApi.ModifiedAt = DateTime.UtcNow;

            await _integrationApiRepository.UpdateAsync(integrationApi, cancellationToken);

            // Load with product for DTO mapping
            var productForDto = await _productRepository.GetByIdAsync(integrationApi.ProductId, cancellationToken);

            var integrationApiDto = integrationApi.Adapt<ViewIntegrationApiDto>();
            integrationApiDto.ProductName = productForDto?.Name ?? string.Empty;

            return Result<ViewIntegrationApiDto>.Success(integrationApiDto);
        }
        catch (Exception ex)
        {
            return Result<ViewIntegrationApiDto>.Failure($"Failed to update integration API: {ex.Message}");
        }
    }
}
