using Application.FeatureValues.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.FeatureValues.Commands;

/// <summary>
/// Update FeatureValue command
/// </summary>
public class UpdateFeatureValueCommand : IRequest<Result<FeatureValueDto>>
{
    /// <summary>
    /// FeatureValue ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Feature metadata ID
    /// </summary>
    public Guid FeatureMetadataId { get; set; }

    /// <summary>
    /// Reference ID for grouping related values
    /// </summary>
    public Guid? RefId { get; set; }

    /// <summary>
    /// Parent feature value ID for hierarchical structure
    /// </summary>
    public Guid? ParentFeatureValueId { get; set; }

    /// <summary>
    /// The actual value as text
    /// </summary>
    public string? Value { get; set; }
}
