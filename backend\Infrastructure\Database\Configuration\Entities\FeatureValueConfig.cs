using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for FeatureValue entity
/// </summary>
public class FeatureValueConfig : IEntityTypeConfiguration<FeatureValue>
{
    public void Configure(EntityTypeBuilder<FeatureValue> builder)
    {
        builder.ToTable("FeatureValues", "Genp");

        // Primary key
        builder.HasKey(e => e.Id);

        // Properties
        builder.Property(e => e.FeatureMetadataId)
            .IsRequired();

        builder.Property(e => e.RefId);

        builder.Property(e => e.Value)
            .HasColumnType("TEXT");

        builder.Property(e => e.CreatedAt)
            .HasDefaultValueSql("NOW()")
            .IsRequired();

        builder.Property(e => e.CreatedBy);

        builder.Property(e => e.ModifiedAt)
            .HasDefaultValueSql("NOW()")
            .IsRequired();

        builder.Property(e => e.ModifiedBy);

        // Indexes
        builder.HasIndex(e => e.FeatureMetadataId)
            .HasDatabaseName("IX_FeatureValues_FeatureMetadataId");

        builder.HasIndex(e => e.RefId)
            .HasDatabaseName("IX_FeatureValues_RefId");

        builder.HasIndex(e => e.CreatedAt)
            .HasDatabaseName("IX_FeatureValues_CreatedAt");

        // Multi-tenancy
        builder.IsMultiTenant();

        // Relationships
        builder.HasOne(e => e.FeatureMetadata)
            .WithMany(e => e.FeatureValues)
            .HasForeignKey(e => e.FeatureMetadataId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
