using Application.MetadataManagement.Commands;
using Application.MetadataManagement.DTOs;
using Application.MetadataManagement.Queries;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;

namespace Web.Host.Controllers;

/// <summary>
/// Metadata controller
/// </summary>
[Route("api/[controller]")]
public class MetadataController : BaseApiController
{
    /// <summary>
    /// Get all metadata with pagination
    /// </summary>
    [HttpGet]
    [TenantIdHeader]
    public async Task<ActionResult<PaginatedResult<MetadataDto>>> GetMetadata([FromQuery] GetMetadataQuery query)
    {
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get metadata by ID
    /// </summary>
    [HttpGet("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<MetadataDto>>> GetMetadataById(Guid id)
    {
        return Ok(await Mediator.Send(new GetMetadataByIdQuery(id)));
    }

    /// <summary>
    /// Create new metadata
    /// </summary>
    [HttpPost]
    [TenantIdHeader]
    public async Task<ActionResult<Result<MetadataDto>>> CreateMetadata(CreateMetadataCommand command)
    {
        return Ok(await Mediator.Send(command));
    }

    /// <summary>
    /// Update existing metadata
    /// </summary>
    [HttpPut("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<MetadataDto>>> UpdateMetadata(Guid id, UpdateMetadataCommand command)
    {
        command.Id = id;
        return Ok(await Mediator.Send(command));
    }

    /// <summary>
    /// Delete metadata
    /// </summary>
    [HttpDelete("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<bool>>> DeleteMetadata(Guid id)
    {
        return Ok(await Mediator.Send(new DeleteMetadataCommand(id)));
    }
}
