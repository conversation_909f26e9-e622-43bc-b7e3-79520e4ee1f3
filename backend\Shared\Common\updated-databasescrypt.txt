-- =====================================================
-- REAL ESTATE MANAGEMENT SYSTEM - SCHEMA CREATION
-- Products, Features, Objects, Roles and Metadata Tables
-- =====================================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- CORE ENTITY TABLES
-- =====================================================

-- Customers table
CREATE TABLE IF NOT EXISTS customers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    company_name VARCHAR(255),
    subscription_status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- Customer product subscriptions
CREATE TABLE IF NOT EXISTS c_product_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    blueprint_product_id UUID NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    product_prefix VARCHAR(50) NOT NULL,
    subscribed_version VARCHAR(50) NOT NULL,
    current_version VARCHAR(50) NOT NULL,
    subscription_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expiry_date TIMESTAMP,
    blueprint_server_host VARCHAR(255),
    blueprint_database_name VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(customer_id, blueprint_product_id)
);

-- Customer Products (Real Estate Projects)
CREATE TABLE IF NOT EXISTS c_products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    subscription_id UUID NOT NULL REFERENCES c_product_subscriptions(id) ON DELETE CASCADE,
    blueprint_product_id UUID NOT NULL,
    blueprint_name VARCHAR(255) NOT NULL,
    blueprint_prefix VARCHAR(50) NOT NULL,
    blueprint_version VARCHAR(50) NOT NULL,
    name VARCHAR(255) NOT NULL,
    prefix VARCHAR(50) NOT NULL,
    version VARCHAR(50) NOT NULL,
    show_sequence INTEGER DEFAULT 0,
    value TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(customer_id, blueprint_product_id)
);

-- Customer Features (Development Types)
CREATE TABLE IF NOT EXISTS c_features (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    c_product_id UUID NOT NULL REFERENCES c_products(id) ON DELETE CASCADE,
    blueprint_feature_id UUID NOT NULL,
    blueprint_name VARCHAR(255) NOT NULL,
    blueprint_prefix VARCHAR(50) NOT NULL,
    blueprint_product_version VARCHAR(50),
    name VARCHAR(255) NOT NULL,
    prefix VARCHAR(50) NOT NULL,
    product_version VARCHAR(50),
    show_sequence INTEGER DEFAULT 0,
    value TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

);

-- Customer Objects (Buildings, Towers, Floors, Units)
CREATE TABLE IF NOT EXISTS c_objects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    c_feature_id UUID NOT NULL REFERENCES c_features(id) ON DELETE CASCADE,
    parent_c_object_id UUID REFERENCES c_objects(id) ON DELETE CASCADE,
    blueprint_object_id UUID NOT NULL,
    blueprint_parent_object_id UUID,
    blueprint_name VARCHAR(255) NOT NULL,
    blueprint_prefix VARCHAR(50) NOT NULL,
    blueprint_product_version VARCHAR(50),
    name VARCHAR(255) NOT NULL,
    prefix VARCHAR(50) NOT NULL,
    product_version VARCHAR(50),
    show_sequence INTEGER DEFAULT 0,
    value TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

);

-- Customer Roles (Access Control)
CREATE TABLE IF NOT EXISTS c_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    c_product_id UUID NOT NULL REFERENCES c_products(id) ON DELETE CASCADE,
    blueprint_role_id UUID,
    blueprint_name VARCHAR(255),
    blueprint_prefix VARCHAR(50),
    blueprint_product_version VARCHAR(50),
    blueprint_is_default BOOLEAN DEFAULT FALSE,
    name VARCHAR(255) NOT NULL,
    prefix VARCHAR(50) NOT NULL,
    product_version VARCHAR(50),
    is_default BOOLEAN DEFAULT FALSE,
    show_sequence INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_custom BOOLEAN DEFAULT FALSE,
    description TEXT,
    value TEXT,
    UNIQUE(customer_id, c_product_id, prefix)
);

-- Customer Statuses (Available, Booked, Sold, etc.)
CREATE TABLE IF NOT EXISTS c_statuses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    c_product_id UUID NOT NULL REFERENCES c_products(id) ON DELETE CASCADE,
    blueprint_status_id UUID NOT NULL,
    blueprint_name VARCHAR(255) NOT NULL,
    blueprint_prefix VARCHAR(50) NOT NULL,
    name VARCHAR(255) NOT NULL,
    prefix VARCHAR(50) NOT NULL,
    show_sequence INTEGER DEFAULT 0,
    value TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

);

-- Customer Actions (View, Book, Sell, etc.)
CREATE TABLE IF NOT EXISTS c_actions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    c_product_id UUID NOT NULL REFERENCES c_products(id) ON DELETE CASCADE,
    blueprint_action_id UUID NOT NULL,
    blueprint_name VARCHAR(255) NOT NULL,
    blueprint_prefix VARCHAR(50) NOT NULL,
    name VARCHAR(255) NOT NULL,
    prefix VARCHAR(50) NOT NULL,
    show_sequence INTEGER DEFAULT 0,
    value TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

);

-- Customer Permissions (Read, Write, Delete, etc.)
CREATE TABLE IF NOT EXISTS c_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    c_product_id UUID NOT NULL REFERENCES c_products(id) ON DELETE CASCADE,
    blueprint_permission_id UUID NOT NULL,
    blueprint_name VARCHAR(255) NOT NULL,
    blueprint_prefix VARCHAR(50) NOT NULL,
    name VARCHAR(255) NOT NULL,
    prefix VARCHAR(50) NOT NULL,
    show_sequence INTEGER DEFAULT 0,
    value TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

);

-- =====================================================
-- METADATA TABLES
-- =====================================================

-- Customer Product Metadata
CREATE TABLE IF NOT EXISTS c_product_metadata (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    c_product_id UUID NOT NULL REFERENCES c_products(id) ON DELETE CASCADE,
    blueprint_metadata_id UUID,
    key VARCHAR(255) NOT NULL,
    value JSONB,
    data_type VARCHAR(50) DEFAULT 'string',
    product_version VARCHAR(50),
    show_sequence INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(customer_id, c_product_id, key)
);

-- Customer Feature Metadata
CREATE TABLE IF NOT EXISTS c_feature_metadata (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    c_feature_id UUID NOT NULL REFERENCES c_features(id) ON DELETE CASCADE,
    blueprint_metadata_id UUID,
    key VARCHAR(255) NOT NULL,
    value JSONB,
    data_type VARCHAR(50) DEFAULT 'string',
    product_version VARCHAR(50),
    show_sequence INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(customer_id, c_feature_id, key)
);

-- Customer Object Metadata
CREATE TABLE IF NOT EXISTS c_object_metadata (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    c_object_id UUID NOT NULL REFERENCES c_objects(id) ON DELETE CASCADE,
    blueprint_metadata_id UUID,
    key VARCHAR(255) NOT NULL,
    value JSONB,
    data_type VARCHAR(50) DEFAULT 'string',
    product_version VARCHAR(50),
    show_sequence INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(customer_id, c_object_id, key)
);

-- Customer Role Metadata
CREATE TABLE IF NOT EXISTS c_role_metadata (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    c_role_id UUID NOT NULL REFERENCES c_roles(id) ON DELETE CASCADE,
    blueprint_metadata_id UUID,
    key VARCHAR(255) NOT NULL,
    value JSONB,
    data_type VARCHAR(50) DEFAULT 'string',
    product_version VARCHAR(50),
    show_sequence INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
