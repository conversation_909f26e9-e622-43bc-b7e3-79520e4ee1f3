using Application.MultiTenancy.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.MultiTenancy.Queries;

/// <summary>
/// Get tenant with product information query
/// </summary>
public class GetTenantWithProductQuery : IRequest<Result<TenantWithProductDto>>
{
    /// <summary>
    /// Tenant ID
    /// </summary>
    public string TenantId { get; set; } = default!;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetTenantWithProductQuery(string tenantId)
    {
        TenantId = tenantId;
    }
}
