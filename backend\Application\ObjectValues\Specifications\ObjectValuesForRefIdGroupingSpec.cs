using Ardalis.Specification;
using Domain.Entities;

namespace Application.ObjectValues.Specifications;

/// <summary>
/// Specification to get ObjectValues for RefId grouping by ObjectId
/// </summary>
public class ObjectValuesForRefIdGroupingSpec : Specification<ObjectValue>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ObjectValuesForRefIdGroupingSpec(
        Guid objectId,
        string tenantId,
        string? searchTerm = null,
        bool onlyActive = true)
    {
        Query.Where(ov => ov.ObjectMetadata.ObjectId == objectId && !ov.IsDeleted);

        // Include related data needed for grouping
        Query.Include(ov => ov.ObjectMetadata)
             .ThenInclude(om => om.Object);

        Query.Include(ov => ov.ObjectMetadata)
             .ThenInclude(om => om.Metadata);

        // Active filters (tenant isolation handled by multi-tenant framework)
        Query.Where(ov => !ov.ObjectMetadata.Object.IsDeleted &&
                         ov.ObjectMetadata.IsActive &&
                         !ov.ObjectMetadata.IsDeleted);

        // Include only active objects if requested
        if (onlyActive)
        {
            Query.Where(ov => ov.ObjectMetadata.Object.IsActive);
        }

        // Apply search filter if provided
        if (!string.IsNullOrEmpty(searchTerm))
        {
            Query.Where(ov => (ov.Value != null && ov.Value.Contains(searchTerm)) ||
                             ov.ObjectMetadata.Metadata.MetadataKey.Contains(searchTerm) ||
                             (ov.ObjectMetadata.Metadata.DisplayLabel != null &&
                              ov.ObjectMetadata.Metadata.DisplayLabel.Contains(searchTerm)));
        }

        // Order by modification date for grouping
        Query.OrderByDescending(ov => ov.CreatedAt);
    }
}
