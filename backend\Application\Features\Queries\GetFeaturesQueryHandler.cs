using Application.Features.DTOs;
using Application.Features.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.Features.Queries;

/// <summary>
/// Get features query handler
/// </summary>
public class GetFeaturesQueryHandler : IRequestHandler<GetFeaturesQuery, PaginatedResult<FeatureDto>>
{
    private readonly IReadRepository<Feature> _featureRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetFeaturesQueryHandler(IReadRepository<Feature> featureRepository)
    {
        _featureRepository = featureRepository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<PaginatedResult<FeatureDto>> Handle(GetFeaturesQuery request, CancellationToken cancellationToken)
    {
        // Calculate pagination
        var skip = (request.PageNumber - 1) * request.PageSize;

        // Create specifications for data and count
        var dataSpec = new FeaturesWithFiltersSpec(
            request.SearchTerm,
            request.IsActive,
            request.ProductId,
            skip,
            request.PageSize);

        var countSpec = new FeaturesCountSpec(
            request.SearchTerm,
            request.IsActive,
            request.ProductId);

        // Get data and count using specifications (tenant isolation handled by Finbuckle.MultiTenant)
        var items = await _featureRepository.ListAsync(dataSpec, cancellationToken);
        var totalCount = await _featureRepository.CountAsync(countSpec, cancellationToken);

        var dtos = items.Adapt<List<FeatureDto>>();

        return new PaginatedResult<FeatureDto>(dtos, request.PageNumber, request.PageSize, totalCount);
    }
}
