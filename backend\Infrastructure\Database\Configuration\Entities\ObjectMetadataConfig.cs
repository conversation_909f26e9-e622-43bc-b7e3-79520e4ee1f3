using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for ObjectMetadata entity
/// </summary>
public class ObjectMetadataConfig : IEntityTypeConfiguration<ObjectMetadata>
{
    public void Configure(EntityTypeBuilder<ObjectMetadata> builder)
    {

        builder.ToTable("ObjectMetadata", "Genp");

        // Properties
        builder.Property(e => e.ObjectId)
            .IsRequired();

        builder.Property(e => e.MetadataId)
            .IsRequired();

        builder.Property(e => e.IsUnique)
            .HasDefaultValue(false);

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        // Indexes
        builder.HasIndex(e => e.ObjectId)
            .HasDatabaseName("IX_ObjectMetadata_ObjectId");

        builder.HasIndex(e => e.MetadataId)
            .HasDatabaseName("IX_ObjectMetadata_MetadataId");

        builder.HasIndex(e => e.IsUnique)
            .HasDatabaseName("IX_ObjectMetadata_IsUnique");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_ObjectMetadata_IsActive")
            .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

        // Unique constraint
        builder.HasIndex(e => new { e.ObjectId, e.MetadataId })
            .IsUnique()
            .HasDatabaseName("IX_ObjectMetadata_ObjectId_MetadataId");

        // Multi-tenancy
        builder.IsMultiTenant();

        // Relationships
        builder.HasOne(e => e.Object)
            .WithMany(e => e.ObjectMetadata)
            .HasForeignKey(e => e.ObjectId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.Metadata)
            .WithMany(e => e.ObjectMetadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.ObjectValues)
            .WithOne(e => e.ObjectMetadata)
            .HasForeignKey(e => e.ObjectMetadataId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
