namespace Application.SyncHistories.DTOs;

/// <summary>
/// Create Sync History DTO
/// </summary>
public class CreateSyncHistoryDto
{
    /// <summary>
    /// Integration ID this sync history belongs to
    /// </summary>
    public Guid IntegrationId { get; set; }

    /// <summary>
    /// Object ID this sync operation was performed on
    /// </summary>
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Number of records processed in this sync operation
    /// </summary>
    public int RecordCount { get; set; }

    /// <summary>
    /// Sync operation status (e.g., "Success", "Failed", "Partial", "InProgress")
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// When the sync operation started
    /// </summary>
    public DateTime StartedAt { get; set; }

    /// <summary>
    /// When the sync operation completed (null if still in progress)
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// Error log details if the sync operation failed
    /// </summary>
    public string? ErrorLog { get; set; }
}
