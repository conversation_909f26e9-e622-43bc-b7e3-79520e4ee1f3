using Application.Products.DTOs;
using Application.Products.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.Products.Commands;

/// <summary>
/// Update product command handler
/// </summary>
public class UpdateProductCommandHandler : IRequestHandler<UpdateProductCommand, Result<ProductDto>>
{
    private readonly IRepository<Product> _productRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpdateProductCommandHandler(IRepository<Product> productRepository)
    {
        _productRepository = productRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<ProductDto>> Handle(UpdateProductCommand request, CancellationToken cancellationToken)
    {
        // Get product using specification (tenant isolation handled by Finbuckle.MultiTenant)
        var productSpec = new ProductByIdSpec(request.Id);
        var product = await _productRepository.GetBySpecAsync(productSpec, cancellationToken);

        if (product == null)
        {
            return Result<ProductDto>.Failure("Product not found.");
        }

        // Update product properties
        product.Name = request.Name;
        product.Description = request.Description;
        product.Version = request.Version;
        product.IsActive = request.IsActive;
        product.IsUserImported = request.IsUserImported;
        product.IsRoleAssigned = request.IsRoleAssigned;
        product.ApiKey = request.ApiKey;
        product.IsOnboardCompleted = request.IsOnboardCompleted;
        product.ApplicationUrl = request.ApplicationUrl;

        await _productRepository.UpdateAsync(product, cancellationToken);

        var dto = product.Adapt<ProductDto>();
        return Result<ProductDto>.Success(dto);
    }
}
