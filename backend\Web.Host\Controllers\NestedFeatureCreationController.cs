using Application.Features.Commands.CreateFeatureWithNestedData;
using Application.Features.Commands.UpsertObjectWithMetadata;
using Application.Features.DTOs;
using DocumentFormat.OpenXml.Bibliography;
using Infrastructure.OpenApi;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;

namespace Web.Host.Controllers;

/// <summary>
/// API Controller for creating Features with deeply nested data structures
/// </summary>
[ApiController]
[Route("api/[controller]")]
[AllowAnonymous]
public class NestedFeatureCreationController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<NestedFeatureCreationController> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public NestedFeatureCreationController(
        IMediator mediator,
        ILogger<NestedFeatureCreationController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Create a Feature with deeply nested objects and metadata from JSON payload
    /// </summary>
    /// <param name="request">Feature creation request with nested data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Creation result with all created entities</returns>
    /// <remarks>
    /// This endpoint accepts a complex JSON payload containing:
    /// - Feature information (featureId, productId, name, description, etc.)
    /// - Nested objects with their own metadata and values
    /// - Child objects with metadata and values
    /// - Automatic data type inference for metadata fields
    /// - Atomic transaction processing for data integrity
    /// 
    /// Sample request:
    /// ```json
    /// {
    ///   "feature": {
    ///     "featureId": null,
    ///     "productId": "d2c34b0a-7db3-4e9e-aecf-6d3f8e85c678",
    ///     "name": "Site Visit Feature",
    ///     "description": "Tracks visits by field executives.",
    ///     "isDefault": false,
    ///     "isActive": true,
    ///     "metaJson": {},
    ///     "values": {},
    ///     "objects": [
    ///       {
    ///         "name": "Visit Record",
    ///         "description": "Object holding site visit data",
    ///         "isActive": true,
    ///         "metaJson": {
    ///           "type": 2,
    ///           "isDone": true,
    ///           "longitude": 77.6454067,
    ///           "latitude": 12.9209968,
    ///           "executiveName": "Abhilash",
    ///           "notes": "site visit done"
    ///         },
    ///         "MetaValues": [
    ///           {
    ///             "type": 1,
    ///             "isDone": true,
    ///             "longitude": 77.6454076,
    ///             "latitude": 12.9210115,
    ///             "executiveName": "Abhilash",
    ///             "notes": "meeting done with Ekta"
    ///           }
    ///         ],
    ///         "childObjects": [
    ///           {
    ///             "name": "Visit Photo",
    ///             "description": "Photos taken during site visits",
    ///             "isActive": true,
    ///             "metaJson": {
    ///               "photoId": "photo-001",
    ///               "url": "https://example.com/photos/visit1.jpg",
    ///               "timestamp": "2025-01-06T13:30:00Z"
    ///             },
    ///             "MetaValues": [
    ///               {
    ///                 "photoId": "photo-001",
    ///                 "isApproved": true,
    ///                 "uploadedBy": "bf4d9895-2580-4ddd-b370-189e6437dea4"
    ///               }
    ///             ]
    ///           }
    ///         ]
    ///       }
    ///     ]
    ///   }
    /// }
    /// ```
    /// 
    /// Features:
    /// - Automatic data type inference based on field names and values
    /// - Support for hierarchical object structures (parent-child relationships)
    /// - Atomic transaction processing ensures data consistency
    /// - Automatic metadata creation for unknown fields
    /// - Multi-tenant data isolation
    /// - Comprehensive error handling and validation
    /// - Detailed response with creation statistics
    /// 
    /// Response includes:
    /// - Created feature information
    /// - List of created objects with their IDs
    /// - Statistics on metadata and values created
    /// - Processing summary with timing and warnings
    /// - Data type mappings used during creation
    /// </remarks>
    [HttpPost]
    [ProducesResponseType(typeof(Result<CreateFeatureWithNestedDataResponseDto>), 200)]
    [ProducesResponseType(typeof(Result<CreateFeatureWithNestedDataResponseDto>), 400)]
    [ProducesResponseType(typeof(Result<CreateFeatureWithNestedDataResponseDto>), 500)]
    [TenantIdHeader]
    public async Task<IActionResult> CreateFeatureWithNestedData(
        [FromBody] CreateFeatureWithNestedDataRequestDto request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Received request to create feature with nested data: {FeatureName}", 
                request.Feature?.Name ?? "Unknown");

            if (request?.Feature == null)
            {
                return BadRequest(Result<CreateFeatureWithNestedDataResponseDto>.Failure("Feature data is required"));
            }

            // Validate required fields
            if (string.IsNullOrWhiteSpace(request.Feature.Name))
            {
                return BadRequest(Result<CreateFeatureWithNestedDataResponseDto>.Failure("Feature name is required"));
            }

            if (request.Feature.ProductId == Guid.Empty)
            {
                return BadRequest(Result<CreateFeatureWithNestedDataResponseDto>.Failure("Product ID is required"));
            }

            // Create command and execute
            var command = CreateFeatureWithNestedDataCommand.FromDto(request);
            var result = await _mediator.Send(command, cancellationToken);

            if (!result.Succeeded)
            {
                _logger.LogWarning("Failed to create feature with nested data: {Error}", result.Message);
                return BadRequest(result);
            }

            _logger.LogInformation("Successfully created feature with nested data: {FeatureId}, Processing time: {ProcessingTime}ms",
                result.Data!.Feature.Id, result.Data.ProcessingSummary.ProcessingTimeMs);

            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid request data for feature creation");
            return BadRequest(Result<CreateFeatureWithNestedDataResponseDto>.Failure($"Invalid request: {ex.Message}"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error creating feature with nested data");
            return StatusCode(500, Result<CreateFeatureWithNestedDataResponseDto>.Failure("An unexpected error occurred while processing the request"));
        }
    }

    /// <summary>
    /// Upsert an object with metadata and values
    /// </summary>
    /// <param name="request">Object upsert request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Upsert result with object information and statistics</returns>
    /// <remarks>
    /// This endpoint allows you to create or update an object with its metadata and values.
    ///
    /// **Create Mode (when Id is null):**
    /// - Creates a new object with the provided name, description, and metadata
    /// - FeatureId is required for new objects
    /// - Automatically creates metadata for fields in MetaJson
    /// - Creates object values from MetaValues array
    ///
    /// **Update Mode (when Id is provided):**
    /// - Updates the existing object's name, description, and active status
    /// - Adds new metadata fields from MetaJson (existing metadata is preserved)
    /// - Adds new values from MetaValues array (existing values are preserved)
    ///
    /// Sample request for **creating** a new object:
    /// ```json
    /// {
    ///   "object": {
    ///     "id": null,
    ///     "featureId": "d2c34b0a-7db3-4e9e-aecf-6d3f8e85c678",
    ///     "name": "Deal",
    ///     "description": "Holds customer and deal data",
    ///     "isActive": true,
    ///     "metaJson": {
    ///       "customerName": "John Doe",
    ///       "dealAmount": 50000.00,
    ///       "dealDate": "2025-01-06",
    ///       "isActive": true,
    ///       "customerEmail": "<EMAIL>"
    ///     },
    ///     "metaValues": [
    ///       {
    ///         "customerName": "Jane Smith",
    ///         "dealAmount": 75000.00,
    ///         "dealDate": "2025-01-07",
    ///         "isActive": true,
    ///         "customerEmail": "<EMAIL>"
    ///       }
    ///     ]
    ///   }
    /// }
    /// ```
    ///
    /// Sample request for **updating** an existing object:
    /// ```json
    /// {
    ///   "object": {
    ///     "id": "existing-object-guid",
    ///     "name": "Updated Deal Name",
    ///     "description": "Updated description for deal data",
    ///     "isActive": true,
    ///     "metaJson": {
    ///       "newField": "New metadata field",
    ///       "priority": "High"
    ///     },
    ///     "metaValues": [
    ///       {
    ///         "customerName": "New Customer",
    ///         "dealAmount": 100000.00,
    ///         "newField": "New field value",
    ///         "priority": "High"
    ///       }
    ///     ]
    ///   }
    /// }
    /// ```
    /// </remarks>
    [HttpPost("upsert-object")]
    [AllowAnonymous]
    [TenantIdHeader]
    public async Task<IActionResult> UpsertObjectWithMetadata(
        [FromBody] UpsertObjectWithMetadataRequestDto request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Received request to upsert object: {ObjectName}, ObjectId: {ObjectId}",
                request.Object?.Name ?? "Unknown", request.Object?.Id);

            if (request?.Object == null)
            {
                return BadRequest(Result<UpsertObjectWithMetadataResponseDto>.Failure("Object data is required"));
            }

            // Validate required fields
            if (request?.Object == null && string.IsNullOrWhiteSpace(request?.Object.Name))
            {
                return BadRequest(Result<UpsertObjectWithMetadataResponseDto>.Failure("Object name is required"));
            }

            // For new objects, FeatureId is required
            if (!request.Object.Id.HasValue && !request.Object.FeatureId.HasValue)
            {
                return BadRequest(Result<UpsertObjectWithMetadataResponseDto>.Failure("FeatureId is required when creating a new object"));
            }

            // Create command and execute
            var command = UpsertObjectWithMetadataCommand.FromDto(request);
            var result = await _mediator.Send(command, cancellationToken);

            if (!result.Succeeded)
            {
                _logger.LogWarning("Failed to upsert object: {Error}", result.Message);
                return BadRequest(result);
            }

            _logger.LogInformation("Successfully upserted object: {ObjectId}, Processing time: {ProcessingTime}ms",
                result.Data!.Object.Id, result.Data.ProcessingSummary.ProcessingTimeMs);

            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid request data for object upsert");
            return BadRequest(Result<UpsertObjectWithMetadataResponseDto>.Failure($"Invalid request: {ex.Message}"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error upserting object");
            return StatusCode(500, Result<UpsertObjectWithMetadataResponseDto>.Failure("An unexpected error occurred while processing the request"));
        }
    }
}
