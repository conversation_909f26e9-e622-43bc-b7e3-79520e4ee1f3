using Application.ObjectValues.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.ObjectValues.Commands.UpsertObjectValues;

/// <summary>
/// Command to upsert ObjectValues for a specific RefId
/// </summary>
public class UpsertObjectValuesCommand : IRequest<Result<UpsertObjectValuesResponseDto>>
{
    /// <summary>
    /// Reference ID that groups related ObjectValues
    /// </summary>
    public Guid RefId { get; set; }
    
    /// <summary>
    /// Object ID that these values belong to
    /// </summary>
    public Guid ObjectId { get; set; }
    
    /// <summary>
    /// Dictionary of values keyed by MetadataKey
    /// </summary>
    public Dictionary<string, string> Values { get; set; } = new();
    
    /// <summary>
    /// Whether to create missing metadata automatically
    /// </summary>
    public bool AutoCreateMetadata { get; set; } = true;
    
    /// <summary>
    /// Whether to validate data types strictly
    /// </summary>
    public bool StrictValidation { get; set; } = true;
    
    /// <summary>
    /// Whether to skip validation for performance
    /// </summary>
    public bool SkipValidation { get; set; } = false;
    
    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="refId">Reference ID</param>
    /// <param name="objectId">Object ID</param>
    /// <param name="values">Values dictionary</param>
    /// <param name="autoCreateMetadata">Auto-create missing metadata</param>
    /// <param name="strictValidation">Apply strict validation</param>
    /// <param name="skipValidation">Skip validation entirely</param>
    public UpsertObjectValuesCommand(
        Guid refId,
        Guid objectId,
        Dictionary<string, string> values,
        bool autoCreateMetadata = true,
        bool strictValidation = true,
        bool skipValidation = false)
    {
        RefId = refId;
        ObjectId = objectId;
        Values = values ?? new Dictionary<string, string>();
        AutoCreateMetadata = autoCreateMetadata;
        StrictValidation = strictValidation;
        SkipValidation = skipValidation;
    }
    
    /// <summary>
    /// Create command from DTO
    /// </summary>
    /// <param name="request">Upsert request DTO</param>
    /// <returns>Command instance</returns>
    public static UpsertObjectValuesCommand FromDto(UpsertObjectValuesRequestDto request)
    {
        return new UpsertObjectValuesCommand(
            request.RefId,
            request.ObjectId,
            request.Values,
            request.AutoCreateMetadata,
            request.StrictValidation);
    }
}
