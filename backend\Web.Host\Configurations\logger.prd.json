{"Serilog": {"MinimumLevel": {"Default": "Warning", "Override": {"Microsoft": "Error", "Microsoft.Hosting.Lifetime": "Warning", "Microsoft.EntityFrameworkCore": "Error"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "Logs/prd-log-.txt", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj}{NewLine}{Exception}", "retainedFileCountLimit": 30}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}}