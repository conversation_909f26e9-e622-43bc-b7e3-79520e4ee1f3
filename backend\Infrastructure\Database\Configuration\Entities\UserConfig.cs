using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for User entity
/// </summary>
public class UserConfig : IEntityTypeConfiguration<User>
{
    public void Configure(EntityTypeBuilder<User> builder)
    {
        builder.ToTable("Users", "Genp");

        builder.IsMultiTenant();
        // Primary key
        builder.HasKey(e => e.Id);

        // Properties
        builder.Property(e => e.FirstName)
            .HasMaxLength(128);

        builder.Property(e => e.LastName)
            .HasMaxLength(128);

        builder.Property(e => e.ExternalUserId)
            .HasMaxLength(255);

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        builder.Property(e => e.CreatedAt)
            .HasDefaultValueSql("NOW()")
            .IsRequired();

        builder.Property(e => e.CreatedBy);

        builder.Property(e => e.ModifiedAt)
            .HasDefaultValueSql("NOW()")
            .IsRequired();

        builder.Property(e => e.ModifiedBy);

        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false);

        // Indexes
        builder.HasIndex(e => e.ExternalUserId)
            .HasDatabaseName("IX_Users_ExternalUserId");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_Users_IsActive")
            .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

        builder.HasIndex(e => e.LastLoginAt)
            .HasDatabaseName("IX_Users_LastLoginAt");

        // Query filter for soft delete
        builder.HasQueryFilter(e => !e.IsDeleted);

        // Relationships
        builder.HasMany(e => e.UserRoles)
            .WithOne(e => e.User)
            .HasForeignKey(e => e.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.UserMetadata)
            .WithOne(e => e.User)
            .HasForeignKey(e => e.UserId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
