using Application.Features.DTOs;
using Shared.Common.Response;
using System.Text.Json;

namespace Application.Features.Interfaces;

/// <summary>
/// Service for creating features with deeply nested data structures
/// </summary>
public interface INestedFeatureCreationService
{
    /// <summary>
    /// Create a feature with all nested objects, metadata, and values
    /// </summary>
    /// <param name="featureData">Feature creation data</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="userId">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Creation result with all created entities</returns>
    Task<Result<CreateFeatureWithNestedDataResponseDto>> CreateFeatureWithNestedDataAsync(
        FeatureCreationDto featureData,
        string tenantId,
        string userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Infer data type from a value and get the corresponding DataType ID
    /// </summary>
    /// <param name="value">Value to analyze</param>
    /// <param name="fieldName">Field name for context</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>DataType ID</returns>
    Task<Guid> InferDataTypeAsync(object value, string fieldName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create metadata from JSON structure
    /// </summary>
    /// <param name="metaJson">JSON metadata structure</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="userId">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of created metadata IDs</returns>
    Task<Result<List<Guid>>> CreateMetadataFromJsonAsync(
        JsonElement metaJson,
        string tenantId,
        string userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Create object values from metadata values array
    /// </summary>
    /// <param name="objectId">Object ID</param>
    /// <param name="metaValues">Array of metadata values</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="userId">User ID</param>
    /// <param name="parentObjectValueId">Parent object value ID for hierarchical structure</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of object values created</returns>
    Task<Result<int>> CreateObjectValuesFromMetaValuesAsync(
        Guid objectId,
        List<Dictionary<string, object>> metaValues,
        string tenantId,
        string userId,
        Guid? parentObjectValueId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Create feature metadata and values from feature data
    /// </summary>
    /// <param name="featureId">Feature ID</param>
    /// <param name="metaJson">Feature metadata JSON</param>
    /// <param name="values">Feature values</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="userId">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of feature metadata and values created</returns>
    Task<Result<(int MetadataCreated, int ValuesCreated)>> CreateFeatureMetadataAndValuesAsync(
        Guid featureId,
        JsonElement? metaJson,
        Dictionary<string, object> values,
        string tenantId,
        string userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate that the product exists and is accessible
    /// </summary>
    /// <param name="productId">Product ID</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Validation result</returns>
    Task<Result<bool>> ValidateProductAccessAsync(
        Guid productId,
        string tenantId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get or create metadata for a field
    /// </summary>
    /// <param name="fieldName">Field name</param>
    /// <param name="value">Field value for type inference</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="userId">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Metadata ID</returns>
    Task<Result<Guid>> GetOrCreateMetadataAsync(
        string fieldName,
        object value,
        string tenantId,
        string userId,
        CancellationToken cancellationToken = default);
}
