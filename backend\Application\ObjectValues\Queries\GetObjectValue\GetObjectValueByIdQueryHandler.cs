using Application.ObjectValues.DTOs;
using Application.ObjectValues.Specifications;
using Application.ObjectMetadataManagement.DTOs;
using Abstraction.Database.Repositories;
using Abstraction.Common;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.ObjectValues.Queries.GetObjectValue;

/// <summary>
/// Handler for GetObjectValueByIdQuery
/// </summary>
public class GetObjectValueByIdQueryHandler : IRequestHandler<GetObjectValueByIdQuery, Result<ObjectValueResponseDto>>
{
    private readonly IRepository<ObjectValue> _repository;
    private readonly ICurrentUser _currentUser;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetObjectValueByIdQueryHandler(
        IRepository<ObjectValue> repository,
        ICurrentUser currentUser)
    {
        _repository = repository;
        _currentUser = currentUser;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<ObjectValueResponseDto>> Handle(GetObjectValueByIdQuery request, CancellationToken cancellationToken)
    {
        var spec = new ObjectValueByIdWithFullDataSpec(request.Id);
        var objectValue = await _repository.GetBySpecAsync(spec, cancellationToken);

        if (objectValue == null)
        {
            return Result<ObjectValueResponseDto>.Failure($"ObjectValue with ID '{request.Id}' not found.");
        }

        var dto = MapToResponseDto(objectValue);

        if (request.IncludeChildValues)
        {
            dto.ChildValues = await GetChildValuesRecursive(objectValue.Id, cancellationToken);
        }

        return Result<ObjectValueResponseDto>.Success(dto);
    }

    /// <summary>
    /// Get child values recursively
    /// </summary>
    private async Task<List<ObjectValueResponseDto>> GetChildValuesRecursive(Guid parentValueId, CancellationToken cancellationToken)
    {
        var childSpec = new ObjectValueChildrenSpec(parentValueId);
        var childValues = await _repository.ListAsync(childSpec, cancellationToken);

        var result = new List<ObjectValueResponseDto>();

        foreach (var childValue in childValues)
        {
            var dto = MapToResponseDto(childValue);
            dto.ChildValues = await GetChildValuesRecursive(childValue.Id, cancellationToken);
            result.Add(dto);
        }

        return result;
    }

    /// <summary>
    /// Map ObjectValue entity to ObjectValueResponseDto
    /// </summary>
    private ObjectValueResponseDto MapToResponseDto(ObjectValue objectValue)
    {
        return new ObjectValueResponseDto
        {
            Id = objectValue.Id,
            RefId = objectValue.RefId,
            ParentObjectValueId = objectValue.ParentObjectValueId,
            Value = objectValue.Value,
            CreatedAt = objectValue.CreatedAt,
            ModifiedAt = objectValue.ModifiedAt,
            ObjectMetadata = new ObjectMetadataDto
            {
                Id = objectValue.ObjectMetadata.Id,
                ObjectId = objectValue.ObjectMetadata.ObjectId,
                ObjectName = objectValue.ObjectMetadata.Object.Name,
                MetadataId = objectValue.ObjectMetadata.MetadataId,
                MetadataKey = objectValue.ObjectMetadata.Metadata.MetadataKey,
                MetadataDisplayLabel = objectValue.ObjectMetadata.Metadata.DisplayLabel,
                IsUnique = objectValue.ObjectMetadata.IsUnique,
                IsActive = objectValue.ObjectMetadata.IsActive,
                ValuesCount = 0,
                CreatedAt = objectValue.ObjectMetadata.CreatedAt,
                CreatedBy = objectValue.ObjectMetadata.CreatedBy ?? Guid.Empty,
                ModifiedAt = objectValue.ObjectMetadata.ModifiedAt,
                ModifiedBy = objectValue.ObjectMetadata.ModifiedBy
            }
        };
    }
}
