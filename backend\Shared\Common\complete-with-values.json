{"operationType": 5, "includeFeatures": true, "includeObjects": true, "includeMetadata": true, "productData": [{"name": "Real Estate Management System", "description": "Complete real estate project management system with hierarchical structure", "version": "1.0.0", "isActive": true, "features": [{"name": "Property Management", "description": "Comprehensive property management with full hierarchy support", "isDefault": true, "isActive": true, "metadata": [{"metadataId": "feature-priority-level", "isUnique": false, "isActive": true}, {"metadataId": "feature-complexity-score", "isUnique": false, "isActive": true}, {"metadataId": "feature-development-status", "isUnique": false, "isActive": true}], "values": [{"metadataKey": "feature-priority-level", "value": "High", "refId": "ref-001"}, {"metadataKey": "feature-complexity-score", "value": "8.5", "refId": "ref-001"}, {"metadataKey": "feature-development-status", "value": "In Progress", "refId": "ref-001"}], "objects": [{"name": "Project", "description": "Top-level real estate project", "isActive": true, "metadata": [{"metadataId": "project-approval-status", "isUnique": false, "isActive": true}, {"metadataId": "project-investment-amount", "isUnique": false, "isActive": true}, {"metadataId": "project-regulatory-clearance", "isUnique": false, "isActive": true}], "values": [{"metadataKey": "project-approval-status", "value": "Approved", "refId": "project-001"}, {"metadataKey": "project-investment-amount", "value": "50000000", "refId": "project-001"}, {"metadataKey": "project-regulatory-clearance", "value": "Obtained", "refId": "project-001"}]}, {"name": "Phase", "description": "Project development phase", "isActive": true, "metadata": [{"metadataId": "phase-construction-timeline", "isUnique": false, "isActive": true}, {"metadataId": "phase-contractor-name", "isUnique": false, "isActive": true}, {"metadataId": "phase-quality-rating", "isUnique": false, "isActive": true}], "values": [{"metadataKey": "phase-construction-timeline", "value": "24 months", "refId": "phase-001"}, {"metadataKey": "phase-contractor-name", "value": "ABC Construction Ltd", "refId": "phase-001"}, {"metadataKey": "phase-quality-rating", "value": "A+", "refId": "phase-001"}]}, {"name": "Block", "description": "Construction block within a phase", "isActive": true, "metadata": [{"metadataId": "block-foundation-type", "isUnique": false, "isActive": true}, {"metadataId": "block-structural-design", "isUnique": false, "isActive": true}, {"metadataId": "block-safety-compliance", "isUnique": false, "isActive": true}], "values": [{"metadataKey": "block-foundation-type", "value": "Deep Foundation", "refId": "block-001"}, {"metadataKey": "block-structural-design", "value": "RCC Frame Structure", "refId": "block-001"}, {"metadataKey": "block-safety-compliance", "value": "IS 456:2000 Compliant", "refId": "block-001"}]}, {"name": "Property", "description": "Individual property within a block", "isActive": true, "metadata": [{"metadataId": "property-legal-title", "isUnique": true, "isActive": true}, {"metadataId": "property-market-value", "isUnique": false, "isActive": true}, {"metadataId": "property-amenities-score", "isUnique": false, "isActive": true}], "values": [{"metadataKey": "property-legal-title", "value": "PROP-2024-001", "refId": "property-001"}, {"metadataKey": "property-market-value", "value": "2500000", "refId": "property-001"}, {"metadataKey": "property-amenities-score", "value": "9.2", "refId": "property-001"}]}, {"name": "Tower", "description": "Building tower within a property", "isActive": true, "metadata": [{"metadataId": "tower-architectural-style", "isUnique": false, "isActive": true}, {"metadataId": "tower-fire-safety-rating", "isUnique": false, "isActive": true}, {"metadataId": "tower-energy-efficiency", "isUnique": false, "isActive": true}], "values": [{"metadataKey": "tower-architectural-style", "value": "Modern Contemporary", "refId": "tower-001"}, {"metadataKey": "tower-fire-safety-rating", "value": "Grade A", "refId": "tower-001"}, {"metadataKey": "tower-energy-efficiency", "value": "5 Star Rating", "refId": "tower-001"}]}, {"name": "Floor", "description": "Floor within a tower", "isActive": true, "metadata": [{"metadataId": "floor-ceiling-height", "isUnique": false, "isActive": true}, {"metadataId": "floor-ventilation-type", "isUnique": false, "isActive": true}, {"metadataId": "floor-accessibility-features", "isUnique": false, "isActive": true}], "values": [{"metadataKey": "floor-ceiling-height", "value": "10.5 feet", "refId": "floor-001"}, {"metadataKey": "floor-ventilation-type", "value": "Cross Ventilation", "refId": "floor-001"}, {"metadataKey": "floor-accessibility-features", "value": "Wheelchair Accessible", "refId": "floor-001"}]}, {"name": "Unit", "description": "Individual unit within a floor", "isActive": true, "metadata": [{"metadataId": "unit-registration-number", "isUnique": true, "isActive": true}, {"metadataId": "unit-interior-design", "isUnique": false, "isActive": true}, {"metadataId": "unit-smart-home-features", "isUnique": false, "isActive": true}, {"metadataId": "unit-warranty-period", "isUnique": false, "isActive": true}], "values": [{"metadataKey": "unit-registration-number", "value": "UNIT-A101-2024", "refId": "unit-001"}, {"metadataKey": "unit-interior-design", "value": "Premium Finish", "refId": "unit-001"}, {"metadataKey": "unit-smart-home-features", "value": "IoT Enabled", "refId": "unit-001"}, {"metadataKey": "unit-warranty-period", "value": "5 Years", "refId": "unit-001"}]}]}], "metadata": [{"metadataId": "product-license-key", "isUnique": true, "isActive": true}, {"metadataId": "product-deployment-environment", "isUnique": false, "isActive": true}, {"metadataId": "product-support-tier", "isUnique": false, "isActive": true}, {"metadataId": "product-integration-count", "isUnique": false, "isActive": true}, {"metadataId": "product-user-capacity", "isUnique": false, "isActive": true}], "values": [{"metadataKey": "product-license-key", "value": "REMS-2024-ENTERPRISE-001", "refId": "product-001"}, {"metadataKey": "product-deployment-environment", "value": "Cloud", "refId": "product-001"}, {"metadataKey": "product-support-tier", "value": "Premium", "refId": "product-001"}, {"metadataKey": "product-integration-count", "value": "15", "refId": "product-001"}, {"metadataKey": "product-user-capacity", "value": "1000", "refId": "product-001"}]}]}