using Application.Integrations.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Integrations.Commands;

/// <summary>
/// Update integration command
/// </summary>
public class UpdateIntegrationCommand : IRequest<Result<IntegrationDto>>
{
    /// <summary>
    /// Integration ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Integration name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Authentication type
    /// </summary>
    public string AuthType { get; set; } = string.Empty;

    /// <summary>
    /// Authentication configuration stored as JSON
    /// </summary>
    public string AuthConfig { get; set; } = string.Empty;

    /// <summary>
    /// Whether the integration is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Sync frequency for automatic synchronization
    /// </summary>
    public TimeSpan? SyncFrequency { get; set; }
}
