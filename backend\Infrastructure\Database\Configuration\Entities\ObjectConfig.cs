using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for Object entity
/// </summary>
public class ObjectConfig : IEntityTypeConfiguration<Domain.Entities.Object>
{
    public void Configure(EntityTypeBuilder<Domain.Entities.Object> builder)
    {
        builder.ToTable("Objects", "Genp");

        // Properties
        builder.Property(e => e.FeatureId)
            .IsRequired();

        builder.Property(e => e.ParentObjectId);

        builder.Property(e => e.Name)
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(e => e.Description)
            .HasColumnType("TEXT");

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        // Indexes
        builder.HasIndex(e => e.FeatureId)
            .HasDatabaseName("IX_Objects_FeatureId");

        builder.HasIndex(e => e.ParentObjectId)
            .HasDatabaseName("IX_Objects_ParentObjectId");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_Objects_IsActive")
            .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

        // Unique constraint
        builder.HasIndex(e => new { e.FeatureId, e.Name })
            .IsUnique()
            .HasDatabaseName("IX_Objects_FeatureId_Name");

        // Multi-tenancy
        builder.IsMultiTenant();

        // Relationships
        builder.HasOne(e => e.Feature)
            .WithMany(e => e.Objects)
            .HasForeignKey(e => e.FeatureId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.ParentObject)
            .WithMany(e => e.ChildObjects)
            .HasForeignKey(e => e.ParentObjectId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(e => e.ChildObjects)
            .WithOne(e => e.ParentObject)
            .HasForeignKey(e => e.ParentObjectId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(e => e.ObjectMetadata)
            .WithOne(e => e.Object)
            .HasForeignKey(e => e.ObjectId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
