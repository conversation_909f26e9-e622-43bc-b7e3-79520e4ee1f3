namespace Application.FeatureMetadata.DTOs;

/// <summary>
/// FeatureMetadata DTO
/// </summary>
public class FeatureMetadataDto
{
    /// <summary>
    /// FeatureMetadata ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Feature ID
    /// </summary>
    public Guid FeatureId { get; set; }

    /// <summary>
    /// Feature name
    /// </summary>
    public string? FeatureName { get; set; }

    /// <summary>
    /// Metadata ID
    /// </summary>
    public Guid MetadataId { get; set; }

    /// <summary>
    /// Metadata key
    /// </summary>
    public string? MetadataKey { get; set; }

    /// <summary>
    /// Metadata display label
    /// </summary>
    public string? MetadataDisplayLabel { get; set; }

    /// <summary>
    /// Whether this metadata is unique for the feature
    /// </summary>
    public bool IsUnique { get; set; }

    /// <summary>
    /// Whether this metadata link is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Number of values for this metadata
    /// </summary>
    public int ValuesCount { get; set; }

    /// <summary>
    /// Created at
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by
    /// </summary>
    public Guid CreatedBy { get; set; }

    /// <summary>
    /// Modified at
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Modified by
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}
