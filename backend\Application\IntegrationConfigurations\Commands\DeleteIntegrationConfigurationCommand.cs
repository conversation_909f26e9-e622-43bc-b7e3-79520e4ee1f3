using MediatR;
using Shared.Common.Response;

namespace Application.IntegrationConfigurations.Commands;

/// <summary>
/// Delete integration configuration command
/// </summary>
public class DeleteIntegrationConfigurationCommand : IRequest<Result<bool>>
{
    /// <summary>
    /// Integration Configuration ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteIntegrationConfigurationCommand(Guid id)
    {
        Id = id;
    }
}
