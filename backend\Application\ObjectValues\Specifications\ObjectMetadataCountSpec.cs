using Ardalis.Specification;
using Domain.Entities;

namespace Application.ObjectValues.Specifications;

/// <summary>
/// Specification to count ObjectMetadata for an ObjectId
/// </summary>
public class ObjectMetadataCountSpec : Specification<ObjectMetadata>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ObjectMetadataCountSpec(Guid objectId, bool onlyActive = true)
    {
        Query.Where(om => om.ObjectId == objectId);

        // Active filters
        if (onlyActive)
        {
            Query.Where(om => om.IsActive);
        }
    }
}
