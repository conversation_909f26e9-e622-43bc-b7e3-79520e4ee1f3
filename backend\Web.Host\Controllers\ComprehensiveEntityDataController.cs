using Application.ComprehensiveEntityData.DTOs;
using Application.ComprehensiveEntityData.Queries;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;

namespace Web.Host.Controllers;

/// <summary>
/// Comprehensive Entity Data API Controller
/// Provides endpoints to retrieve hierarchical entity data with complete metadata and values
/// </summary>
[Route("api/comprehensive-entity")]
public class ComprehensiveEntityDataController : BaseApiController
{
    /// <summary>
    /// Get comprehensive entity data with advanced filtering and pagination
    /// </summary>
    /// <param name="productId">Filter by Product ID</param>
    /// <param name="featureId">Filter by Feature ID</param>
    /// <param name="objectId">Filter by Object ID</param>
    /// <param name="searchTerm">Search term for names and descriptions</param>
    /// <param name="isActive">Filter by active status</param>
    /// <param name="onlyVisibleMetadata">Include only visible metadata (default: true)</param>
    /// <param name="onlyActiveMetadata">Include only active metadata (default: true)</param>
    /// <param name="pageNumber">Page number for pagination (default: 1)</param>
    /// <param name="pageSize">Page size for pagination (default: 50)</param>
    /// <param name="orderBy">Order by field (ProductName, FeatureName, ObjectName, etc.)</param>
    /// <param name="orderDirection">Order direction: asc or desc (default: asc)</param>
    /// <returns>Comprehensive entity data with hierarchical structure</returns>
    /// <remarks>
    /// This endpoint returns a comprehensive view of entity data including:
    /// - Product information with all features
    /// - Feature information with all objects and metadata
    /// - Object information with all metadata and values
    /// - Hierarchical parent-child object relationships
    /// - Complete metadata with data type information
    /// - All associated values grouped by RefId
    /// 
    /// The response is structured hierarchically:
    /// ```
    /// Products
    /// ├── Features
    /// │   ├── Feature Metadata & Values
    /// │   └── Objects
    /// │       ├── Object Metadata & Values
    /// │       └── Child Objects (recursive)
    /// ```
    /// 
    /// Sample response structure:
    /// ```json
    /// {
    ///   "succeeded": true,
    ///   "data": {
    ///     "products": [
    ///       {
    ///         "productInfo": {
    ///           "id": "product-guid",
    ///           "name": "Product Name",
    ///           "description": "Product Description",
    ///           "isActive": true
    ///         },
    ///         "features": [
    ///           {
    ///             "featureInfo": {
    ///               "id": "feature-guid",
    ///               "name": "Feature Name",
    ///               "description": "Feature Description",
    ///               "isActive": true
    ///             },
    ///             "featureMetadata": [
    ///               {
    ///                 "metadata": {
    ///                   "id": "metadata-guid",
    ///                   "metadataKey": "fieldName",
    ///                   "displayLabel": "Field Name",
    ///                   "dataType": {
    ///                     "id": "datatype-guid",
    ///                     "name": "text",
    ///                     "displayName": "Text"
    ///                   }
    ///                 },
    ///                 "values": [
    ///                   {
    ///                     "id": "value-guid",
    ///                     "value": "Sample Value",
    ///                     "refId": "ref-guid"
    ///                   }
    ///                 ]
    ///               }
    ///             ],
    ///             "objects": [
    ///               {
    ///                 "objectInfo": {
    ///                   "id": "object-guid",
    ///                   "name": "Object Name",
    ///                   "description": "Object Description",
    ///                   "isActive": true
    ///                 },
    ///                 "objectMetadata": [...],
    ///                 "childObjects": [...]
    ///               }
    ///             ]
    ///           }
    ///         ]
    ///       }
    ///     ],
    ///     "totalCount": 100,
    ///     "pageNumber": 1,
    ///     "pageSize": 50,
    ///     "totalPages": 2
    ///   }
    /// }
    /// ```
    /// </remarks>
    [HttpGet]
    [TenantIdHeader]
    [AllowAnonymous]
    public async Task<ActionResult<Result<ComprehensiveEntityDataResponseDto>>> GetComprehensiveEntityData(
        [FromQuery] Guid? productId = null,
        [FromQuery] Guid? featureId = null,
        [FromQuery] Guid? objectId = null,
        [FromQuery] string? searchTerm = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] bool onlyVisibleMetadata = true,
        [FromQuery] bool onlyActiveMetadata = true,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? orderBy = null,
        [FromQuery] string orderDirection = "asc")
    {
        var query = new GetComprehensiveEntityDataQuery
        {
            ProductId = productId,
            FeatureId = featureId,
            ObjectId = objectId,
            SearchTerm = searchTerm,
            IsActive = isActive,
            OnlyVisibleMetadata = onlyVisibleMetadata,
            OnlyActiveMetadata = onlyActiveMetadata,
            PageNumber = pageNumber,
            PageSize = pageSize,
            OrderBy = orderBy,
            OrderDirection = orderDirection
        };

        var result = await Mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Get comprehensive entity data for a specific product
    /// </summary>
    /// <param name="productId">Product ID</param>
    /// <param name="onlyVisibleMetadata">Include only visible metadata (default: true)</param>
    /// <param name="onlyActiveMetadata">Include only active metadata (default: true)</param>
    /// <param name="pageSize">Page size (default: 1000 for single product)</param>
    /// <returns>Comprehensive data for the specified product</returns>
    [HttpGet("products/{productId:guid}")]
    [TenantIdHeader]
    [AllowAnonymous]
    public async Task<ActionResult<Result<ComprehensiveEntityDataResponseDto>>> GetComprehensiveEntityDataByProduct(
        Guid productId,
        [FromQuery] bool onlyVisibleMetadata = true,
        [FromQuery] bool onlyActiveMetadata = true,
        [FromQuery] int pageSize = 1000)
    {
        var query = new GetComprehensiveEntityDataQuery
        {
            ProductId = productId,
            OnlyVisibleMetadata = onlyVisibleMetadata,
            OnlyActiveMetadata = onlyActiveMetadata,
            PageSize = pageSize
        };

        var result = await Mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Get comprehensive entity data for a specific feature
    /// </summary>
    /// <param name="featureId">Feature ID</param>
    /// <param name="onlyVisibleMetadata">Include only visible metadata (default: true)</param>
    /// <param name="onlyActiveMetadata">Include only active metadata (default: true)</param>
    /// <param name="pageSize">Page size (default: 1000 for single feature)</param>
    /// <returns>Comprehensive data for the specified feature</returns>
    [HttpGet("features/{featureId:guid}")]
    [TenantIdHeader]
    [AllowAnonymous]
    public async Task<ActionResult<Result<ComprehensiveEntityDataResponseDto>>> GetComprehensiveEntityDataByFeature(
        Guid featureId,
        [FromQuery] bool onlyVisibleMetadata = true,
        [FromQuery] bool onlyActiveMetadata = true,
        [FromQuery] int pageSize = 1000)
    {
        var query = new GetComprehensiveEntityDataQuery
        {
            FeatureId = featureId,
            OnlyVisibleMetadata = onlyVisibleMetadata,
            OnlyActiveMetadata = onlyActiveMetadata,
            PageSize = pageSize
        };

        var result = await Mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Get comprehensive entity data for a specific object
    /// </summary>
    /// <param name="objectId">Object ID</param>
    /// <param name="onlyVisibleMetadata">Include only visible metadata (default: true)</param>
    /// <param name="onlyActiveMetadata">Include only active metadata (default: true)</param>
    /// <param name="pageSize">Page size (default: 1000 for single object)</param>
    /// <returns>Comprehensive data for the specified object</returns>

    [HttpGet("objects/{objectId:guid}")]
    [TenantIdHeader]
    [AllowAnonymous]
    public async Task<ActionResult<Result<ComprehensiveEntityDataResponseDto>>> GetComprehensiveEntityDataByObject(
        Guid objectId,
        [FromQuery] bool onlyVisibleMetadata = true,
        [FromQuery] bool onlyActiveMetadata = true,
        [FromQuery] int pageSize = 1000)
    {
        var query = new GetComprehensiveEntityDataQuery
        {
            ObjectId = objectId,
            OnlyVisibleMetadata = onlyVisibleMetadata,
            OnlyActiveMetadata = onlyActiveMetadata,
            PageSize = pageSize
        };

        var result = await Mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Search comprehensive entity data by term
    /// </summary>
    /// <param name="searchTerm">Search term to look for in names and descriptions</param>
    /// <param name="isActive">Filter by active status</param>
    /// <param name="onlyVisibleMetadata">Include only visible metadata (default: true)</param>
    /// <param name="onlyActiveMetadata">Include only active metadata (default: true)</param>
    /// <param name="pageNumber">Page number for pagination (default: 1)</param>
    /// <param name="pageSize">Page size for pagination (default: 50)</param>
    /// <param name="orderBy">Order by field</param>
    /// <param name="orderDirection">Order direction: asc or desc (default: asc)</param>
    /// <returns>Comprehensive entity data matching the search criteria</returns>
    [HttpGet("search")]
    [TenantIdHeader]
    [AllowAnonymous]
    public async Task<ActionResult<Result<ComprehensiveEntityDataResponseDto>>> SearchComprehensiveEntityData(
        [FromQuery] string searchTerm,
        [FromQuery] bool? isActive = null,
        [FromQuery] bool onlyVisibleMetadata = true,
        [FromQuery] bool onlyActiveMetadata = true,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? orderBy = null,
        [FromQuery] string orderDirection = "asc")
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
        {
            return BadRequest(Result<ComprehensiveEntityDataResponseDto>.Failure("Search term is required"));
        }

        var query = new GetComprehensiveEntityDataQuery
        {
            SearchTerm = searchTerm,
            IsActive = isActive,
            OnlyVisibleMetadata = onlyVisibleMetadata,
            OnlyActiveMetadata = onlyActiveMetadata,
            PageNumber = pageNumber,
            PageSize = pageSize,
            OrderBy = orderBy,
            OrderDirection = orderDirection
        };

        var result = await Mediator.Send(query);
        return Ok(result);
    }
}
