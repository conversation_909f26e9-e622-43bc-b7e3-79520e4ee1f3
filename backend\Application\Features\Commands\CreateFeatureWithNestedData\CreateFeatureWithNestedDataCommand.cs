using Application.Features.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Features.Commands.CreateFeatureWithNestedData;

/// <summary>
/// Command to create a Feature with deeply nested objects and metadata
/// </summary>
public class CreateFeatureWithNestedDataCommand : IRequest<Result<CreateFeatureWithNestedDataResponseDto>>
{
    /// <summary>
    /// Feature information with nested objects and metadata
    /// </summary>
    public FeatureCreationDto Feature { get; set; } = new();

    /// <summary>
    /// Create command from DTO
    /// </summary>
    public static CreateFeatureWithNestedDataCommand FromDto(CreateFeatureWithNestedDataRequestDto dto)
    {
        return new CreateFeatureWithNestedDataCommand
        {
            Feature = dto.Feature
        };
    }
}
