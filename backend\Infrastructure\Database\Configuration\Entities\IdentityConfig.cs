﻿using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.Database;

namespace Infrastructure.Database.Configuration.Entities
{
    public class IdentityUserClaimConfig : IEntityTypeConfiguration<IdentityUserClaim<Guid>>
    {
        public void Configure(EntityTypeBuilder<IdentityUserClaim<Guid>> builder) =>
            builder
                .ToTable("UserClaims", SchemaNames.Genp)
                .IsMultiTenant();
    }

    public class IdentityUserLoginConfig : IEntityTypeConfiguration<IdentityUserLogin<Guid>>
    {
        public void Configure(EntityTypeBuilder<IdentityUserLogin<Guid>> builder) =>
            builder
                .ToTable("UserLogins", SchemaNames.Genp)
                .IsMultiTenant();
    }

    public class IdentityUserTokenConfig : IEntityTypeConfiguration<IdentityUserToken<Guid>>
    {
        public void Configure(EntityTypeBuilder<IdentityUserToken<Guid>> builder) =>
            builder
                .ToTable("UserTokens", SchemaNames.Genp)
                .IsMultiTenant();
    }
}
