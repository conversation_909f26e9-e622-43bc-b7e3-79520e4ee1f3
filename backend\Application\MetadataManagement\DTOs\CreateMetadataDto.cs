using System.ComponentModel.DataAnnotations;

namespace Application.MetadataManagement.DTOs;

/// <summary>
/// Create Metadata DTO
/// </summary>
public class CreateMetadataDto
{
    /// <summary>
    /// Unique metadata key
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string MetadataKey { get; set; } = string.Empty;

    /// <summary>
    /// Reference to the data type
    /// </summary>
    [Required]
    public Guid DataTypeId { get; set; }

    /// <summary>
    /// Custom validation pattern override
    /// </summary>
    [MaxLength(500)]
    public string? CustomValidationPattern { get; set; }

    /// <summary>
    /// Custom minimum length override
    /// </summary>
    public int? CustomMinLength { get; set; }

    /// <summary>
    /// Custom maximum length override
    /// </summary>
    public int? CustomMaxLength { get; set; }

    /// <summary>
    /// Custom minimum value override
    /// </summary>
    public decimal? CustomMinValue { get; set; }

    /// <summary>
    /// Custom maximum value override
    /// </summary>
    public decimal? CustomMaxValue { get; set; }

    /// <summary>
    /// Custom required field override
    /// </summary>
    public bool? CustomIsRequired { get; set; }

    /// <summary>
    /// Custom placeholder text override
    /// </summary>
    [MaxLength(255)]
    public string? CustomPlaceholder { get; set; }

    /// <summary>
    /// Custom options override
    /// </summary>
    public string? CustomOptions { get; set; }

    /// <summary>
    /// Custom maximum selections override
    /// </summary>
    public int? CustomMaxSelections { get; set; }

    /// <summary>
    /// Custom allowed file types override
    /// </summary>
    [MaxLength(500)]
    public string? CustomAllowedFileTypes { get; set; }

    /// <summary>
    /// Custom maximum file size override
    /// </summary>
    public long? CustomMaxFileSize { get; set; }

    /// <summary>
    /// Custom error message override
    /// </summary>
    [MaxLength(255)]
    public string? CustomErrorMessage { get; set; }

    /// <summary>
    /// Display label for the field
    /// </summary>
    [MaxLength(255)]
    public string? DisplayLabel { get; set; }

    /// <summary>
    /// Help text for the field
    /// </summary>
    [MaxLength(500)]
    public string? HelpText { get; set; }

    /// <summary>
    /// Order of the field in forms
    /// </summary>
    public int? FieldOrder { get; set; }

    /// <summary>
    /// Whether the field is visible
    /// </summary>
    public bool IsVisible { get; set; } = true;

    /// <summary>
    /// Whether the field is readonly
    /// </summary>
    public bool IsReadonly { get; set; } = false;
}
