using Abstraction.Repositories;
using Application.DataTypes.DTOs;
using Domain.Entities;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.DataTypes.Queries;

/// <summary>
/// Handler for getting a data type by ID
/// </summary>
public class GetDataTypeByIdQueryHandler : IRequestHandler<GetDataTypeByIdQuery, Result<DataTypeDto>>
{
    private readonly IDynamicRepository<DataType> _repository;
    private readonly ILogger<GetDataTypeByIdQueryHandler> _logger;

    public GetDataTypeByIdQueryHandler(
        IDynamicRepository<DataType> repository,
        ILogger<GetDataTypeByIdQueryHandler> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    public async Task<Result<DataTypeDto>> Handle(GetDataTypeByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting data type by ID: {Id}", request.Id);

            var dataType = await _repository.GetByIdAsync(request.Id);

            if (dataType == null)
            {
                _logger.LogWarning("Data type not found with ID: {Id}", request.Id);
                return Result<DataTypeDto>.Failure(new List<string> { $"Data type with ID {request.Id} not found" });
            }

            var dataTypeDto = dataType.Adapt<DataTypeDto>();

            _logger.LogInformation("Successfully retrieved data type: {Name} (ID: {Id})", dataType.Name, dataType.Id);

            return Result<DataTypeDto>.Success(dataTypeDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting data type by ID: {Id}", request.Id);
            return Result<DataTypeDto>.Failure(new List<string> { ex.Message });
        }
    }
}
