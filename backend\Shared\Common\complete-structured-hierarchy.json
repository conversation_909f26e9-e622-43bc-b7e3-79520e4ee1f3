{"operationType": 5, "includeFeatures": true, "includeObjects": true, "includeMetadata": true, "productData": [{"name": "Real Estate Management System", "description": "Complete real estate project management system with hierarchical structure", "version": "1.0.0", "isActive": true, "features": [{"name": "Property Management", "description": "Comprehensive property management with full hierarchy support", "isDefault": true, "isActive": true, "metadata": [{"metadataId": "feature-priority-level", "isUnique": false, "isActive": true}, {"metadataId": "feature-complexity-score", "isUnique": false, "isActive": true}, {"metadataId": "feature-development-status", "isUnique": false, "isActive": true}], "objects": [{"name": "Project", "description": "Top-level real estate project", "isActive": true, "metadata": [{"metadataId": "project-approval-status", "isUnique": false, "isActive": true}, {"metadataId": "project-investment-amount", "isUnique": false, "isActive": true}, {"metadataId": "project-regulatory-clearance", "isUnique": false, "isActive": true}]}, {"name": "Phase", "description": "Project development phase", "isActive": true, "metadata": [{"metadataId": "phase-construction-timeline", "isUnique": false, "isActive": true}, {"metadataId": "phase-contractor-name", "isUnique": false, "isActive": true}, {"metadataId": "phase-quality-rating", "isUnique": false, "isActive": true}]}, {"name": "Block", "description": "Construction block within a phase", "isActive": true, "metadata": [{"metadataId": "block-foundation-type", "isUnique": false, "isActive": true}, {"metadataId": "block-structural-design", "isUnique": false, "isActive": true}, {"metadataId": "block-safety-compliance", "isUnique": false, "isActive": true}]}, {"name": "Property", "description": "Individual property within a block", "isActive": true, "metadata": [{"metadataId": "property-legal-title", "isUnique": true, "isActive": true}, {"metadataId": "property-market-value", "isUnique": false, "isActive": true}, {"metadataId": "property-amenities-score", "isUnique": false, "isActive": true}]}, {"name": "Tower", "description": "Building tower within a property", "isActive": true, "metadata": [{"metadataId": "tower-architectural-style", "isUnique": false, "isActive": true}, {"metadataId": "tower-fire-safety-rating", "isUnique": false, "isActive": true}, {"metadataId": "tower-energy-efficiency", "isUnique": false, "isActive": true}]}, {"name": "Floor", "description": "Floor within a tower", "isActive": true, "metadata": [{"metadataId": "floor-ceiling-height", "isUnique": false, "isActive": true}, {"metadataId": "floor-ventilation-type", "isUnique": false, "isActive": true}, {"metadataId": "floor-accessibility-features", "isUnique": false, "isActive": true}]}, {"name": "Unit", "description": "Individual unit within a floor", "isActive": true, "metadata": [{"metadataId": "unit-registration-number", "isUnique": true, "isActive": true}, {"metadataId": "unit-interior-design", "isUnique": false, "isActive": true}, {"metadataId": "unit-smart-home-features", "isUnique": false, "isActive": true}, {"metadataId": "unit-warranty-period", "isUnique": false, "isActive": true}]}]}], "metadata": [{"metadataId": "product-license-key", "isUnique": true, "isActive": true}, {"metadataId": "product-deployment-environment", "isUnique": false, "isActive": true}, {"metadataId": "product-support-tier", "isUnique": false, "isActive": true}, {"metadataId": "product-integration-count", "isUnique": false, "isActive": true}, {"metadataId": "product-user-capacity", "isUnique": false, "isActive": true}]}]}