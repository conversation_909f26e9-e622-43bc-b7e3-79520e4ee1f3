using Application.ProductMetadata.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.ProductMetadata.Queries;

/// <summary>
/// Get ProductMetadata query
/// </summary>
public class GetProductMetadataQuery : IRequest<PaginatedResult<ProductMetadataDto>>
{
    /// <summary>
    /// Page number
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Search term
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Product ID filter
    /// </summary>
    public Guid? ProductId { get; set; }

    /// <summary>
    /// Metadata ID filter
    /// </summary>
    public Guid? MetadataId { get; set; }

    /// <summary>
    /// Is active filter
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Order by
    /// </summary>
    public string? OrderBy { get; set; }
}
