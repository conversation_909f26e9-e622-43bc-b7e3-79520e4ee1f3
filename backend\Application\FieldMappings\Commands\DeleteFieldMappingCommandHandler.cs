using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.FieldMappings.Commands;

/// <summary>
/// Delete field mapping command handler
/// </summary>
public class DeleteFieldMappingCommandHandler : IRequestHandler<DeleteFieldMappingCommand, Result<bool>>
{
    private readonly IRepository<FieldMapping> _fieldMappingRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteFieldMappingCommandHandler(IRepository<FieldMapping> fieldMappingRepository)
    {
        _fieldMappingRepository = fieldMappingRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<bool>> Handle(DeleteFieldMappingCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Get existing field mapping
            var fieldMapping = await _fieldMappingRepository.GetByIdAsync(request.Id, cancellationToken);
            if (fieldMapping == null)
            {
                return Result<bool>.Failure("Field mapping not found.");
            }

            // Soft delete the field mapping
            await _fieldMappingRepository.DeleteAsync(fieldMapping, cancellationToken);

            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            return Result<bool>.Failure($"Failed to delete field mapping: {ex.Message}");
        }
    }
}
