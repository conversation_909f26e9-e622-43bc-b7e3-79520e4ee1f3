using Ardalis.Specification;
using Domain.Entities;

namespace Application.IntegrationConfigurations.Specifications;

/// <summary>
/// Specification to get integration configuration by integration, API, and object IDs
/// </summary>
public class IntegrationConfigurationByIntegrationApiObjectSpec : Specification<IntegrationConfiguration>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public IntegrationConfigurationByIntegrationApiObjectSpec(Guid integrationId, Guid integrationApiId, Guid objectId)
    {
        Query.Where(ic => ic.IntegrationId == integrationId &&
                          ic.IntegrationApiId == integrationApiId &&
                          ic.ObjectId == objectId);
    }
}
