using Domain.Entities;
using Infrastructure.Database.Configuration;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Finbuckle.MultiTenant;
using Finbuckle.MultiTenant.EntityFrameworkCore;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for FieldMapping entity
/// </summary>
public class FieldMappingConfig : IEntityTypeConfiguration<FieldMapping>
{
    public void Configure(EntityTypeBuilder<FieldMapping> builder)
    {

        builder.ToTable("FieldMapping", "Genp");

        builder.IsMultiTenant();

        // Properties
        builder.Property(e => e.IntegrationId)
            .IsRequired();

        builder.Property(e => e.ObjectMetadataId)
            .IsRequired();

        builder.Property(e => e.UserId);

        builder.Property(e => e.RoleId);

        builder.Property(e => e.SourceField)
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(e => e.SourceType)
            .HasMaxLength(50);

        builder.Property(e => e.ApiName)
            .HasMaxLength(255);

        builder.Property(e => e.TargetObjectName)
            .HasMaxLength(255);

        builder.Property(e => e.TransformationRules)
            .HasColumnType("TEXT");

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        // Indexes
        builder.HasIndex(e => e.IntegrationId)
            .HasDatabaseName("IX_FieldMapping_IntegrationId");

        builder.HasIndex(e => e.ObjectMetadataId)
            .HasDatabaseName("IX_FieldMapping_ObjectMetadataId");

        builder.HasIndex(e => e.UserId)
            .HasDatabaseName("IX_FieldMapping_UserId");

        builder.HasIndex(e => e.RoleId)
            .HasDatabaseName("IX_FieldMapping_RoleId");

        builder.HasIndex(e => e.SourceField)
            .HasDatabaseName("IX_FieldMapping_SourceField");

        builder.HasIndex(e => e.SourceType)
            .HasDatabaseName("IX_FieldMapping_SourceType");

        builder.HasIndex(e => e.ApiName)
            .HasDatabaseName("IX_FieldMapping_ApiName");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_FieldMapping_IsActive")
            .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

        // Relationships
        builder.HasOne(e => e.Integration)
            .WithMany(e => e.FieldMappings)
            .HasForeignKey(e => e.IntegrationId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.ObjectMetadata)
            .WithMany(e => e.FieldMappings)
            .HasForeignKey(e => e.ObjectMetadataId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(e => e.User)
            .WithMany(e => e.FieldMappings)
            .HasForeignKey(e => e.UserId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasOne(e => e.Role)
            .WithMany(e => e.FieldMappings)
            .HasForeignKey(e => e.RoleId)
            .OnDelete(DeleteBehavior.SetNull);
    }
}
