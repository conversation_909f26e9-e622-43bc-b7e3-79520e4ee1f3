using Ardalis.Specification;
using Domain.Entities;

namespace Application.ObjectMetadataManagement.Specifications;

/// <summary>
/// Specification to get ObjectMetadata with filters and pagination
/// </summary>
public class ObjectMetadataWithFiltersSpec : Specification<ObjectMetadata>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ObjectMetadataWithFiltersSpec(
        Guid objectId,
        string? searchTerm = null,
        Guid? metadataId = null,
        bool? isActive = null,
        string? orderBy = null,
        int skip = 0,
        int take = 0)
    {
        Query.Where(om => om.ObjectId == objectId && !om.IsDeleted);

        if (isActive.HasValue)
        {
            Query.Where(om => om.IsActive == isActive.Value);
        }

        // Include related data
        Query.Include(om => om.Object);
        Query.Include(om => om.Metadata)
             .ThenInclude(m => m.DataType);

        // Apply search filter if provided
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            Query.Where(om => om.Metadata.MetadataKey.Contains(searchTerm) ||
                             (om.Metadata.DisplayLabel != null && om.Metadata.DisplayLabel.Contains(searchTerm)));
        }

        // Apply metadata ID filter if provided
        if (metadataId.HasValue)
        {
            Query.Where(om => om.MetadataId == metadataId.Value);
        }

        // Apply ordering
        if (!string.IsNullOrWhiteSpace(orderBy))
        {
            switch (orderBy.ToLower())
            {
                case "metadatakey":
                    Query.OrderBy(om => om.Metadata.MetadataKey);
                    break;
                case "metadatakey_desc":
                    Query.OrderByDescending(om => om.Metadata.MetadataKey);
                    break;
                case "displayLabel":
                    Query.OrderBy(om => om.Metadata.DisplayLabel);
                    break;
                case "displayLabel_desc":
                    Query.OrderByDescending(om => om.Metadata.DisplayLabel);
                    break;
                default:
                    Query.OrderBy(om => om.Metadata.FieldOrder ?? int.MaxValue)
                         .ThenBy(om => om.Metadata.MetadataKey);
                    break;
            }
        }
        else
        {
            Query.OrderBy(om => om.Metadata.FieldOrder ?? int.MaxValue)
                 .ThenBy(om => om.Metadata.MetadataKey);
        }

        // Apply pagination
        if (skip > 0)
        {
            Query.Skip(skip);
        }

        if (take > 0)
        {
            Query.Take(take);
        }
    }
}
