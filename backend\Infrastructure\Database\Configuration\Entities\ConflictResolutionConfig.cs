using Domain.Entities;
using Infrastructure.Database.Configuration;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Finbuckle.MultiTenant;
using Finbuckle.MultiTenant.EntityFrameworkCore;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for ConflictResolution entity
/// </summary>
public class ConflictResolutionConfig : IEntityTypeConfiguration<ConflictResolution>
{
    public void Configure(EntityTypeBuilder<ConflictResolution> builder)
    {

        builder.ToTable("ConflictResolution", "Genp");

        builder.IsMultiTenant();

        // Properties
        builder.Property(e => e.IntegrationId)
            .IsRequired();

        builder.Property(e => e.ObjectId)
            .IsRequired();

        builder.Property(e => e.FieldName)
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(e => e.ConflictType)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.ResolutionStrategy)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.SourceValue)
            .HasColumnType("TEXT");

        builder.Property(e => e.TargetValue)
            .HasColumnType("TEXT");

        builder.Property(e => e.ResolvedValue)
            .HasColumnType("TEXT");

        builder.Property(e => e.ResolvedBy);

        builder.Property(e => e.ResolvedAt);

        builder.Property(e => e.IsResolved)
            .HasDefaultValue(false);

        // Indexes
        builder.HasIndex(e => e.IntegrationId)
            .HasDatabaseName("IX_ConflictResolution_IntegrationId");

        builder.HasIndex(e => e.ObjectId)
            .HasDatabaseName("IX_ConflictResolution_ObjectId");

        builder.HasIndex(e => e.FieldName)
            .HasDatabaseName("IX_ConflictResolution_FieldName");

        builder.HasIndex(e => e.ConflictType)
            .HasDatabaseName("IX_ConflictResolution_ConflictType");

        builder.HasIndex(e => e.ResolutionStrategy)
            .HasDatabaseName("IX_ConflictResolution_ResolutionStrategy");

        builder.HasIndex(e => e.IsResolved)
            .HasDatabaseName("IX_ConflictResolution_IsResolved");

        builder.HasIndex(e => e.ResolvedAt)
            .HasDatabaseName("IX_ConflictResolution_ResolvedAt");

        // Relationships
        builder.HasOne(e => e.Integration)
            .WithMany(e => e.ConflictResolutions)
            .HasForeignKey(e => e.IntegrationId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.Object)
            .WithMany(e => e.ConflictResolutions)
            .HasForeignKey(e => e.ObjectId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(e => e.ResolvedByUser)
            .WithMany(e => e.ConflictResolutions)
            .HasForeignKey(e => e.ResolvedBy)
            .OnDelete(DeleteBehavior.SetNull);
    }
}
