using Application.FeatureValues.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.FeatureValues.Queries;

/// <summary>
/// Get FeatureValues query
/// </summary>
public class GetFeatureValuesQuery : IRequest<PaginatedResult<FeatureValueDto>>
{
    /// <summary>
    /// Page number
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Search term
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Feature metadata ID filter
    /// </summary>
    public Guid? FeatureMetadataId { get; set; }

    /// <summary>
    /// Reference ID filter
    /// </summary>
    public Guid? RefId { get; set; }

    /// <summary>
    /// Parent feature value ID filter
    /// </summary>
    public Guid? ParentFeatureValueId { get; set; }

    /// <summary>
    /// Order by
    /// </summary>
    public string? OrderBy { get; set; }
}
