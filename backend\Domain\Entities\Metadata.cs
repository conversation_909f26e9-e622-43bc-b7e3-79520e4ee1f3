using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Global metadata field definitions for all entity types
/// </summary>
public class Metadata : AuditableEntity, IAggregateRoot
{
    /// <summary>
    /// Unique metadata key
    /// </summary>
    public string Metadata<PERSON>ey { get; set; } = string.Empty;

    /// <summary>
    /// Reference to the data type
    /// </summary>
    public Guid DataTypeId { get; set; }

    // Customer-specific validation overrides
    /// <summary>
    /// Custom validation pattern override
    /// </summary>
    public string? CustomValidationPattern { get; set; }

    /// <summary>
    /// Custom minimum length override
    /// </summary>
    public int? CustomMinLength { get; set; }

    /// <summary>
    /// Custom maximum length override
    /// </summary>
    public int? CustomMaxLength { get; set; }

    /// <summary>
    /// Custom minimum value override
    /// </summary>
    public decimal? CustomMinValue { get; set; }

    /// <summary>
    /// Custom maximum value override
    /// </summary>
    public decimal? CustomMaxValue { get; set; }

    /// <summary>
    /// Custom required field override
    /// </summary>
    public bool? CustomIsRequired { get; set; }

    // Customer-specific UI overrides
    /// <summary>
    /// Custom placeholder text override
    /// </summary>
    public string? CustomPlaceholder { get; set; }

    /// <summary>
    /// Custom options override
    /// </summary>
    public string? CustomOptions { get; set; }

    /// <summary>
    /// Custom maximum selections override
    /// </summary>
    public int? CustomMaxSelections { get; set; }

    /// <summary>
    /// Custom allowed file types override
    /// </summary>
    public string? CustomAllowedFileTypes { get; set; }

    /// <summary>
    /// Custom maximum file size override
    /// </summary>
    public long? CustomMaxFileSize { get; set; }

    // Customer-specific error messages
    /// <summary>
    /// Custom error message override
    /// </summary>
    public string? CustomErrorMessage { get; set; }

    // UI Display Properties
    /// <summary>
    /// Display label for the field
    /// </summary>
    public string? DisplayLabel { get; set; }

    /// <summary>
    /// Help text for the field
    /// </summary>
    public string? HelpText { get; set; }

    /// <summary>
    /// Order of the field in forms
    /// </summary>
    public int? FieldOrder { get; set; }

    /// <summary>
    /// Whether the field is visible
    /// </summary>
    public bool IsVisible { get; set; } = true;

    /// <summary>
    /// Whether the field is readonly
    /// </summary>
    public bool IsReadonly { get; set; } = false;

    // Navigation Properties
    /// <summary>
    /// Data type definition
    /// </summary>
    public virtual DataType DataType { get; set; } = null!;

    /// <summary>
    /// Tenant info metadata links
    /// </summary>
    public virtual ICollection<TenantInfoMetadata> TenantInfoMetadata { get; set; } = new List<TenantInfoMetadata>();

    /// <summary>
    /// Product metadata links
    /// </summary>
    public virtual ICollection<ProductMetadata> ProductMetadata { get; set; } = new List<ProductMetadata>();

    /// <summary>
    /// Feature metadata links
    /// </summary>
    public virtual ICollection<FeatureMetadata> FeatureMetadata { get; set; } = new List<FeatureMetadata>();

    /// <summary>
    /// Role metadata links
    /// </summary>
    public virtual ICollection<RoleMetadata> RoleMetadata { get; set; } = new List<RoleMetadata>();

    /// <summary>
    /// User metadata links
    /// </summary>
    public virtual ICollection<UserMetadata> UserMetadata { get; set; } = new List<UserMetadata>();

    /// <summary>
    /// Object metadata links
    /// </summary>
    public virtual ICollection<ObjectMetadata> ObjectMetadata { get; set; } = new List<ObjectMetadata>();

    /// <summary>
    /// Subscription metadata links
    /// </summary>
    public virtual ICollection<SubscriptionMetadata> SubscriptionMetadata { get; set; } = new List<SubscriptionMetadata>();
}
