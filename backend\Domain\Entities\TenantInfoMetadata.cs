using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Links TenantInfo types to their metadata with IsUnique support
/// </summary>
public class TenantInfoMetadata : AuditableEntity, IAggregateRoot
{
    /// <summary>
    /// Metadata ID
    /// </summary>
    public Guid MetadataId { get; set; }

    /// <summary>
    /// Whether this metadata is unique for the tenant
    /// </summary>
    public bool IsUnique { get; set; } = false;

    /// <summary>
    /// Whether this metadata link is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// ShouldVisibleInList
    /// </summary>
    public bool ShouldVisibleInList { get; set; } = true;

    /// <summary>
    /// ShouldVisibleInList
    /// </summary>
    public bool ShouldVisibleInEdit { get; set; } = true;

    /// <summary>
    /// ShouldVisibleInList
    /// </summary>
    public bool ShouldVisibleInCreate { get; set; } = true;

    /// <summary>
    /// ShouldVisibleInList
    /// </summary>
    public bool ShouldVisibleInView { get; set; } = true;

    /// <summary>
    /// ShouldVisibleInList
    /// </summary>
    public bool IsCalculate { get; set; } = true;

    // Navigation properties
    /// <summary>
    /// Metadata definition
    /// </summary>
    public virtual Metadata Metadata { get; set; } = null!;

    /// <summary>
    /// Tenant info values
    /// </summary>
    public virtual ICollection<TenantInfoValue> TenantInfoValues { get; set; } = new List<TenantInfoValue>();
}
