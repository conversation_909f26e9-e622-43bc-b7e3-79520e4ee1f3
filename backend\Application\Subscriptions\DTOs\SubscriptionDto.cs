namespace Application.Subscriptions.DTOs;

/// <summary>
/// Subscription DTO
/// </summary>
public class SubscriptionDto
{
    /// <summary>
    /// Subscription ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Product ID this subscription is for
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Product name
    /// </summary>
    public string? ProductName { get; set; }

    /// <summary>
    /// Subscription type
    /// </summary>
    public string SubscriptionType { get; set; } = string.Empty;

    /// <summary>
    /// Subscription status
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// Subscription start date
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// Subscription end date
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Whether auto-renewal is enabled
    /// </summary>
    public bool AutoRenew { get; set; }

    /// <summary>
    /// Pricing tier
    /// </summary>
    public string? PricingTier { get; set; }

    /// <summary>
    /// Whether the subscription is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Number of metadata definitions
    /// </summary>
    public int MetadataCount { get; set; }

    /// <summary>
    /// Created at
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by
    /// </summary>
    public Guid CreatedBy { get; set; }

    /// <summary>
    /// Modified at
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Modified by
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}
