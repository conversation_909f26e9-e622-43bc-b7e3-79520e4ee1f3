using Application.MetadataManagement.DTOs;
using Application.MetadataManagement.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.MetadataManagement.Commands;

/// <summary>
/// Update Metadata command handler
/// </summary>
public class UpdateMetadataCommandHandler : IRequestHandler<UpdateMetadataCommand, Result<MetadataDto>>
{
    private readonly IRepository<Domain.Entities.Metadata> _repository;
    private readonly IRepository<DataType> _dataTypeRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpdateMetadataCommandHandler(
        IRepository<Domain.Entities.Metadata> repository,
        IRepository<DataType> dataTypeRepository)
    {
        _repository = repository;
        _dataTypeRepository = dataTypeRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<MetadataDto>> Handle(UpdateMetadataCommand request, CancellationToken cancellationToken)
    {
        // Get existing metadata
        var metadata = await _repository.GetByIdAsync(request.Id, cancellationToken);
        if (metadata == null)
        {
            return Result<MetadataDto>.Failure($"Metadata with ID '{request.Id}' not found.");
        }

        // Validate DataType exists
        var dataType = await _dataTypeRepository.GetByIdAsync(request.DataTypeId, cancellationToken);
        if (dataType == null)
        {
            return Result<MetadataDto>.Failure($"DataType with ID '{request.DataTypeId}' not found.");
        }

        // Check if another Metadata with same key already exists
        var existingMetadata = await _repository.GetBySpecAsync(new MetadataByKeySpec(request.MetadataKey), cancellationToken);
        if (existingMetadata != null && existingMetadata.Id != request.Id)
        {
            return Result<MetadataDto>.Failure($"Metadata with key '{request.MetadataKey}' already exists.");
        }

        // Update metadata properties
        metadata.MetadataKey = request.MetadataKey;
        metadata.DataTypeId = request.DataTypeId;
        metadata.CustomValidationPattern = request.CustomValidationPattern;
        metadata.CustomMinLength = request.CustomMinLength;
        metadata.CustomMaxLength = request.CustomMaxLength;
        metadata.CustomMinValue = request.CustomMinValue;
        metadata.CustomMaxValue = request.CustomMaxValue;
        metadata.CustomIsRequired = request.CustomIsRequired;
        metadata.CustomPlaceholder = request.CustomPlaceholder;
        metadata.CustomOptions = request.CustomOptions;
        metadata.CustomMaxSelections = request.CustomMaxSelections;
        metadata.CustomAllowedFileTypes = request.CustomAllowedFileTypes;
        metadata.CustomMaxFileSize = request.CustomMaxFileSize;
        metadata.CustomErrorMessage = request.CustomErrorMessage;
        metadata.DisplayLabel = request.DisplayLabel;
        metadata.HelpText = request.HelpText;
        metadata.FieldOrder = request.FieldOrder;
        metadata.IsVisible = request.IsVisible;
        metadata.IsReadonly = request.IsReadonly;

        await _repository.UpdateAsync(metadata, cancellationToken);

        var dto = new MetadataDto
        {
            Id = metadata.Id,
            MetadataKey = metadata.MetadataKey,
            DataTypeId = metadata.DataTypeId,
            DataTypeName = dataType.Name,
            CustomValidationPattern = metadata.CustomValidationPattern,
            CustomMinLength = metadata.CustomMinLength,
            CustomMaxLength = metadata.CustomMaxLength,
            CustomMinValue = metadata.CustomMinValue,
            CustomMaxValue = metadata.CustomMaxValue,
            CustomIsRequired = metadata.CustomIsRequired,
            CustomPlaceholder = metadata.CustomPlaceholder,
            CustomOptions = metadata.CustomOptions,
            CustomMaxSelections = metadata.CustomMaxSelections,
            CustomAllowedFileTypes = metadata.CustomAllowedFileTypes,
            CustomMaxFileSize = metadata.CustomMaxFileSize,
            CustomErrorMessage = metadata.CustomErrorMessage,
            DisplayLabel = metadata.DisplayLabel,
            HelpText = metadata.HelpText,
            FieldOrder = metadata.FieldOrder,
            IsVisible = metadata.IsVisible,
            IsReadonly = metadata.IsReadonly,
            CreatedAt = metadata.CreatedAt,
            CreatedBy = metadata.CreatedBy ?? Guid.Empty,
            ModifiedAt = metadata.ModifiedAt,
            ModifiedBy = metadata.ModifiedBy
        };

        return Result<MetadataDto>.Success(dto);
    }
}
