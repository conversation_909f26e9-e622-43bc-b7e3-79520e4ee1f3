namespace Shared.Common.Response;

/// <summary>
/// Generic API response class
/// </summary>
/// <typeparam name="T">Type of the data</typeparam>
public class ApiResponse<T>
{
    /// <summary>
    /// Whether the operation was successful
    /// </summary>
    public bool Succeeded { get; set; }

    /// <summary>
    /// Response message
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Response data
    /// </summary>
    public T? Data { get; set; }

    /// <summary>
    /// Constructor for success response
    /// </summary>
    public ApiResponse(bool succeeded, string message, T? data)
    {
        Succeeded = succeeded;
        Message = message;
        Data = data;
    }

    /// <summary>
    /// Create a success response
    /// </summary>
    public static ApiResponse<T> Success(T data, string message = "Operation completed successfully.")
    {
        return new ApiResponse<T>(true, message, data);
    }

    /// <summary>
    /// Create a failure response
    /// </summary>
    public static ApiResponse<T> Failure(string message, T? data = default)
    {
        return new ApiResponse<T>(false, message, data);
    }
}
