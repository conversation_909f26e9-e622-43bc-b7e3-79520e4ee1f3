﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Migrators.Migrations.Application
{
    /// <inheritdoc />
    public partial class AddRefIdInObjectValues : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsRefId",
                schema: "Genp",
                table: "ObjectValues",
                type: "boolean",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsUserId",
                schema: "Genp",
                table: "ObjectValues",
                type: "boolean",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsRefId",
                schema: "Genp",
                table: "ObjectValues");

            migrationBuilder.DropColumn(
                name: "IsUserId",
                schema: "Genp",
                table: "ObjectValues");
        }
    }
}
