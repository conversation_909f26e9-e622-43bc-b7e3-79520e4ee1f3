using MediatR;
using Shared.Common.Response;

namespace Application.MetadataManagement.Commands;

/// <summary>
/// Delete Metadata command
/// </summary>
public class DeleteMetadataCommand : IRequest<Result<bool>>
{
    /// <summary>
    /// Metadata ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteMetadataCommand(Guid id)
    {
        Id = id;
    }
}
