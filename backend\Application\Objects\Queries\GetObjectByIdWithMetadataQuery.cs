using Application.Objects.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Objects.Queries;

/// <summary>
/// Get Object by ID with metadata query
/// </summary>
public class GetObjectByIdWithMetadataQuery : IRequest<Result<ObjectWithMetadataDto>>
{
    /// <summary>
    /// Object ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public GetObjectByIdWithMetadataQuery(Guid id)
    {
        Id = id;
    }
}
