using Abstraction.Common;
using Application.ObjectValues.DTOs;
using Application.ObjectValues.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.ObjectValues.Queries.GetRefIdsByObjectId;

/// <summary>
/// Handler for GetRefIdsByObjectIdQuery
/// </summary>
public class GetRefIdsByObjectIdQueryHandler : IRequestHandler<GetRefIdsByObjectIdQuery, Result<PaginatedResult<ObjectValuesRefIdSummaryDto>>>
{
    private readonly IObjectValuesRepository _objectValuesRepository;
    private readonly ICurrentUser _currentUserService;
    private readonly ILogger<GetRefIdsByObjectIdQueryHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="objectValuesRepository">ObjectValues repository</param>
    /// <param name="currentUserService">Current user service</param>
    /// <param name="logger">Logger</param>
    public GetRefIdsByObjectIdQueryHandler(
        IObjectValuesRepository objectValuesRepository,
        ICurrentUser currentUserService,
        ILogger<GetRefIdsByObjectIdQueryHandler> logger)
    {
        _objectValuesRepository = objectValuesRepository;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    /// <param name="request">Query request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated RefId summaries or error</returns>
    public async Task<Result<PaginatedResult<ObjectValuesRefIdSummaryDto>>> Handle(
        GetRefIdsByObjectIdQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting RefIds for ObjectId {ObjectId} with search term '{SearchTerm}'",
                request.ObjectId, request.SearchTerm);

            var tenantId = _currentUserService.GetTenant();
            if (string.IsNullOrEmpty(tenantId))
            {
                _logger.LogWarning("TenantId is null or empty for ObjectId {ObjectId}", request.ObjectId);
                return Result<PaginatedResult<ObjectValuesRefIdSummaryDto>>.Failure("TenantId is required");
            }

            // Validate pagination parameters
            if (request.PageNumber < 1)
            {
                return Result<PaginatedResult<ObjectValuesRefIdSummaryDto>>.Failure("PageNumber must be greater than 0");
            }

            if (request.PageSize < 1 || request.PageSize > 1000)
            {
                return Result<PaginatedResult<ObjectValuesRefIdSummaryDto>>.Failure("PageSize must be between 1 and 1000");
            }

            var result = await _objectValuesRepository.GetRefIdsByObjectIdAsync(
                request.ObjectId,
                tenantId,
                request.SearchTerm,
                request.OnlyActive,
                request.PageNumber,
                request.PageSize,
                request.OrderBy,
                request.OrderDirection,
                cancellationToken);

            _logger.LogInformation("Successfully retrieved {Count} RefIds for ObjectId {ObjectId} (Page {PageNumber}/{TotalPages})",
                result.Data?.Count ?? 0, request.ObjectId, result.Page, result.TotalPages);

            return Result<PaginatedResult<ObjectValuesRefIdSummaryDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting RefIds for ObjectId {ObjectId}", request.ObjectId);
            return Result<PaginatedResult<ObjectValuesRefIdSummaryDto>>.Failure("An error occurred while retrieving RefIds");
        }
    }
}
