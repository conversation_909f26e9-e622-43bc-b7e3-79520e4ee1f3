using Domain.MultiTenancy;
using Finbuckle.MultiTenant.Stores;
using Microsoft.EntityFrameworkCore;
using Shared.Database;

namespace Infrastructure.MultiTenancy;

/// <summary>
/// Database context for tenant information
/// </summary>
public class TenantDbContext : EFCoreStoreDbContext<AppTenantInfo>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public TenantDbContext(DbContextOptions<TenantDbContext> options)
        : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Entity<AppTenantInfo>().ToTable("Tenants", SchemaNames.MultiTenancy);
    }
}
