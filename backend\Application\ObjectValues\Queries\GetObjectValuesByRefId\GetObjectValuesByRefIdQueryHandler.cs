using Abstraction.Common;
using Application.ObjectValues.DTOs;
using Application.ObjectValues.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.ObjectValues.Queries.GetObjectValuesByRefId;

/// <summary>
/// Handler for GetObjectValuesByRefIdQuery
/// </summary>
public class GetObjectValuesByRefIdQueryHandler : IRequestHandler<GetObjectValuesByRefIdQuery, Result<ObjectValuesResponseDto>>
{
    private readonly IObjectValuesRepository _objectValuesRepository;
    private readonly ICurrentUser _currentUserService;
    private readonly ILogger<GetObjectValuesByRefIdQueryHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="objectValuesRepository">ObjectValues repository</param>
    /// <param name="currentUserService">Current user service</param>
    /// <param name="logger">Logger</param>
    public GetObjectValuesByRefIdQueryHandler(
        IObjectValuesRepository objectValuesRepository,
        ICurrentUser currentUserService,
        ILogger<GetObjectValuesByRefIdQueryHandler> logger)
    {
        _objectValuesRepository = objectValuesRepository;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    /// <param name="request">Query request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>ObjectValues response or error</returns>
    public async Task<Result<ObjectValuesResponseDto>> Handle(GetObjectValuesByRefIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting ObjectValues for RefId {RefId}", request.RefId);

            var tenantId = _currentUserService.GetTenant();
            if (string.IsNullOrEmpty(tenantId))
            {
                _logger.LogWarning("TenantId is null or empty for RefId {RefId}", request.RefId);
                return Result<ObjectValuesResponseDto>.Failure("TenantId is required");
            }

            var objectValues = await _objectValuesRepository.GetByRefIdAsync(
                request.RefId,
                tenantId,
                request.IncludeInactive,
                cancellationToken);

            if (objectValues == null)
            {
                _logger.LogInformation("No ObjectValues found for RefId {RefId} in tenant {TenantId}", 
                    request.RefId, tenantId);
                return Result<ObjectValuesResponseDto>.Failure($"ObjectValues not found for RefId {request.RefId}");
            }

            // Filter by visibility if requested
            if (request.OnlyVisible)
            {
                var visibleValues = objectValues.Values
                    .Where(kvp => kvp.Value.IsVisible)
                    .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                objectValues.Values = visibleValues;
            }

            _logger.LogInformation("Successfully retrieved ObjectValues for RefId {RefId} with {ValueCount} values", 
                request.RefId, objectValues.Values.Count);

            return Result<ObjectValuesResponseDto>.Success(objectValues);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting ObjectValues for RefId {RefId}", request.RefId);
            return Result<ObjectValuesResponseDto>.Failure("An error occurred while retrieving ObjectValues");
        }
    }
}
