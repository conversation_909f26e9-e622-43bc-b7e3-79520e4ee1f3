using Abstraction.Common;
using Application.Features.DTOs;
using Application.Features.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;
using System.Diagnostics;

namespace Application.Features.Commands.CreateFeatureWithNestedData;

/// <summary>
/// Handler for creating features with deeply nested data
/// </summary>
public class CreateFeatureWithNestedDataCommandHandler : IRequestHandler<CreateFeatureWithNestedDataCommand, Result<CreateFeatureWithNestedDataResponseDto>>
{
    private readonly INestedFeatureCreationService _nestedFeatureService;
    private readonly ICurrentUser _currentUser;
    private readonly ILogger<CreateFeatureWithNestedDataCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateFeatureWithNestedDataCommandHandler(
        INestedFeatureCreationService nestedFeatureService,
        ICurrentUser currentUser,
        ILogger<CreateFeatureWithNestedDataCommandHandler> logger)
    {
        _nestedFeatureService = nestedFeatureService;
        _currentUser = currentUser;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<CreateFeatureWithNestedDataResponseDto>> Handle(
        CreateFeatureWithNestedDataCommand request,
        CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Starting creation of feature with nested data: {FeatureName}", request.Feature.Name);

            // Get tenant and user context
            var tenantId = _currentUser.GetTenant();
            if (string.IsNullOrEmpty(tenantId))
            {
                return Result<CreateFeatureWithNestedDataResponseDto>.Failure("Tenant context is required");
            }
            var userId = Guid.Empty.ToString();
            // Validate input
            var validationResult = ValidateRequest(request.Feature);
            if (!validationResult.Succeeded)
            {
                return Result<CreateFeatureWithNestedDataResponseDto>.Failure(validationResult.Message ?? "Invalid request");
            }

            // Create feature with nested data
            var result = await _nestedFeatureService.CreateFeatureWithNestedDataAsync(
                request.Feature,
                tenantId,
                userId,
                cancellationToken);

            if (!result.Succeeded)
            {
                _logger.LogError("Failed to create feature with nested data: {Error}", result.Message);
                return Result<CreateFeatureWithNestedDataResponseDto>.Failure(result.Message ?? "Failed to create feature");
            }

            stopwatch.Stop();
            result.Data!.ProcessingSummary.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;

            _logger.LogInformation("Successfully created feature with nested data: {FeatureName} in {ElapsedMs}ms",
                request.Feature.Name, stopwatch.ElapsedMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error creating feature with nested data: {FeatureName}", request.Feature.Name);
            return Result<CreateFeatureWithNestedDataResponseDto>.Failure("An error occurred while creating the feature with nested data");
        }
    }

    /// <summary>
    /// Validate the request
    /// </summary>
    private static Result<bool> ValidateRequest(FeatureCreationDto feature)
    {
        if (string.IsNullOrWhiteSpace(feature.Name))
        {
            return Result<bool>.Failure("Feature name is required");
        }

        if (feature.ProductId == Guid.Empty)
        {
            return Result<bool>.Failure("Product ID is required");
        }

        // Validate objects
        foreach (var obj in feature.Objects)
        {
            if (string.IsNullOrWhiteSpace(obj.Name))
            {
                return Result<bool>.Failure("Object name is required for all objects");
            }

            // Validate child objects
            foreach (var childObj in obj.ChildObjects)
            {
                if (string.IsNullOrWhiteSpace(childObj.Name))
                {
                    return Result<bool>.Failure("Child object name is required for all child objects");
                }
            }
        }

        return Result<bool>.Success(true);
    }
}
