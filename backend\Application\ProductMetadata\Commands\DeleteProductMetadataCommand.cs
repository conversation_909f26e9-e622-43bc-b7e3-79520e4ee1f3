using MediatR;
using Shared.Common.Response;

namespace Application.ProductMetadata.Commands;

/// <summary>
/// Delete ProductMetadata command
/// </summary>
public class DeleteProductMetadataCommand : IRequest<Result<bool>>
{
    /// <summary>
    /// ProductMetadata ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteProductMetadataCommand(Guid id)
    {
        Id = id;
    }
}
