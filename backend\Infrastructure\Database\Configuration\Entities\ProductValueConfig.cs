using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for ProductValue entity
/// </summary>
public class ProductValueConfig : IEntityTypeConfiguration<ProductValue>
{
    public void Configure(EntityTypeBuilder<ProductValue> builder)
    {
        builder.ToTable("ProductValues", "Genp");

        // Primary key
        builder.HasKey(e => e.Id);

        // Properties
        builder.Property(e => e.ProductMetadataId)
            .IsRequired();

        builder.Property(e => e.RefId);

        builder.Property(e => e.Value)
            .HasColumnType("TEXT");

        builder.Property(e => e.CreatedAt)
            .HasDefaultValueSql("NOW()")
            .IsRequired();

        builder.Property(e => e.CreatedBy);

        builder.Property(e => e.ModifiedAt)
            .HasDefaultValueSql("NOW()")
            .IsRequired();

        builder.Property(e => e.ModifiedBy);

        // Indexes
        builder.HasIndex(e => e.ProductMetadataId)
            .HasDatabaseName("IX_ProductValues_ProductMetadataId");

        builder.HasIndex(e => e.RefId)
            .HasDatabaseName("IX_ProductValues_RefId");

        builder.HasIndex(e => e.CreatedAt)
            .HasDatabaseName("IX_ProductValues_CreatedAt");

        // Multi-tenancy
        builder.IsMultiTenant();

        // Relationships
        builder.HasOne(e => e.ProductMetadata)
            .WithMany(e => e.ProductValues)
            .HasForeignKey(e => e.ProductMetadataId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
