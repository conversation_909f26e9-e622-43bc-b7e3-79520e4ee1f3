using Ardalis.Specification;
using Domain.Entities;

namespace Application.DataTypes.Specifications;

/// <summary>
/// Specification to count DataTypes with filters
/// </summary>
public class DataTypesCountSpec : Specification<DataType>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public DataTypesCountSpec(string? searchTerm, string? category, bool? isActive)
    {
        Query.Where(dt => !dt.IsDeleted);

        if (!string.IsNullOrEmpty(searchTerm))
        {
            Query.Where(dt => dt.Name.Contains(searchTerm) ||
                             dt.DisplayName.Contains(searchTerm) ||
                             (dt.Category != null && dt.Category.Contains(searchTerm)));
        }

        if (!string.IsNullOrEmpty(category))
        {
            Query.Where(dt => dt.Category == category);
        }

        if (isActive.HasValue)
        {
            Query.Where(dt => dt.IsActive == isActive.Value);
        }
    }
}
