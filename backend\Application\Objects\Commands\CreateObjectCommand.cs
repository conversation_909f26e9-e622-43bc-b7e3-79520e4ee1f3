using Application.Objects.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Objects.Commands;

/// <summary>
/// Create Object command
/// </summary>
public class CreateObjectCommand : IRequest<Result<ObjectDto>>
{
    /// <summary>
    /// Feature ID this object belongs to
    /// </summary>
    public Guid FeatureId { get; set; }

    /// <summary>
    /// Parent object ID for hierarchical structure
    /// </summary>
    public Guid? ParentObjectId { get; set; }

    /// <summary>
    /// Object name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Object description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Whether the object is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
