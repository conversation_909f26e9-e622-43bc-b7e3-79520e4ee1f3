{"operationType": 5, "includeFeatures": true, "includeObjects": true, "includeMetadata": true, "productData": [{"name": "Test Product with Flat Properties", "description": "Testing flat property processing", "version": "1.0.0", "isActive": true, "features": [{"name": "Test Feature", "description": "Feature with objects containing flat properties", "isDefault": true, "isActive": true, "objects": [{"name": "Test Object", "description": "Object with flat properties", "isActive": true, "code": "TEST001", "location": "Test Location", "price": 100000, "status": "Available", "area": 1200.5, "isVip": true}]}]}]}