using FluentValidation;

namespace Application.DataTypes.Commands;

/// <summary>
/// Validator for bulk create data types command
/// </summary>
public class BulkCreateDataTypesCommandValidator : AbstractValidator<BulkCreateDataTypesCommand>
{
    public BulkCreateDataTypesCommandValidator()
    {
        RuleFor(x => x.DataTypes)
            .NotNull()
            .WithMessage("DataTypes list cannot be null")
            .NotEmpty()
            .WithMessage("DataTypes list cannot be empty")
            .Must(x => x.Count <= 1000)
            .WithMessage("Cannot create more than 1000 data types in a single bulk operation");

        RuleForEach(x => x.DataTypes)
            .NotNull()
            .WithMessage("Individual data type commands cannot be null");

        // Validate for duplicate names within the request
        RuleFor(x => x.DataTypes)
            .Must(HaveUniqueNames)
            .WithMessage("Duplicate data type names found within the request. Each data type must have a unique name.");
    }

    private static bool HaveUniqueNames(List<CreateDataTypeCommand> dataTypes)
    {
        if (dataTypes == null || !dataTypes.Any())
            return true;

        var names = dataTypes
            .Where(dt => !string.IsNullOrWhiteSpace(dt.Name))
            .Select(dt => dt.Name!.Trim().ToLowerInvariant())
            .ToList();

        return names.Count == names.Distinct().Count();
    }
}
