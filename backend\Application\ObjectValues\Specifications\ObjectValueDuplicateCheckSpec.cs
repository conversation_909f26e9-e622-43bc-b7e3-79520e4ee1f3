using Ardalis.Specification;
using Domain.Entities;

namespace Application.ObjectValues.Specifications;

/// <summary>
/// Specification to check for duplicate ObjectValues for unique constraint validation
/// </summary>
public class ObjectValueDuplicateCheckSpec : Specification<ObjectValue>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ObjectValueDuplicateCheckSpec(Guid objectId, Guid metadataId, string value, Guid? excludeRefId = null)
    {
        Query.Where(ov => ov.ObjectMetadata.ObjectId == objectId &&
                         ov.ObjectMetadata.MetadataId == metadataId &&
                         ov.Value == value);

        // Exclude specific RefId if provided (for updates)
        if (excludeRefId.HasValue)
        {
            Query.Where(ov => ov.RefId != excludeRefId.Value);
        }

        // Include metadata for checks
        Query.Include(ov => ov.ObjectMetadata);

        // Take only one record for existence check
        Query.Take(1);
    }
}
