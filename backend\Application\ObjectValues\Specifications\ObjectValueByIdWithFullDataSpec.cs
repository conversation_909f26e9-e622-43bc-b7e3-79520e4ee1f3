using Ardalis.Specification;
using Domain.Entities;

namespace Application.ObjectValues.Specifications;

/// <summary>
/// Specification to get ObjectValue by ID with full metadata information
/// </summary>
public class ObjectValueByIdWithFullDataSpec : Specification<ObjectValue>, ISingleResultSpecification<ObjectValue>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ObjectValueByIdWithFullDataSpec(Guid id)
    {
        Query.Where(ov => ov.Id == id && !ov.IsDeleted);

        // Include all related data
        Query.Include(ov => ov.ObjectMetadata)
             .ThenInclude(om => om.Object);

        Query.Include(ov => ov.ObjectMetadata)
             .ThenInclude(om => om.Metadata)
             .ThenInclude(m => m.DataType);

        // Additional filters for active records
        Query.Where(ov => ov.ObjectMetadata.Object.IsActive && 
                         !ov.ObjectMetadata.Object.IsDeleted &&
                         ov.ObjectMetadata.IsActive && 
                         !ov.ObjectMetadata.IsDeleted);
    }
}
