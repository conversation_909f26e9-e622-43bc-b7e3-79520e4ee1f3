using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Actual subscription instance data
/// </summary>
public class SubscriptionValue : AuditableEntity, IAggregateRoot
{

    /// <summary>
    /// Subscription metadata ID
    /// </summary>
    public Guid SubscriptionMetadataId { get; set; }

    /// <summary>
    /// Reference ID for grouping related values
    /// </summary>
    public Guid? RefId { get; set; }

    /// <summary>
    /// The actual value as text
    /// </summary>
    public string? Value { get; set; }

    // Navigation properties
    /// <summary>
    /// Subscription metadata link
    /// </summary>
    public virtual SubscriptionMetadata SubscriptionMetadata { get; set; } = null!;
}
