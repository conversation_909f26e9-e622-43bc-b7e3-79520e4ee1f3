using Application.MetadataManagement.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.MetadataManagement.Queries;

/// <summary>
/// Get Metadata by ID query
/// </summary>
public class GetMetadataByIdQuery : IRequest<Result<MetadataDto>>
{
    /// <summary>
    /// Metadata ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public GetMetadataByIdQuery(Guid id)
    {
        Id = id;
    }
}
