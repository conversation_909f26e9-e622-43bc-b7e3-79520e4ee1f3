using Ardalis.Specification;
using Domain.Entities;

namespace Application.ObjectValues.Specifications;

/// <summary>
/// Specification to get ObjectMetadata by ObjectId and MetadataId
/// </summary>
public class ObjectMetadataByObjectAndMetadataIdSpec : Specification<ObjectMetadata>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ObjectMetadataByObjectAndMetadataIdSpec(Guid objectId, Guid metadataId)
    {
        Query.Where(om => om.ObjectId == objectId && om.MetadataId == metadataId);

        // Include related data
        Query.Include(om => om.Metadata);
        Query.Include(om => om.Object);

        // Take only one record
        Query.Take(1);
    }
}
