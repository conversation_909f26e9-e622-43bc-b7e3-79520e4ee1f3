# Test script for Dynamic Operations API
# Make sure the backend is running on localhost:5018

$baseUrl = "http://localhost:5018"
$headers = @{
    "Content-Type" = "application/json"
    "tenant" = "black"
    "Accept" = "*/*"
}

Write-Host "Testing Dynamic Operations API..." -ForegroundColor Green

# Test 1: Get supported entities
Write-Host "`n1. Testing GET supported entities..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/dynamicoperations/supported-entities" -Method GET -Headers $headers
    Write-Host "✅ Supported entities retrieved successfully" -ForegroundColor Green
    Write-Host "Core Entities: $($response.CoreEntities -join ', ')" -ForegroundColor Cyan
    Write-Host "Value Entities: $($response.ValueEntities -join ', ')" -ForegroundColor Cyan
}
catch {
    Write-Host "❌ Failed to get supported entities: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Single Product Insert
Write-Host "`n2. Testing single product insert..." -ForegroundColor Yellow
$productData = @{
    operationType = 0  # Insert
    productData = @(
        @{
            name = "Test Apartment Complex"
            description = "Test apartment complex for dynamic operations"
            version = "1.0.0"
            isActive = $true
        }
    )
    includeFeatures = $false
    includeObjects = $false
    includeMetadata = $false
} | ConvertTo-Json -Depth 3

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/dynamicoperations/products" -Method POST -Headers $headers -Body $productData
    Write-Host "✅ Product insert successful" -ForegroundColor Green
    Write-Host "Response: $($response | ConvertTo-Json -Depth 2)" -ForegroundColor Cyan
}
catch {
    Write-Host "❌ Product insert failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorBody = $reader.ReadToEnd()
        Write-Host "Error details: $errorBody" -ForegroundColor Red
    }
}

# Test 3: Bulk Product Upsert
Write-Host "`n3. Testing bulk product upsert..." -ForegroundColor Yellow
$bulkData = @{
    entityType = "Product"
    operationType = 5  # BulkUpsert
    propertyValuesList = @(
        @{
            name = "Bulk Product 1"
            description = "First bulk product"
            version = "1.0.0"
            isActive = $true
        },
        @{
            name = "Bulk Product 2"
            description = "Second bulk product"
            version = "1.0.0"
            isActive = $true
        }
    )
    keyProperties = @("name")
    batchSize = 1000
} | ConvertTo-Json -Depth 3

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/dynamicoperations/bulk-execute" -Method POST -Headers $headers -Body $bulkData
    Write-Host "✅ Bulk product upsert successful" -ForegroundColor Green
    Write-Host "Response: $($response | ConvertTo-Json -Depth 2)" -ForegroundColor Cyan
}
catch {
    Write-Host "❌ Bulk product upsert failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorBody = $reader.ReadToEnd()
        Write-Host "Error details: $errorBody" -ForegroundColor Red
    }
}

# Test 4: Single Dynamic Operation
Write-Host "`n4. Testing single dynamic operation..." -ForegroundColor Yellow
$singleData = @{
    entityType = "Product"
    operationType = 0  # Insert
    propertyValues = @{
        name = "Single Dynamic Product"
        description = "Product created via single dynamic operation"
        version = "1.0.0"
        isActive = $true
    }
    keyProperties = @()
} | ConvertTo-Json -Depth 3

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/dynamicoperations/execute" -Method POST -Headers $headers -Body $singleData
    Write-Host "✅ Single dynamic operation successful" -ForegroundColor Green
    Write-Host "Response: $($response | ConvertTo-Json -Depth 2)" -ForegroundColor Cyan
}
catch {
    Write-Host "❌ Single dynamic operation failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorBody = $reader.ReadToEnd()
        Write-Host "Error details: $errorBody" -ForegroundColor Red
    }
}

Write-Host "`n🎉 Dynamic Operations API testing completed!" -ForegroundColor Green
Write-Host "Check the results above to see which operations succeeded." -ForegroundColor Yellow
