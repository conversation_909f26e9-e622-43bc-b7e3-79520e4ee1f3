﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Migrators.Migrations.Application
{
    /// <inheritdoc />
    public partial class ProductDetails : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ApiK<PERSON>",
                schema: "Genp",
                table: "Products",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ApplicationUrl",
                schema: "Genp",
                table: "Products",
                type: "character varying(2000)",
                maxLength: 2000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Icon",
                schema: "Genp",
                table: "Products",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsOnboardCompleted",
                schema: "Genp",
                table: "Products",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsRoleAssigned",
                schema: "Genp",
                table: "Products",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsUserImported",
                schema: "Genp",
                table: "Products",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ApiKey",
                schema: "Genp",
                table: "Products");

            migrationBuilder.DropColumn(
                name: "ApplicationUrl",
                schema: "Genp",
                table: "Products");

            migrationBuilder.DropColumn(
                name: "Icon",
                schema: "Genp",
                table: "Products");

            migrationBuilder.DropColumn(
                name: "IsOnboardCompleted",
                schema: "Genp",
                table: "Products");

            migrationBuilder.DropColumn(
                name: "IsRoleAssigned",
                schema: "Genp",
                table: "Products");

            migrationBuilder.DropColumn(
                name: "IsUserImported",
                schema: "Genp",
                table: "Products");
        }
    }
}
