using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Links Product types to their metadata with IsUnique support
/// </summary>
public class ProductMetadata : AuditableEntity, IAggregateRoot
{
    /// <summary>
    /// Product ID
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Metadata ID
    /// </summary>
    public Guid MetadataId { get; set; }

    /// <summary>
    /// Whether this metadata is unique for the product
    /// </summary>
    public bool IsUnique { get; set; } = false;

    /// <summary>
    /// Whether this metadata link is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// ShouldVisibleInList
    /// </summary>
    public bool ShouldVisibleInList { get; set; } = true;

    /// <summary>
    /// ShouldVisibleInList
    /// </summary>
    public bool ShouldVisibleInEdit { get; set; } = true;

    /// <summary>
    /// ShouldVisibleInList
    /// </summary>
    public bool ShouldVisibleInCreate { get; set; } = true;

    /// <summary>
    /// ShouldVisibleInList
    /// </summary>
    public bool ShouldVisibleInView { get; set; } = true;

    /// <summary>
    /// ShouldVisibleInList
    /// </summary>
    public bool IsCalculate { get; set; } = true;

    // Navigation properties
    /// <summary>
    /// Product
    /// </summary>
    public virtual Product Product { get; set; } = null!;

    /// <summary>
    /// Metadata definition
    /// </summary>
    public virtual Metadata Metadata { get; set; } = null!;

    /// <summary>
    /// Product values
    /// </summary>
    public virtual ICollection<ProductValue> ProductValues { get; set; } = new List<ProductValue>();
}
