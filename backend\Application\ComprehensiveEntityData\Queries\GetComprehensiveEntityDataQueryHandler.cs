using Abstraction.Database.Repositories;
using Application.ComprehensiveEntityData.DTOs;
using Domain.Views;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.ComprehensiveEntityData.Queries;

/// <summary>
/// Get comprehensive entity data query handler
/// </summary>
public class GetComprehensiveEntityDataQueryHandler : IRequestHandler<GetComprehensiveEntityDataQuery, Result<ComprehensiveEntityDataResponseDto>>
{
    private readonly IComprehensiveEntityDataRepository _repository;
    private readonly ILogger<GetComprehensiveEntityDataQueryHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetComprehensiveEntityDataQueryHandler(
        IComprehensiveEntityDataRepository repository,
        ILogger<GetComprehensiveEntityDataQueryHandler> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<ComprehensiveEntityDataResponseDto>> Handle(
        GetComprehensiveEntityDataQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing comprehensive entity data query with filters: ProductId={ProductId}, FeatureId={FeatureId}, ObjectId={ObjectId}",
                request.ProductId, request.FeatureId, request.ObjectId);

            // Get data from repository
            var viewData = await _repository.GetComprehensiveEntityDataAsync(
                request.ProductId,
                request.FeatureId,
                request.ObjectId,
                request.SearchTerm,
                request.IsActive,
                request.OnlyVisibleMetadata,
                request.OnlyActiveMetadata,
                request.PageNumber,
                request.PageSize,
                request.OrderBy,
                request.OrderDirection,
                cancellationToken);

            // Transform flat data into hierarchical structure
            var response = TransformToHierarchicalStructure(viewData);

            _logger.LogInformation("Successfully processed comprehensive entity data query, returning {ProductCount} products",
                response.Products.Count);

            return Result<ComprehensiveEntityDataResponseDto>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing comprehensive entity data query");
            return Result<ComprehensiveEntityDataResponseDto>.Failure("An error occurred while retrieving comprehensive entity data");
        }
    }

    /// <summary>
    /// Transform flat view data into hierarchical structure
    /// </summary>
    private ComprehensiveEntityDataResponseDto TransformToHierarchicalStructure(
        IEnumerable<VwComprehensiveEntityData> viewData)
    {
        var response = new ComprehensiveEntityDataResponseDto();

        // Group by Product
        var productGroups = viewData
            .Where(x => x.ProductId != Guid.Empty)
            .GroupBy(x => x.ProductId)
            .ToList();

        foreach (var productGroup in productGroups)
        {
            var productData = productGroup.First();
            var productDto = new ProductComprehensiveDto
            {
                ProductInfo = new ProductInfoDto
                {
                    Id = productData.ProductId,
                    TenantId = productData.TenantId,
                    Name = productData.ProductName,
                    Description = productData.ProductDescription,
                    Version = productData.ProductVersion,
                    IsActive = productData.ProductIsActive
                }
            };

            // Add product metadata
            var productMetadata = productGroup
                .Where(x => x.ProductMetadataId.HasValue && x.MetadataId.HasValue)
                .GroupBy(x => x.MetadataId)
                .Select(CreateMetadataWithValues)
                .ToList();
            productDto.ProductMetadata = productMetadata;

            // Group by Feature
            var featureGroups = productGroup
                .Where(x => x.FeatureId.HasValue)
                .GroupBy(x => x.FeatureId)
                .ToList();

            foreach (var featureGroup in featureGroups)
            {
                var featureData = featureGroup.First();
                var featureDto = new FeatureComprehensiveDto
                {
                    FeatureInfo = new FeatureInfoDto
                    {
                        Id = featureData.FeatureId!.Value,
                        Name = featureData.FeatureName,
                        Description = featureData.FeatureDescription,
                        IsActive = featureData.FeatureIsActive ?? false
                    }
                };

                // Add feature metadata
                var featureMetadata = featureGroup
                    .Where(x => x.FeatureMetadataId.HasValue && x.MetadataId.HasValue)
                    .GroupBy(x => x.MetadataId)
                    .Select(CreateMetadataWithValues)
                    .ToList();
                featureDto.FeatureMetadata = featureMetadata;

                // Group by Object
                var objectGroups = featureGroup
                    .Where(x => x.ObjectId.HasValue)
                    .GroupBy(x => x.ObjectId)
                    .ToList();

                foreach (var objectGroup in objectGroups)
                {
                    var objectDto = CreateObjectComprehensive(objectGroup);
                    featureDto.Objects.Add(objectDto);
                }

                productDto.Features.Add(featureDto);
            }

            response.Products.Add(productDto);
        }

        return response;
    }

    /// <summary>
    /// Create object comprehensive DTO
    /// </summary>
    private ObjectComprehensiveDto CreateObjectComprehensive(
        IGrouping<Guid?, VwComprehensiveEntityData> objectGroup)
    {
        var objectData = objectGroup.First();
        var objectDto = new ObjectComprehensiveDto
        {
            ObjectInfo = new ObjectInfoDto
            {
                Id = objectData.ObjectId!.Value,
                ParentObjectId = objectData.ParentObjectId,
                Name = objectData.ObjectName,
                Description = objectData.ObjectDescription,
                IsActive = objectData.ObjectIsActive ?? false
            }
        };

        // Add parent object info if exists
        if (objectData.ParentObjectId.HasValue)
        {
            objectDto.ParentObject = new ParentObjectInfoDto
            {
                Id = objectData.ParentObjectId.Value,
                Name = objectData.ParentObjectName,
                Description = objectData.ParentObjectDescription
            };
        }

        // Add object metadata
        var objectMetadata = objectGroup
            .Where(x => x.ObjectMetadataId.HasValue && x.MetadataId.HasValue)
            .GroupBy(x => x.MetadataId)
            .Select(CreateMetadataWithValues)
            .ToList();
        objectDto.ObjectMetadata = objectMetadata;

        return objectDto;
    }

    /// <summary>
    /// Create metadata with values DTO
    /// </summary>
    private MetadataWithValuesDto CreateMetadataWithValues(IGrouping<Guid?, VwComprehensiveEntityData> metadataGroup)
    {
        var metadataData = metadataGroup.First();

        // Create the main metadata object
        var metadataInfo = new MetadataInfoDto
        {
            Id = metadataData.MetadataId!.Value,
            MetadataKey = metadataData.MetadataKey,
            DisplayLabel = metadataData.DisplayLabel,
            HelpText = metadataData.HelpText,
            FieldOrder = metadataData.FieldOrder,
            IsVisible = metadataData.IsVisible,
            IsReadonly = metadataData.IsReadonly,
            CustomPlaceholder = metadataData.CustomPlaceholder,
            CustomOptions = metadataData.CustomOptions,
            CustomIsRequired = metadataData.CustomIsRequired
        };

        // Add nested data type info if exists
        if (metadataData.DataTypeId.HasValue)
        {
            metadataInfo.DataType = new DataTypeInfoDto
            {
                Id = metadataData.DataTypeId.Value,
                Name = metadataData.DataTypeName,
                DisplayName = metadataData.DataTypeDisplayName,
                Category = metadataData.DataTypeCategory,
                UiComponent = metadataData.UiComponent,
                InputType = metadataData.InputType,
                InputMask = metadataData.InputMask,
                Placeholder = metadataData.Placeholder,
                DefaultOptions = metadataData.DefaultOptions,
                ValidationPattern = metadataData.ValidationPattern,
                MinLength = metadataData.MinLength,
                MaxLength = metadataData.MaxLength,
                MinValue = metadataData.MinValue,
                MaxValue = metadataData.MaxValue,
                DecimalPlaces = metadataData.DecimalPlaces,
                StepValue = metadataData.StepValue,
                IsRequired = metadataData.IsRequired,
                AllowsMultiple = metadataData.AllowsMultiple,
                AllowsCustomOptions = metadataData.AllowsCustomOptions,
                HtmlAttributes = metadataData.HtmlAttributes
            };
        }

        // Add nested metadata link info based on which metadata link is active
        if (metadataData.ProductMetadataId.HasValue)
        {
            metadataInfo.MetadataLink = new MetadataLinkInfoDto
            {
                Id = metadataData.ProductMetadataId.Value,
                IsUnique = metadataData.ProductMetadataIsUnique ?? false,
                IsActive = metadataData.ProductMetadataIsActive ?? false
            };
        }
        else if (metadataData.FeatureMetadataId.HasValue)
        {
            metadataInfo.MetadataLink = new MetadataLinkInfoDto
            {
                Id = metadataData.FeatureMetadataId.Value,
                IsUnique = metadataData.FeatureMetadataIsUnique ?? false,
                IsActive = metadataData.FeatureMetadataIsActive ?? false
            };
        }
        else if (metadataData.ObjectMetadataId.HasValue)
        {
            metadataInfo.MetadataLink = new MetadataLinkInfoDto
            {
                Id = metadataData.ObjectMetadataId.Value,
                IsUnique = metadataData.ObjectMetadataIsUnique ?? false,
                IsActive = metadataData.ObjectMetadataIsActive ?? false
            };
        }

        // Create the wrapper DTO with the nested metadata
        var metadataDto = new MetadataWithValuesDto
        {
            Metadata = metadataInfo
        };

        // Add values
        var values = metadataGroup
            .Where(x => (x.ProductValueId.HasValue || x.FeatureValueId.HasValue))
            .Select(x => new ValueInfoDto
            {
                Id = x.ProductValueId ?? x.FeatureValueId ?? Guid.Empty,
                RefId = x.ProductValueRefId ?? x.FeatureValueRefId,
                Value = x.ProductValue ?? x.FeatureValue
            })
            .Where(x => x.Id != Guid.Empty)
            .ToList();
        metadataDto.Values = values;

        return metadataDto;
    }
}
