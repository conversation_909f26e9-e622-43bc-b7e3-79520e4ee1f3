using Application.Subscriptions.DTOs;
using Application.Subscriptions.Queries;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;

namespace Web.Host.Controllers;

/// <summary>
/// Subscriptions controller
/// </summary>
[Route("api/[controller]")]
public class SubscriptionsController : BaseApiController
{
    /// <summary>
    /// Get all subscriptions with pagination
    /// </summary>
    [HttpGet]
    [TenantIdHeader]
    public async Task<ActionResult<PaginatedResult<SubscriptionDto>>> GetSubscriptionsAsync([FromQuery] GetSubscriptionsQuery query)
    {
        return Ok(await Mediator.Send(query));
    }
}
