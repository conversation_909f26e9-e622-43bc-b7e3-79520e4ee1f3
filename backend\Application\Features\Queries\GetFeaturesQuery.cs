using Application.Features.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Features.Queries;

/// <summary>
/// Get features query
/// </summary>
public class GetFeaturesQuery : IRequest<PaginatedResult<FeatureDto>>
{
    /// <summary>
    /// Page number
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Search term
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Filter by active status
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Filter by product ID
    /// </summary>
    public Guid? ProductId { get; set; }
}
