namespace Application.Products.DTOs;

/// <summary>
/// Product DTO
/// </summary>
public class ProductDto
{
    /// <summary>
    /// Product ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Product name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Product description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Product version
    /// </summary>
    public string? Version { get; set; }

    /// <summary>
    /// Whether the product is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Whether user has been imported
    /// </summary>
    public bool IsUserImported { get; set; }

    /// <summary>
    /// Whether role has been assigned
    /// </summary>
    public bool IsRoleAssigned { get; set; }

    /// <summary>
    /// API key for the product
    /// </summary>
    public string? ApiKey { get; set; }

    /// <summary>
    /// Whether onboarding process is completed
    /// </summary>
    public bool IsOnboardCompleted { get; set; }

    /// <summary>
    /// Application URL for the product
    /// </summary>
    public string? ApplicationUrl { get; set; }

    /// <summary>
    /// Created at
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by
    /// </summary>
    public Guid CreatedBy { get; set; }

    /// <summary>
    /// Modified at
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Modified by
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}
