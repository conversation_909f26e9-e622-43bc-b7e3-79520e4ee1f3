using Application.Products.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Products.Commands;

/// <summary>
/// Create product with subscription command - creates both Product and Subscription in a single transaction
/// </summary>
public class CreateProductWithSubscriptionCommand : IRequest<Result<ProductWithSubscriptionDto>>
{
    // Product properties
    /// <summary>
    /// Product name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Product description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Product version
    /// </summary>
    public string? Version { get; set; }

    /// <summary>
    /// Whether the product is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    // Subscription properties
    /// <summary>
    /// Subscription type
    /// </summary>
    public string? SubscriptionType { get; set; }

    /// <summary>
    /// Subscription end date
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Whether auto-renewal is enabled
    /// </summary>
    public bool AutoRenew { get; set; } = true;

    /// <summary>
    /// Pricing tier
    /// </summary>
    public string? PricingTier { get; set; }
}
