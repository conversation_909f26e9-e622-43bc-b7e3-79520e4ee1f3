using Abstraction.Repositories;
using Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.DataTypes.Commands;

/// <summary>
/// Handler for deleting a data type
/// </summary>
public class DeleteDataTypeCommandHandler : IRequestHandler<DeleteDataTypeCommand, Result<bool>>
{
    private readonly IDynamicRepository<DataType> _repository;
    private readonly ILogger<DeleteDataTypeCommandHandler> _logger;

    public DeleteDataTypeCommandHandler(
        IDynamicRepository<DataType> repository,
        ILogger<DeleteDataTypeCommandHandler> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    public async Task<Result<bool>> Handle(DeleteDataTypeCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Deleting data type with ID: {Id}", request.Id);

            // Find existing data type
            var existingDataType = await _repository.GetByIdAsync(request.Id);
            if (existingDataType == null)
            {
                _logger.LogWarning("Data type not found with ID: {Id}", request.Id);
                return Result<bool>.Failure(new List<string> { $"Data type with ID {request.Id} not found" });
            }

            // Delete the data type
            await _repository.DeleteAsync(request.Id);

            _logger.LogInformation("Successfully deleted data type: {Name} with ID: {Id}", existingDataType.Name, request.Id);

            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while deleting data type with ID: {Id}", request.Id);
            return Result<bool>.Failure(new List<string> { ex.Message });
        }
    }
}
