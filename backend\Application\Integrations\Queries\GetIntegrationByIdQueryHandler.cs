using Application.Integrations.DTOs;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.Integrations.Queries;

/// <summary>
/// Get integration by ID query handler
/// </summary>
public class GetIntegrationByIdQueryHandler : IRequestHandler<GetIntegrationByIdQuery, Result<IntegrationDto>>
{
    private readonly IReadRepository<Integration> _integrationRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetIntegrationByIdQueryHandler(IReadRepository<Integration> integrationRepository)
    {
        _integrationRepository = integrationRepository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<IntegrationDto>> Handle(GetIntegrationByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var integration = await _integrationRepository.GetByIdAsync(request.Id, cancellationToken);
            
            if (integration == null)
            {
                return Result<IntegrationDto>.Failure("Integration not found.");
            }

            var integrationDto = integration.Adapt<IntegrationDto>();
            return Result<IntegrationDto>.Success(integrationDto);
        }
        catch (Exception ex)
        {
            return Result<IntegrationDto>.Failure($"Failed to get integration: {ex.Message}");
        }
    }
}
