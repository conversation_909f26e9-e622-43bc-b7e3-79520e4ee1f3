namespace Application.Objects.DTOs;

/// <summary>
/// Object DTO
/// </summary>
public class ObjectDto
{
    /// <summary>
    /// Object ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Feature ID this object belongs to
    /// </summary>
    public Guid FeatureId { get; set; }

    /// <summary>
    /// Feature name
    /// </summary>
    public string? FeatureName { get; set; }

    /// <summary>
    /// Parent object ID for hierarchical structure
    /// </summary>
    public Guid? ParentObjectId { get; set; }

    /// <summary>
    /// Parent object name
    /// </summary>
    public string? ParentObjectName { get; set; }

    /// <summary>
    /// Object name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Object description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Whether the object is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Number of child objects
    /// </summary>
    public int ChildObjectsCount { get; set; }

    /// <summary>
    /// Number of metadata definitions
    /// </summary>
    public int MetadataCount { get; set; }

    /// <summary>
    /// Created at
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by
    /// </summary>
    public Guid CreatedBy { get; set; }

    /// <summary>
    /// Modified at
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Modified by
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}
