using System.ComponentModel.DataAnnotations;

namespace Application.ObjectMetadataManagement.DTOs;

/// <summary>
/// Create ObjectMetadata DTO
/// </summary>
public class CreateObjectMetadataDto
{
    /// <summary>
    /// Object ID
    /// </summary>
    [Required]
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Metadata ID
    /// </summary>
    [Required]
    public Guid MetadataId { get; set; }

    /// <summary>
    /// Whether this metadata is unique for the object
    /// </summary>
    public bool IsUnique { get; set; } = false;

    /// <summary>
    /// Whether this metadata link is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
