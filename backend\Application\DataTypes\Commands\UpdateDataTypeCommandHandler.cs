using Abstraction.Repositories;
using Application.DataTypes.DTOs;
using Domain.Entities;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.DataTypes.Commands;

/// <summary>
/// Handler for updating an existing data type
/// </summary>
public class UpdateDataTypeCommandHandler : IRequestHandler<UpdateDataTypeCommand, Result<DataTypeDto>>
{
    private readonly IDynamicRepository<DataType> _repository;
    private readonly ILogger<UpdateDataTypeCommandHandler> _logger;

    public UpdateDataTypeCommandHandler(
        IDynamicRepository<DataType> repository,
        ILogger<UpdateDataTypeCommandHandler> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    public async Task<Result<DataTypeDto>> Handle(UpdateDataTypeCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Updating data type: {Id} - {Name}", request.Id, request.Name);

            // Find existing data type
            var existingDataType = await _repository.GetByIdAsync(request.Id);
            if (existingDataType == null)
            {
                _logger.LogWarning("Data type not found with ID: {Id}", request.Id);
                return Result<DataTypeDto>.Failure(new List<string> { $"Data type with ID {request.Id} not found" });
            }

            // Check if another data type with same name already exists (excluding current one)
            var allDataTypes = await _repository.GetAllAsync();
            var duplicateDataType = allDataTypes.FirstOrDefault(dt => 
                dt.Id != request.Id &&
                !string.IsNullOrEmpty(dt.Name) && 
                dt.Name.Equals(request.Name, StringComparison.OrdinalIgnoreCase));

            if (duplicateDataType != null)
            {
                _logger.LogWarning("Another data type with name '{Name}' already exists", request.Name);
                return Result<DataTypeDto>.Failure(new List<string> { $"Another data type with name '{request.Name}' already exists" });
            }

            // Update the entity
            request.Adapt(existingDataType);
            existingDataType.ModifiedAt = DateTime.UtcNow;

            // Save changes
            var updatedDataType = await _repository.UpdateAsync(existingDataType);

            // Map to DTO
            var dataTypeDto = updatedDataType.Adapt<DataTypeDto>();

            _logger.LogInformation("Successfully updated data type: {Name} with ID: {Id}", request.Name, request.Id);

            return Result<DataTypeDto>.Success(dataTypeDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while updating data type: {Id} - {Name}", request.Id, request.Name);
            return Result<DataTypeDto>.Failure(new List<string> { ex.Message });
        }
    }
}
