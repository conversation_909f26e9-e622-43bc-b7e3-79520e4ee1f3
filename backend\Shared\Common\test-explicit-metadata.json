{"operationType": 5, "includeFeatures": true, "includeObjects": true, "includeMetadata": true, "productData": [{"name": "Test Product with Explicit Metadata", "description": "Testing explicit metadata and values", "version": "1.0.0", "isActive": true, "metadata": [{"metadataId": "product-category", "isUnique": false, "isActive": true}], "values": [{"metadataKey": "product-category", "value": "Test Category", "refId": "prod-001"}], "features": [{"name": "Test Feature", "description": "Feature with explicit metadata", "isDefault": true, "isActive": true, "metadata": [{"metadataId": "feature-priority", "isUnique": false, "isActive": true}], "values": [{"metadataKey": "feature-priority", "value": "High", "refId": "feat-001"}], "objects": [{"name": "Test Object", "description": "Object with explicit metadata", "isActive": true, "metadata": [{"metadataId": "object-status", "isUnique": false, "isActive": true}], "values": [{"metadataKey": "object-status", "value": "Active", "refId": "obj-001"}]}]}]}]}