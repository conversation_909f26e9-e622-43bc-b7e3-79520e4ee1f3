using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Links Subscription types to their metadata with IsUnique support
/// </summary>
public class SubscriptionMetadata : AuditableEntity, IAggregateRoot
{

    /// <summary>
    /// Subscription ID
    /// </summary>
    public Guid SubscriptionId { get; set; }

    /// <summary>
    /// Metadata ID
    /// </summary>
    public Guid MetadataId { get; set; }

    /// <summary>
    /// Whether this metadata is unique for the subscription
    /// </summary>
    public bool IsUnique { get; set; } = false;

    /// <summary>
    /// Whether this metadata link is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// ShouldVisibleInList
    /// </summary>
    public bool ShouldVisibleInList { get; set; } = true;

    /// <summary>
    /// ShouldVisibleInList
    /// </summary>
    public bool ShouldVisibleInEdit { get; set; } = true;

    /// <summary>
    /// ShouldVisibleInList
    /// </summary>
    public bool ShouldVisibleInCreate { get; set; } = true;

    /// <summary>
    /// ShouldVisibleInList
    /// </summary>
    public bool ShouldVisibleInView { get; set; } = true;

    /// <summary>
    /// ShouldVisibleInList
    /// </summary>
    public bool IsCalculate { get; set; } = true;

    // Navigation properties
    /// <summary>
    /// Subscription
    /// </summary>
    public virtual Subscription Subscription { get; set; } = null!;

    /// <summary>
    /// Metadata definition
    /// </summary>
    public virtual Metadata Metadata { get; set; } = null!;

    /// <summary>
    /// Subscription values
    /// </summary>
    public virtual ICollection<SubscriptionValue> SubscriptionValues { get; set; } = new List<SubscriptionValue>();
}
