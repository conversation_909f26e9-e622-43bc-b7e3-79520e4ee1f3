namespace Application.Features.DTOs;

/// <summary>
/// Feature DTO
/// </summary>
public class FeatureDto
{
    /// <summary>
    /// Feature ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Product ID
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Feature name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Feature description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Whether this is a default feature
    /// </summary>
    public bool IsDefault { get; set; }

    /// <summary>
    /// Whether the feature is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Created at
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by
    /// </summary>
    public Guid CreatedBy { get; set; }

    /// <summary>
    /// Modified at
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Modified by
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}
