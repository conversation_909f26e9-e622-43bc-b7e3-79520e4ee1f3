-- =============================================================================
-- ENTERPRISE MULTI-TENANT DATABASE SCHEMA - FINAL VERSION
-- Compatible with Finbuckle.MultiTenant
-- Schema: Genp (Generic Product)
-- Features: Type-based architecture, Metadata-driven, UUID PKs, TenantId isolation
-- Version: 1.0.0
-- Date: May 30, 2025
-- =============================================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "ltree";

-- Create the main schema
CREATE SCHEMA IF NOT EXISTS Genp;
SET search_path TO Genp, public;

-- =============================================================================
-- GLOBAL TABLES (NO TenantId - shared across all tenants)
-- =============================================================================

-- DataTypes table with comprehensive validation and UI properties
CREATE TABLE Genp.DataTypes (
    Id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    Name VARCHAR(50) NOT NULL UNIQUE,
    DisplayName VARCHAR(100) NOT NULL,
    Category VARCHAR(50) NOT NULL, -- 'primitive', 'formatted', 'choice', 'media', 'temporal', 'complex', 'interactive'
    UiComponent VARCHAR(50) NOT NULL,
    
    -- Validation Rules
    ValidationPattern VARCHAR(500),
    MinLength INTEGER,
    MaxLength INTEGER,
    MinValue DECIMAL,
    MaxValue DECIMAL,
    DecimalPlaces INTEGER,
    StepValue DECIMAL,
    IsRequired BOOLEAN DEFAULT FALSE,
    
    -- UI Properties
    InputType VARCHAR(50),
    InputMask VARCHAR(100),
    Placeholder VARCHAR(255),
    HtmlAttributes VARCHAR(500),
    
    -- Choice Options
    DefaultOptions TEXT, -- JSON or comma-separated
    AllowsMultiple BOOLEAN DEFAULT FALSE,
    AllowsCustomOptions BOOLEAN DEFAULT FALSE,
    MaxSelections INTEGER,
    
    -- File/Media Properties
    AllowedFileTypes VARCHAR(255),
    MaxFileSizeBytes BIGINT,
    
    -- Error Messages
    RequiredErrorMessage VARCHAR(255),
    PatternErrorMessage VARCHAR(255),
    MinLengthErrorMessage VARCHAR(255),
    MaxLengthErrorMessage VARCHAR(255),
    MinValueErrorMessage VARCHAR(255),
    MaxValueErrorMessage VARCHAR(255),
    FileTypeErrorMessage VARCHAR(255),
    FileSizeErrorMessage VARCHAR(255),
    
    IsActive BOOLEAN DEFAULT TRUE,
    CreatedAt TIMESTAMPTZ DEFAULT NOW(),
    CreatedBy UUID,
    ModifiedAt TIMESTAMPTZ DEFAULT NOW(),
    ModifiedBy UUID,
    IsDeleted BOOLEAN DEFAULT FALSE
);

-- Metadata table (global field definitions)
CREATE TABLE Genp.Metadata (
    Id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    MetadataKey VARCHAR(100) NOT NULL UNIQUE,
    DataTypeId UUID NOT NULL REFERENCES Genp.DataTypes(Id),
    
    -- Customer-specific validation overrides
    CustomValidationPattern VARCHAR(500),
    CustomMinLength INTEGER,
    CustomMaxLength INTEGER,
    CustomMinValue DECIMAL,
    CustomMaxValue DECIMAL,
    CustomIsRequired BOOLEAN,
    
    -- Customer-specific UI overrides
    CustomPlaceholder VARCHAR(255),
    CustomOptions TEXT,
    CustomMaxSelections INTEGER,
    CustomAllowedFileTypes VARCHAR(255),
    CustomMaxFileSize BIGINT,
    
    -- Customer-specific error messages
    CustomErrorMessage VARCHAR(255),
    
    -- UI Display Properties
    DisplayLabel VARCHAR(255),
    HelpText TEXT,
    FieldOrder INTEGER,
    IsVisible BOOLEAN DEFAULT TRUE,
    IsReadonly BOOLEAN DEFAULT FALSE,
    
    CreatedAt TIMESTAMPTZ DEFAULT NOW(),
    CreatedBy UUID,
    ModifiedAt TIMESTAMPTZ DEFAULT NOW(),
    ModifiedBy UUID,
    IsDeleted BOOLEAN DEFAULT FALSE
);

-- =============================================================================
-- MAIN ENTITY TABLES (Store only types/templates)
-- =============================================================================

-- Product table (stores product types/templates)
CREATE TABLE Genp.Product (
    Id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    TenantId VARCHAR(64) NOT NULL,
    Name VARCHAR(255) NOT NULL,
    Description TEXT,
    Version VARCHAR(50) DEFAULT '1.0.0',
    IsActive BOOLEAN DEFAULT true,
    CreatedAt TIMESTAMPTZ DEFAULT NOW(),
    CreatedBy UUID,
    ModifiedAt TIMESTAMPTZ DEFAULT NOW(),
    ModifiedBy UUID,
    IsDeleted BOOLEAN DEFAULT false,
    
    CONSTRAINT ProductTenantNameUnique UNIQUE (TenantId, Name)
);

-- Feature table (stores feature types/templates)
CREATE TABLE Genp.Feature (
    Id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ProductId UUID NOT NULL REFERENCES Genp.Product(Id) ON DELETE CASCADE,
    Name VARCHAR(255) NOT NULL,
    Description TEXT,
    IsDefault BOOLEAN DEFAULT false,
    IsActive BOOLEAN DEFAULT true,
    CreatedAt TIMESTAMPTZ DEFAULT NOW(),
    CreatedBy UUID,
    ModifiedAt TIMESTAMPTZ DEFAULT NOW(),
    ModifiedBy UUID,
    IsDeleted BOOLEAN DEFAULT false,
    
    CONSTRAINT FeatureProductNameUnique UNIQUE (ProductId, Name)
);

-- Role table (stores role types/templates)
CREATE TABLE Genp.Role (
    Id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ProductId UUID NOT NULL REFERENCES Genp.Product(Id) ON DELETE CASCADE,
    Name VARCHAR(100) NOT NULL,
    Description TEXT,
    IsSystemRole BOOLEAN DEFAULT false,
    Permissions JSONB DEFAULT '{}',
    IsActive BOOLEAN DEFAULT true,
    CreatedAt TIMESTAMPTZ DEFAULT NOW(),
    CreatedBy UUID,
    ModifiedAt TIMESTAMPTZ DEFAULT NOW(),
    ModifiedBy UUID,
    IsDeleted BOOLEAN DEFAULT false,
    
    CONSTRAINT RoleProductNameUnique UNIQUE (ProductId, Name)
);

-- User table (stores user types/templates)
CREATE TABLE Genp.User (
    Id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    TenantId VARCHAR(64) NOT NULL,
    ExternalUserId VARCHAR(255),
    Username VARCHAR(255) NOT NULL,
    Email VARCHAR(320) NOT NULL,
    PasswordHas VARCHAR(255) NOT NULL,
    IsActive BOOLEAN DEFAULT true,
    LastLoginAt TIMESTAMPTZ,
    CreatedAt TIMESTAMPTZ DEFAULT NOW(),
    CreatedBy UUID,
    ModifiedAt TIMESTAMPTZ DEFAULT NOW(),
    ModifiedBy UUID,
    IsDeleted BOOLEAN DEFAULT false,
    
    CONSTRAINT UserTenantUsernameUnique UNIQUE (TenantId, Username),
    CONSTRAINT UserTenantEmailUnique UNIQUE (TenantId, Email)
);

-- UserRole mapping table
CREATE TABLE Genp.UserRole (
    UserId UUID NOT NULL REFERENCES Genp.User(Id) ON DELETE CASCADE,
    RoleId UUID NOT NULL REFERENCES Genp.Role(Id) ON DELETE CASCADE,
    CreatedAt TIMESTAMPTZ DEFAULT NOW(),
    CreatedBy UUID,
    ModifiedAt TIMESTAMPTZ DEFAULT NOW(),
    ModifiedBy UUID,
    IsDeleted BOOLEAN DEFAULT false
);

-- Object table (stores object types/templates)
CREATE TABLE Genp.Object (
    Id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    FeatureId UUID NOT NULL REFERENCES Genp.Feature(Id) ON DELETE CASCADE,
    ParentObjectId UUID REFERENCES Genp.Object(Id) ON DELETE CASCADE,
    Name VARCHAR(255) NOT NULL,
    Description TEXT,
    IsActive BOOLEAN DEFAULT true,
    CreatedAt TIMESTAMPTZ DEFAULT NOW(),
    CreatedBy UUID,
    ModifiedAt TIMESTAMPTZ DEFAULT NOW(),
    ModifiedBy UUID,
    IsDeleted BOOLEAN DEFAULT false,
    
    CONSTRAINT ObjectFeatureNameUnique UNIQUE (FeatureId, Name)
);

-- Subscription table (stores subscription types/templates)
CREATE TABLE Genp.Subscription (
    Id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    TenantId VARCHAR(64) NOT NULL,
    ProductId UUID NOT NULL REFERENCES Genp.Product(Id) ON DELETE RESTRICT,
    SubscriptionType VARCHAR(50) DEFAULT 'standard',
    Status VARCHAR(50) DEFAULT 'active',
    StartDate TIMESTAMPTZ DEFAULT NOW(),
    EndDate TIMESTAMPTZ,
    AutoRenew BOOLEAN DEFAULT true,
    PricingTier VARCHAR(50),
    IsActive BOOLEAN DEFAULT true,
    CreatedAt TIMESTAMPTZ DEFAULT NOW(),
    CreatedBy UUID,
    ModifiedAt TIMESTAMPTZ DEFAULT NOW(),
    ModifiedBy UUID,
    IsDeleted BOOLEAN DEFAULT false,
    
    CONSTRAINT SubscriptionTenantProductUnique UNIQUE (TenantId, ProductId)
);

-- =============================================================================
-- METADATA LINK TABLES (with TenantId and IsUnique support)
-- =============================================================================

-- TenantInfo Metadata Link Table
CREATE TABLE Genp.TenantInfoMetadata (
    Id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    TenantId VARCHAR(64) NOT NULL,
    MetadataId UUID NOT NULL REFERENCES Genp.Metadata(Id) ON DELETE CASCADE,
    IsUnique BOOLEAN DEFAULT FALSE,
    IsActive BOOLEAN DEFAULT true,
    CreatedAt TIMESTAMPTZ DEFAULT NOW(),
    CreatedBy UUID,
    ModifiedAt TIMESTAMPTZ DEFAULT NOW(),
    ModifiedBy UUID,
    IsDeleted BOOLEAN DEFAULT false,
    
    CONSTRAINT TenantInfoMetadataUnique UNIQUE (TenantId, MetadataId),
    CONSTRAINT TenantInfoMetadataUniquePerTenant CHECK (
        (IsUnique = false) OR 
        (IsUnique = true AND NOT EXISTS (
            SELECT 1 FROM Genp.TenantInfoMetadata tim2 
            WHERE tim2.TenantId = TenantInfoMetadata.TenantId 
            AND tim2.IsUnique = true 
            AND tim2.Id != TenantInfoMetadata.Id 
            AND tim2.IsDeleted = false
        ))
    ) DEFERRABLE INITIALLY DEFERRED
);

-- Product Metadata Link Table
CREATE TABLE Genp.ProductMetadata (
    Id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    TenantId VARCHAR(64) NOT NULL,
    ProductId UUID NOT NULL REFERENCES Genp.Product(Id) ON DELETE CASCADE,
    MetadataId UUID NOT NULL REFERENCES Genp.Metadata(Id) ON DELETE CASCADE,
    IsUnique BOOLEAN DEFAULT FALSE,
    IsActive BOOLEAN DEFAULT true,
    CreatedAt TIMESTAMPTZ DEFAULT NOW(),
    CreatedBy UUID,
    ModifiedAt TIMESTAMPTZ DEFAULT NOW(),
    ModifiedBy UUID,
    IsDeleted BOOLEAN DEFAULT false,
    
    CONSTRAINT ProductMetadataUnique UNIQUE (TenantId, ProductId, MetadataId),
    CONSTRAINT ProductMetadataUniquePerProduct CHECK (
        (IsUnique = false) OR 
        (IsUnique = true AND NOT EXISTS (
            SELECT 1 FROM Genp.ProductMetadata pm2 
            WHERE pm2.TenantId = ProductMetadata.TenantId 
            AND pm2.ProductId = ProductMetadata.ProductId 
            AND pm2.IsUnique = true 
            AND pm2.Id != ProductMetadata.Id 
            AND pm2.IsDeleted = false
        ))
    ) DEFERRABLE INITIALLY DEFERRED
);

-- Feature Metadata Link Table
CREATE TABLE Genp.FeatureMetadata (
    Id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    TenantId VARCHAR(64) NOT NULL,
    FeatureId UUID NOT NULL REFERENCES Genp.Feature(Id) ON DELETE CASCADE,
    MetadataId UUID NOT NULL REFERENCES Genp.Metadata(Id) ON DELETE CASCADE,
    IsUnique BOOLEAN DEFAULT FALSE,
    IsActive BOOLEAN DEFAULT true,
    CreatedAt TIMESTAMPTZ DEFAULT NOW(),
    CreatedBy UUID,
    ModifiedAt TIMESTAMPTZ DEFAULT NOW(),
    ModifiedBy UUID,
    IsDeleted BOOLEAN DEFAULT false,
    
    CONSTRAINT FeatureMetadataUnique UNIQUE (TenantId, FeatureId, MetadataId),
    CONSTRAINT FeatureMetadataUniquePerFeature CHECK (
        (IsUnique = false) OR 
        (IsUnique = true AND NOT EXISTS (
            SELECT 1 FROM Genp.FeatureMetadata fm2 
            WHERE fm2.TenantId = FeatureMetadata.TenantId 
            AND fm2.FeatureId = FeatureMetadata.FeatureId 
            AND fm2.IsUnique = true 
            AND fm2.Id != FeatureMetadata.Id 
            AND fm2.IsDeleted = false
        ))
    ) DEFERRABLE INITIALLY DEFERRED
);

-- Role Metadata Link Table
CREATE TABLE Genp.RoleMetadata (
    Id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    TenantId VARCHAR(64) NOT NULL,
    RoleId UUID NOT NULL REFERENCES Genp.Role(Id) ON DELETE CASCADE,
    MetadataId UUID NOT NULL REFERENCES Genp.Metadata(Id) ON DELETE CASCADE,
    IsUnique BOOLEAN DEFAULT FALSE,
    IsActive BOOLEAN DEFAULT true,
    CreatedAt TIMESTAMPTZ DEFAULT NOW(),
    CreatedBy UUID,
    ModifiedAt TIMESTAMPTZ DEFAULT NOW(),
    ModifiedBy UUID,
    IsDeleted BOOLEAN DEFAULT false,
    
    CONSTRAINT RoleMetadataUnique UNIQUE (TenantId, RoleId, MetadataId),
    CONSTRAINT RoleMetadataUniquePerRole CHECK (
        (IsUnique = false) OR 
        (IsUnique = true AND NOT EXISTS (
            SELECT 1 FROM Genp.RoleMetadata rm2 
            WHERE rm2.TenantId = RoleMetadata.TenantId 
            AND rm2.RoleId = RoleMetadata.RoleId 
            AND rm2.IsUnique = true 
            AND rm2.Id != RoleMetadata.Id 
            AND rm2.IsDeleted = false
        ))
    ) DEFERRABLE INITIALLY DEFERRED
);

-- User Metadata Link Table
CREATE TABLE Genp.UserMetadata (
    Id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    TenantId VARCHAR(64) NOT NULL,
    UserId UUID NOT NULL REFERENCES Genp.User(Id) ON DELETE CASCADE,
    MetadataId UUID NOT NULL REFERENCES Genp.Metadata(Id) ON DELETE CASCADE,
    IsUnique BOOLEAN DEFAULT FALSE,
    IsActive BOOLEAN DEFAULT true,
    CreatedAt TIMESTAMPTZ DEFAULT NOW(),
    CreatedBy UUID,
    ModifiedAt TIMESTAMPTZ DEFAULT NOW(),
    ModifiedBy UUID,
    IsDeleted BOOLEAN DEFAULT false,
    
    CONSTRAINT UserMetadataUnique UNIQUE (TenantId, UserId, MetadataId),
    CONSTRAINT UserMetadataUniquePerUser CHECK (
        (IsUnique = false) OR 
        (IsUnique = true AND NOT EXISTS (
            SELECT 1 FROM Genp.UserMetadata um2 
            WHERE um2.TenantId = UserMetadata.TenantId 
            AND um2.UserId = UserMetadata.UserId 
            AND um2.IsUnique = true 
            AND um2.Id != UserMetadata.Id 
            AND um2.IsDeleted = false
        ))
    ) DEFERRABLE INITIALLY DEFERRED
);

-- Object Metadata Link Table
CREATE TABLE Genp.ObjectMetadata (
    Id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    TenantId VARCHAR(64) NOT NULL,
    ObjectId UUID NOT NULL REFERENCES Genp.Object(Id) ON DELETE CASCADE,
    MetadataId UUID NOT NULL REFERENCES Genp.Metadata(Id) ON DELETE CASCADE,
    IsUnique BOOLEAN DEFAULT FALSE,
    IsActive BOOLEAN DEFAULT true,
    CreatedAt TIMESTAMPTZ DEFAULT NOW(),
    CreatedBy UUID,
    ModifiedAt TIMESTAMPTZ DEFAULT NOW(),
    ModifiedBy UUID,
    IsDeleted BOOLEAN DEFAULT false,
    
    CONSTRAINT ObjectMetadataUnique UNIQUE (TenantId, ObjectId, MetadataId),
    CONSTRAINT ObjectMetadataUniquePerObject CHECK (
        (IsUnique = false) OR 
        (IsUnique = true AND NOT EXISTS (
            SELECT 1 FROM Genp.ObjectMetadata om2 
            WHERE om2.TenantId = ObjectMetadata.TenantId 
            AND om2.ObjectId = ObjectMetadata.ObjectId 
            AND om2.IsUnique = true 
            AND om2.Id != ObjectMetadata.Id 
            AND om2.IsDeleted = false
        ))
    ) DEFERRABLE INITIALLY DEFERRED
);

-- Subscription Metadata Link Table
CREATE TABLE Genp.SubscriptionMetadata (
    Id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    TenantId VARCHAR(64) NOT NULL,
    SubscriptionId UUID NOT NULL REFERENCES Genp.Subscription(Id) ON DELETE CASCADE,
    MetadataId UUID NOT NULL REFERENCES Genp.Metadata(Id) ON DELETE CASCADE,
    IsUnique BOOLEAN DEFAULT FALSE,
    IsActive BOOLEAN DEFAULT true,
    CreatedAt TIMESTAMPTZ DEFAULT NOW(),
    CreatedBy UUID,
    ModifiedAt TIMESTAMPTZ DEFAULT NOW(),
    ModifiedBy UUID,
    IsDeleted BOOLEAN DEFAULT false,
    
    CONSTRAINT SubscriptionMetadataUnique UNIQUE (TenantId, SubscriptionId, MetadataId),
    CONSTRAINT SubscriptionMetadataUniquePerSubscription CHECK (
        (IsUnique = false) OR 
        (IsUnique = true AND NOT EXISTS (
            SELECT 1 FROM Genp.SubscriptionMetadata sm2 
            WHERE sm2.TenantId = SubscriptionMetadata.TenantId 
            AND sm2.SubscriptionId = SubscriptionMetadata.SubscriptionId 
            AND sm2.IsUnique = true 
            AND sm2.Id != SubscriptionMetadata.Id 
            AND sm2.IsDeleted = false
        ))
    ) DEFERRABLE INITIALLY DEFERRED
);

-- =============================================================================
-- VALUE STORAGE TABLES (partitioned by TenantId)
-- =============================================================================

-- TenantInfo Value Table
CREATE TABLE Genp.TenantInfoValue (
    Id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    TenantId VARCHAR(64) NOT NULL,
    TenantInfoMetadataId UUID NOT NULL REFERENCES Genp.TenantInfoMetadata(Id) ON DELETE CASCADE,
    RefId UUID,
    Value TEXT,
    CreatedAt TIMESTAMPTZ DEFAULT NOW(),
    CreatedBy UUID,
    ModifiedAt TIMESTAMPTZ DEFAULT NOW(),
    ModifiedBy UUID,
    
    CONSTRAINT TenantInfoValueUnique UNIQUE (TenantId, TenantInfoMetadataId, RefId)
) PARTITION BY HASH (TenantId);

-- Product Value Table
CREATE TABLE Genp.ProductValue (
    Id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    TenantId VARCHAR(64) NOT NULL,
    ProductMetadataId UUID NOT NULL REFERENCES Genp.ProductMetadata(Id) ON DELETE CASCADE,
    RefId UUID,
    Value TEXT,
    CreatedAt TIMESTAMPTZ DEFAULT NOW(),
    CreatedBy UUID,
    ModifiedAt TIMESTAMPTZ DEFAULT NOW(),
    ModifiedBy UUID,
    
    CONSTRAINT ProductValueUnique UNIQUE (TenantId, ProductMetadataId, RefId)
) PARTITION BY HASH (TenantId);

-- Feature Value Table
CREATE TABLE Genp.FeatureValue (
    Id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    TenantId VARCHAR(64) NOT NULL,
    FeatureMetadataId UUID NOT NULL REFERENCES Genp.FeatureMetadata(Id) ON DELETE CASCADE,
    RefId UUID,
    Value TEXT,
    CreatedAt TIMESTAMPTZ DEFAULT NOW(),
    CreatedBy UUID,
    ModifiedAt TIMESTAMPTZ DEFAULT NOW(),
    ModifiedBy UUID,
    
    CONSTRAINT FeatureValueUnique UNIQUE (TenantId, FeatureMetadataId, RefId)
) PARTITION BY HASH (TenantId);

-- Role Value Table
CREATE TABLE Genp.RoleValue (
    Id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    TenantId VARCHAR(64) NOT NULL,
    RoleMetadataId UUID NOT NULL REFERENCES Genp.RoleMetadata(Id) ON DELETE CASCADE,
    RefId UUID,
    Value TEXT,
    CreatedAt TIMESTAMPTZ DEFAULT NOW(),
    CreatedBy UUID,
    ModifiedAt TIMESTAMPTZ DEFAULT NOW(),
    ModifiedBy UUID,
    
    CONSTRAINT RoleValueUnique UNIQUE (TenantId, RoleMetadataId, RefId)
) PARTITION BY HASH (TenantId);

-- User Value Table
CREATE TABLE Genp.UserValue (
    Id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    TenantId VARCHAR(64) NOT NULL,
    UserMetadataId UUID NOT NULL REFERENCES Genp.UserMetadata(Id) ON DELETE CASCADE,
    RefId UUID,
    Value TEXT,
    CreatedAt TIMESTAMPTZ DEFAULT NOW(),
    CreatedBy UUID,
    ModifiedAt TIMESTAMPTZ DEFAULT NOW(),
    ModifiedBy UUID,
    
    CONSTRAINT UserValueUnique UNIQUE (TenantId, UserMetadataId, RefId)
) PARTITION BY HASH (TenantId);

-- Object Value Table
CREATE TABLE Genp.ObjectValue (
    Id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    TenantId VARCHAR(64) NOT NULL,
    ObjectMetadataId UUID NOT NULL REFERENCES Genp.ObjectMetadata(Id) ON DELETE CASCADE,
    RefId UUID,
    Value TEXT,
    CreatedAt TIMESTAMPTZ DEFAULT NOW(),
    CreatedBy UUID,
    ModifiedAt TIMESTAMPTZ DEFAULT NOW(),
    ModifiedBy UUID,
    
    CONSTRAINT ObjectValueUnique UNIQUE (TenantId, ObjectMetadataId, RefId)
) PARTITION BY HASH (TenantId);

-- Subscription Value Table
CREATE TABLE Genp.SubscriptionValue (
    Id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    TenantId VARCHAR(64) NOT NULL,
    SubscriptionMetadataId UUID NOT NULL REFERENCES Genp.SubscriptionMetadata(Id) ON DELETE CASCADE,
    RefId UUID,
    Value TEXT,
    CreatedAt TIMESTAMPTZ DEFAULT NOW(),
    CreatedBy UUID,
    ModifiedAt TIMESTAMPTZ DEFAULT NOW(),
    ModifiedBy UUID,
    
    CONSTRAINT SubscriptionValueUnique UNIQUE (TenantId, SubscriptionMetadataId, RefId)
) PARTITION BY HASH (TenantId);

-- =============================================================================
-- CREATE PARTITIONS FOR VALUE TABLES
-- =============================================================================

DO $$
DECLARE
    i INTEGER;
    table_names TEXT[] := ARRAY[
        'TenantInfoValue', 'ProductValue', 'FeatureValue', 
        'RoleValue', 'UserValue', 'ObjectValue', 'SubscriptionValue'
    ];
    table_name TEXT;
BEGIN
    FOREACH table_name IN ARRAY table_names
    LOOP
        FOR i IN 0..3 LOOP
            EXECUTE format('CREATE TABLE IF NOT EXISTS Genp.%s_%s PARTITION OF Genp.%s 
                           FOR VALUES WITH (modulus 4, remainder %s)', table_name, i, table_name, i);
        END LOOP;
    END LOOP;
END $$;

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

-- Core table indexes
CREATE INDEX IdxProductTenantId ON Genp.Product(TenantId);
CREATE INDEX IdxProductActive ON Genp.Product(IsActive) WHERE IsActive = true AND IsDeleted = false;

CREATE INDEX IdxFeatureProductId ON Genp.Feature(ProductId);
CREATE INDEX IdxFeatureActive ON Genp.Feature(IsActive) WHERE IsActive = true AND IsDeleted = false;

CREATE INDEX IdxRoleProductId ON Genp.Role(ProductId);
CREATE INDEX IdxRoleSystem ON Genp.Role(IsSystemRole) WHERE IsSystemRole = true AND IsDeleted = false;

CREATE INDEX IdxUserTenantId ON Genp.User(TenantId);
CREATE INDEX IdxUserEmail ON Genp.User(Email);
CREATE INDEX IdxUserActive ON Genp.User(IsActive) WHERE IsActive = true AND IsDeleted = false;

CREATE INDEX IdxUserRoleUserId ON Genp.UserRole(UserId);
CREATE INDEX IdxUserRoleRoleId ON Genp.UserRole(RoleId);

CREATE INDEX IdxObjectFeatureId ON Genp.Object(FeatureId);
CREATE INDEX IdxObjectParentId ON Genp.Object(ParentObjectId);
CREATE INDEX IdxObjectActive ON Genp.Object(IsActive) WHERE IsActive = true AND IsDeleted = false;

CREATE INDEX IdxSubscriptionTenantId ON Genp.Subscription(TenantId);
CREATE INDEX IdxSubscriptionProductId ON Genp.Subscription(ProductId);
CREATE INDEX IdxSubscriptionStatus ON Genp.Subscription(Status);

-- DataTypes and Metadata indexes
CREATE INDEX IdxDataTypesName ON Genp.DataTypes(Name);
CREATE INDEX IdxDataTypesCategory ON Genp.DataTypes(Category);
CREATE INDEX IdxDataTypesActive ON Genp.DataTypes(IsActive) WHERE IsActive = true AND IsDeleted = false;

CREATE INDEX IdxMetadataKey ON Genp.Metadata(MetadataKey);
CREATE INDEX IdxMetadataDataTypeId ON Genp.Metadata(DataTypeId);
CREATE INDEX IdxMetadataVisible ON Genp.Metadata(IsVisible) WHERE IsVisible = true AND IsDeleted = false;

-- Metadata link table indexes
CREATE INDEX IdxTenantInfoMetadataTenantId ON Genp.TenantInfoMetadata(TenantId);
CREATE INDEX IdxTenantInfoMetadataIsUnique ON Genp.TenantInfoMetadata(TenantId, IsUnique) WHERE IsUnique = true;

CREATE INDEX IdxProductMetadataTenantId ON Genp.ProductMetadata(TenantId);
CREATE INDEX IdxProductMetadataProductId ON Genp.ProductMetadata(ProductId);
CREATE INDEX IdxProductMetadataIsUnique ON Genp.ProductMetadata(TenantId, ProductId, IsUnique) WHERE IsUnique = true;

CREATE INDEX IdxFeatureMetadataTenantId ON Genp.FeatureMetadata(TenantId);
CREATE INDEX IdxFeatureMetadataFeatureId ON Genp.FeatureMetadata(FeatureId);
CREATE INDEX IdxFeatureMetadataIsUnique ON Genp.FeatureMetadata(TenantId, FeatureId, IsUnique) WHERE IsUnique = true;

CREATE INDEX IdxRoleMetadataTenantId ON Genp.RoleMetadata(TenantId);
CREATE INDEX IdxRoleMetadataRoleId ON Genp.RoleMetadata(RoleId);
CREATE INDEX IdxRoleMetadataIsUnique ON Genp.RoleMetadata(TenantId, RoleId, IsUnique) WHERE IsUnique = true;

CREATE INDEX IdxUserMetadataTenantId ON Genp.UserMetadata(TenantId);
CREATE INDEX IdxUserMetadataUserId ON Genp.UserMetadata(UserId);
CREATE INDEX IdxUserMetadataIsUnique ON Genp.UserMetadata(TenantId, UserId, IsUnique) WHERE IsUnique = true;

CREATE INDEX IdxObjectMetadataTenantId ON Genp.ObjectMetadata(TenantId);
CREATE INDEX IdxObjectMetadataObjectId ON Genp.ObjectMetadata(ObjectId);
CREATE INDEX IdxObjectMetadataIsUnique ON Genp.ObjectMetadata(TenantId, ObjectId, IsUnique) WHERE IsUnique = true;

CREATE INDEX IdxSubscriptionMetadataTenantId ON Genp.SubscriptionMetadata(TenantId);
CREATE INDEX IdxSubscriptionMetadataSubscriptionId ON Genp.SubscriptionMetadata(SubscriptionId);
CREATE INDEX IdxSubscriptionMetadataIsUnique ON Genp.SubscriptionMetadata(TenantId, SubscriptionId, IsUnique) WHERE IsUnique = true;

-- Value table indexes
CREATE INDEX IdxTenantInfoValueTenantId ON Genp.TenantInfoValue(TenantId);
CREATE INDEX IdxTenantInfoValueRefId ON Genp.TenantInfoValue(TenantId, RefId);
CREATE INDEX IdxTenantInfoValueValue ON Genp.TenantInfoValue(Value) WHERE Value IS NOT NULL;

CREATE INDEX IdxProductValueTenantId ON Genp.ProductValue(TenantId);
CREATE INDEX IdxProductValueRefId ON Genp.ProductValue(TenantId, RefId);
CREATE INDEX IdxProductValueValue ON Genp.ProductValue(Value) WHERE Value IS NOT NULL;

CREATE INDEX IdxFeatureValueTenantId ON Genp.FeatureValue(TenantId);
CREATE INDEX IdxFeatureValueRefId ON Genp.FeatureValue(TenantId, RefId);
CREATE INDEX IdxFeatureValueValue ON Genp.FeatureValue(Value) WHERE Value IS NOT NULL;

CREATE INDEX IdxRoleValueTenantId ON Genp.RoleValue(TenantId);
CREATE INDEX IdxRoleValueRefId ON Genp.RoleValue(TenantId, RefId);
CREATE INDEX IdxRoleValueValue ON Genp.RoleValue(Value) WHERE Value IS NOT NULL;

CREATE INDEX IdxUserValueTenantId ON Genp.UserValue(TenantId);
CREATE INDEX IdxUserValueRefId ON Genp.UserValue(TenantId, RefId);
CREATE INDEX IdxUserValueValue ON Genp.UserValue(Value) WHERE Value IS NOT NULL;

CREATE INDEX IdxObjectValueTenantId ON Genp.ObjectValue(TenantId);
CREATE INDEX IdxObjectValueRefId ON Genp.ObjectValue(TenantId, RefId);
CREATE INDEX IdxObjectValueValue ON Genp.ObjectValue(Value) WHERE Value IS NOT NULL;

CREATE INDEX IdxSubscriptionValueTenantId ON Genp.SubscriptionValue(TenantId);
CREATE INDEX IdxSubscriptionValueRefId ON Genp.SubscriptionValue(TenantId, RefId);
CREATE INDEX IdxSubscriptionValueValue ON Genp.SubscriptionValue(Value) WHERE Value IS NOT NULL;

-- =============================================================================
-- TRIGGERS AND FUNCTIONS
-- =============================================================================

-- Function to update ModifiedAt timestamp
CREATE OR REPLACE FUNCTION Genp.UpdateModifiedAtColumn()
RETURNS TRIGGER AS $$
BEGIN
    NEW.ModifiedAt = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply ModifiedAt triggers to all tables
DO $$
DECLARE
    table_name TEXT;
    tables TEXT[] := ARRAY[
        'DataTypes', 'Metadata', 'Product', 'Feature', 'Role', 'User', 'Object', 'Subscription',
        'TenantInfoMetadata', 'ProductMetadata', 'FeatureMetadata', 
        'RoleMetadata', 'UserMetadata', 'ObjectMetadata', 'SubscriptionMetadata',
        'TenantInfoValue', 'ProductValue', 'FeatureValue',
        'RoleValue', 'UserValue', 'ObjectValue', 'SubscriptionValue'
    ];
BEGIN
    FOREACH table_name IN ARRAY tables
    LOOP
        EXECUTE format('DROP TRIGGER IF EXISTS Tr%sModifiedAt ON Genp.%s', table_name, table_name);
        EXECUTE format('CREATE TRIGGER Tr%sModifiedAt 
                       BEFORE UPDATE ON Genp.%s 
                       FOR EACH ROW EXECUTE FUNCTION Genp.UpdateModifiedAtColumn()', 
                       table_name, table_name);
    END LOOP;
END $$;

-- Function to validate metadata exists before allowing values
CREATE OR REPLACE FUNCTION Genp.ValidateMetadataExists()
RETURNS TRIGGER AS $$
BEGIN
    CASE TG_TABLE_NAME
        WHEN 'TenantInfoValue' THEN
            IF NOT EXISTS (
                SELECT 1 FROM Genp.TenantInfoMetadata tim 
                INNER JOIN Genp.Metadata m ON tim.MetadataId = m.Id
                WHERE tim.Id = NEW.TenantInfoMetadataId 
                AND tim.TenantId = NEW.TenantId
                AND tim.IsDeleted = false 
                AND tim.IsActive = true
                AND m.IsDeleted = false
            ) THEN
                RAISE EXCEPTION 'Invalid TenantInfoMetadata reference: % for TenantId: %', 
                    NEW.TenantInfoMetadataId, NEW.TenantId;
            END IF;
        WHEN 'ProductValue' THEN
            IF NOT EXISTS (
                SELECT 1 FROM Genp.ProductMetadata pm 
                INNER JOIN Genp.Metadata m ON pm.MetadataId = m.Id
                WHERE pm.Id = NEW.ProductMetadataId 
                AND pm.TenantId = NEW.TenantId
                AND pm.IsDeleted = false 
                AND pm.IsActive = true
                AND m.IsDeleted = false
            ) THEN
                RAISE EXCEPTION 'Invalid ProductMetadata reference: % for TenantId: %', 
                    NEW.ProductMetadataId, NEW.TenantId;
            END IF;
        WHEN 'ObjectValue' THEN
            IF NOT EXISTS (
                SELECT 1 FROM Genp.ObjectMetadata om 
                INNER JOIN Genp.Metadata m ON om.MetadataId = m.Id
                WHERE om.Id = NEW.ObjectMetadataId 
                AND om.TenantId = NEW.TenantId
                AND om.IsDeleted = false 
                AND om.IsActive = true
                AND m.IsDeleted = false
            ) THEN
                RAISE EXCEPTION 'Invalid ObjectMetadata reference: % for TenantId: %', 
                    NEW.ObjectMetadataId, NEW.TenantId;
            END IF;
        -- Add similar validations for other value tables as needed
    END CASE;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply validation triggers to value tables
DO $$
DECLARE
    table_name TEXT;
    value_tables TEXT[] := ARRAY[
        'TenantInfoValue', 'ProductValue', 'FeatureValue',
        'RoleValue', 'UserValue', 'ObjectValue', 'SubscriptionValue'
    ];
BEGIN
    FOREACH table_name IN ARRAY value_tables
    LOOP
        EXECUTE format('DROP TRIGGER IF EXISTS TrValidateMetadata%s ON Genp.%s', table_name, table_name);
        EXECUTE format('CREATE TRIGGER TrValidateMetadata%s 
                       BEFORE INSERT OR UPDATE ON Genp.%s 
                       FOR EACH ROW EXECUTE FUNCTION Genp.ValidateMetadataExists()', 
                       table_name, table_name);
    END LOOP;
END $$;

-- =============================================================================
-- HELPER FUNCTIONS
-- =============================================================================

-- Function to create default roles for a product
CREATE OR REPLACE FUNCTION Genp.CreateDefaultRoles(p_ProductId UUID)
RETURNS VOID AS $$
BEGIN
    INSERT INTO Genp.Role (ProductId, Name, Description, IsSystemRole) VALUES
    (p_ProductId, 'Admin', 'System administrator with full access', true),
    (p_ProductId, 'CustomerAdmin', 'Customer administrator with tenant-level access', true),
    (p_ProductId, 'EndUser', 'End user with limited access', true);
END;
$$ LANGUAGE plpgsql;

-- Function to create default feature for a product
CREATE OR REPLACE FUNCTION Genp.CreateDefaultFeature(p_ProductId UUID)
RETURNS UUID AS $$
DECLARE
    feature_id UUID;
BEGIN
    INSERT INTO Genp.Feature (ProductId, Name, Description, IsDefault)
    VALUES (p_ProductId, 'Core Features', 'Default core features', true)
    RETURNING Id INTO feature_id;
    
    RETURN feature_id;
END;
$$ LANGUAGE plpgsql;

-- Function to safely add metadata without duplication
CREATE OR REPLACE FUNCTION Genp.SafeAddMetadata(
    p_EntityType VARCHAR(50),
    p_TenantId VARCHAR(64),
    p_EntityId UUID,
    p_MetadataKey VARCHAR(100),
    p_IsUnique BOOLEAN DEFAULT false
)
RETURNS UUID AS $$
DECLARE
    v_MetadataId UUID;
    v_LinkId UUID;
    v_ExistingCount INTEGER;
BEGIN
    -- Get metadata ID
    SELECT Id INTO v_MetadataId 
    FROM Genp.Metadata 
    WHERE MetadataKey = p_MetadataKey AND IsDeleted = false;
    
    IF v_MetadataId IS NULL THEN
        RAISE EXCEPTION 'Metadata with key % not found', p_MetadataKey;
    END IF;
    
    -- Check for existing metadata link and create if needed
    CASE p_EntityType
        WHEN 'TenantInfo' THEN
            SELECT COUNT(*), MIN(Id) INTO v_ExistingCount, v_LinkId
            FROM Genp.TenantInfoMetadata 
            WHERE TenantId = p_TenantId 
            AND MetadataId = v_MetadataId 
            AND IsDeleted = false;
            
            IF v_ExistingCount = 0 THEN
                INSERT INTO Genp.TenantInfoMetadata (TenantId, MetadataId, IsUnique)
                VALUES (p_TenantId, v_MetadataId, p_IsUnique)
                RETURNING Id INTO v_LinkId;
            END IF;
            
        WHEN 'Product' THEN
            SELECT COUNT(*), MIN(Id) INTO v_ExistingCount, v_LinkId
            FROM Genp.ProductMetadata 
            WHERE TenantId = p_TenantId 
            AND ProductId = p_EntityId 
            AND MetadataId = v_MetadataId 
            AND IsDeleted = false;
            
            IF v_ExistingCount = 0 THEN
                INSERT INTO Genp.ProductMetadata (TenantId, ProductId, MetadataId, IsUnique)
                VALUES (p_TenantId, p_EntityId, v_MetadataId, p_IsUnique)
                RETURNING Id INTO v_LinkId;
            END IF;
            
        WHEN 'Object' THEN
            SELECT COUNT(*), MIN(Id) INTO v_ExistingCount, v_LinkId
            FROM Genp.ObjectMetadata 
            WHERE TenantId = p_TenantId 
            AND ObjectId = p_EntityId 
            AND MetadataId = v_MetadataId 
            AND IsDeleted = false;
            
            IF v_ExistingCount = 0 THEN
                INSERT INTO Genp.ObjectMetadata (TenantId, ObjectId, MetadataId, IsUnique)
                VALUES (p_TenantId, p_EntityId, v_MetadataId, p_IsUnique)
                RETURNING Id INTO v_LinkId;
            END IF;
    END CASE;
    
    RETURN v_LinkId;
END;
$$ LANGUAGE plpgsql;

-- Function to safely add values without duplication
CREATE OR REPLACE FUNCTION Genp.SafeAddValue(
    p_EntityType VARCHAR(50),
    p_TenantId VARCHAR(64),
    p_MetadataKey VARCHAR(100),
    p_Value TEXT,
    p_RefId UUID DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    v_MetadataLinkId UUID;
    v_ValueId UUID;
    v_ExistingCount INTEGER;
    v_FinalRefId UUID;
BEGIN
    -- Get metadata link ID
    CASE p_EntityType
        WHEN 'TenantInfo' THEN
            SELECT tim.Id INTO v_MetadataLinkId
            FROM Genp.TenantInfoMetadata tim
            INNER JOIN Genp.Metadata m ON tim.MetadataId = m.Id
            WHERE tim.TenantId = p_TenantId 
            AND m.MetadataKey = p_MetadataKey
            AND tim.IsDeleted = false
            AND tim.IsActive = true;
            
        WHEN 'Object' THEN
            SELECT om.Id INTO v_MetadataLinkId
            FROM Genp.ObjectMetadata om
            INNER JOIN Genp.Metadata m ON om.MetadataId = m.Id
            WHERE om.TenantId = p_TenantId 
            AND m.MetadataKey = p_MetadataKey
            AND om.IsDeleted = false
            AND om.IsActive = true
            LIMIT 1;
    END CASE;
    
    IF v_MetadataLinkId IS NULL THEN
        RAISE EXCEPTION 'No metadata link found for EntityType: %, TenantId: %, MetadataKey: %', 
            p_EntityType, p_TenantId, p_MetadataKey;
    END IF;
    
    -- Determine RefId
    v_FinalRefId := COALESCE(p_RefId, v_MetadataLinkId);
    
    -- Insert or update value
    CASE p_EntityType
        WHEN 'TenantInfo' THEN
            INSERT INTO Genp.TenantInfoValue (TenantId, TenantInfoMetadataId, RefId, Value)
            VALUES (p_TenantId, v_MetadataLinkId, v_FinalRefId, p_Value)
            ON CONFLICT (TenantId, TenantInfoMetadataId, RefId) 
            DO UPDATE SET Value = EXCLUDED.Value, ModifiedAt = NOW()
            RETURNING Id INTO v_ValueId;
            
        WHEN 'Object' THEN
            INSERT INTO Genp.ObjectValue (TenantId, ObjectMetadataId, RefId, Value)
            VALUES (p_TenantId, v_MetadataLinkId, v_FinalRefId, p_Value)
            ON CONFLICT (TenantId, ObjectMetadataId, RefId) 
            DO UPDATE SET Value = EXCLUDED.Value, ModifiedAt = NOW()
            RETURNING Id INTO v_ValueId;
    END CASE;
    
    RETURN v_ValueId;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- INITIAL DATA - DATATYPES
-- =============================================================================

-- Insert comprehensive DataTypes with specific UUIDs for consistency
INSERT INTO Genp.DataTypes (Id, Name, DisplayName, Category, UiComponent, InputType, Placeholder, DefaultOptions, AllowsMultiple) VALUES
('00000000-0000-0000-0000-000000000000', 'text', 'Text', 'primitive', 'input', 'text', 'Enter text...', NULL, FALSE),
('00000000-0000-0000-0000-000000000001', 'number', 'Number', 'primitive', 'input', 'number', 'Enter number...', NULL, FALSE),
('00000000-0000-0000-0000-000000000002', 'date', 'Date', 'temporal', 'datepicker', 'date', 'Select date...', NULL, FALSE),
('00000000-0000-0000-0000-000000000003', 'datetime', 'Date & Time', 'temporal', 'datetimepicker', 'datetime-local', 'Select date and time...', NULL, FALSE),
('00000000-0000-0000-0000-000000000004', 'boolean', 'Boolean', 'primitive', 'checkbox', 'checkbox', NULL, NULL, FALSE),
('00000000-0000-0000-0000-000000000005', 'select', 'Select', 'choice', 'select', 'select', 'Choose option...', '[]', FALSE),
('00000000-0000-0000-0000-000000000006', 'multiselect', 'Multi-Select', 'choice', 'multiselect', 'select', 'Choose options...', '[]', TRUE),
('00000000-0000-0000-0000-000000000007', 'email', 'Email', 'formatted', 'input', 'email', 'Enter email address...', NULL, FALSE),
('00000000-0000-0000-0000-000000000008', 'phone', 'Phone', 'formatted', 'input', 'tel', 'Enter phone number...', NULL, FALSE),
('00000000-0000-0000-0000-000000000009', 'url', 'URL', 'formatted', 'input', 'url', 'Enter URL...', NULL, FALSE),
('00000000-0000-0000-0000-000000000010', 'file', 'File', 'media', 'fileupload', 'file', 'Choose file...', NULL, FALSE),
('00000000-0000-0000-0000-000000000011', 'image', 'Image', 'media', 'imageupload', 'file', 'Choose image...', NULL, FALSE),
('00000000-0000-0000-0000-000000000012', 'richtext', 'Rich Text', 'complex', 'richtexteditor', 'textarea', 'Enter rich text...', NULL, FALSE),
('00000000-0000-0000-0000-000000000013', 'address', 'Address', 'complex', 'addressinput', 'textarea', 'Enter address...', NULL, FALSE),
('00000000-0000-0000-0000-000000000014', 'currency', 'Currency', 'formatted', 'input', 'number', 'Enter amount...', NULL, FALSE),
('00000000-0000-0000-0000-000000000015', 'percentage', 'Percentage', 'formatted', 'input', 'number', 'Enter percentage...', NULL, FALSE),
('00000000-0000-0000-0000-000000000016', 'color', 'Color', 'interactive', 'colorpicker', 'color', 'Choose color...', NULL, FALSE),
('00000000-0000-0000-0000-000000000017', 'rating', 'Rating', 'interactive', 'rating', 'range', NULL, NULL, FALSE),
('00000000-0000-0000-0000-000000000018', 'slider', 'Slider', 'interactive', 'slider', 'range', NULL, NULL, FALSE),
('00000000-0000-0000-0000-000000000019', 'tag', 'Tags', 'choice', 'taginput', 'text', 'Enter tags...', '[]', TRUE)
ON CONFLICT (Id) DO NOTHING;

-- =============================================================================
-- HELPER VIEWS
-- =============================================================================

-- View to get complete metadata information with DataType details
CREATE OR REPLACE VIEW Genp.VwCompleteMetadata AS
SELECT 
    m.Id,
    m.MetadataKey,
    m.DisplayLabel,
    m.HelpText,
    m.FieldOrder,
    m.IsVisible,
    m.IsReadonly,
    dt.Name AS DataTypeName,
    dt.DisplayName AS DataTypeDisplayName,
    dt.Category,
    dt.UiComponent,
    dt.InputType,
    COALESCE(m.CustomPlaceholder, dt.Placeholder) AS Placeholder,
    COALESCE(m.CustomOptions, dt.DefaultOptions) AS Options,
    COALESCE(m.CustomIsRequired, dt.IsRequired) AS IsRequired,
    dt.ValidationPattern,
    dt.MinLength,
    dt.MaxLength,
    dt.MinValue,
    dt.MaxValue,
    dt.AllowsMultiple,
    dt.AllowsCustomOptions,
    m.CreatedAt,
    m.ModifiedAt
FROM Genp.Metadata m
INNER JOIN Genp.DataTypes dt ON m.DataTypeId = dt.Id
WHERE m.IsDeleted = false AND dt.IsDeleted = false;

-- =============================================================================
-- GRANT PERMISSIONS
-- =============================================================================

-- Grant usage on schema
GRANT USAGE ON SCHEMA Genp TO PUBLIC;

-- Grant permissions on tables (adjust as per your security requirements)
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA Genp TO PUBLIC;

-- Grant permissions on views and functions
GRANT SELECT ON ALL TABLES IN SCHEMA Genp TO PUBLIC;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA Genp TO PUBLIC;

-- =============================================================================
-- COMMENTS FOR DOCUMENTATION
-- =============================================================================

COMMENT ON SCHEMA Genp IS 'Enterprise multi-tenant database schema with type-based metadata architecture';

-- Core table comments
COMMENT ON TABLE Genp.DataTypes IS 'Global data type definitions with comprehensive validation, UI, and error message properties';
COMMENT ON TABLE Genp.Metadata IS 'Global metadata field definitions for all entity types';
COMMENT ON TABLE Genp.Product IS 'Product types/templates (actual instances stored in ProductValue)';
COMMENT ON TABLE Genp.Feature IS 'Feature types/templates (actual instances stored in FeatureValue)';
COMMENT ON TABLE Genp.Role IS 'Role types/templates (actual instances stored in RoleValue)';
COMMENT ON TABLE Genp.User IS 'User types/templates (actual instances stored in UserValue)';
COMMENT ON TABLE Genp.Object IS 'Object types/templates like Tower, Floor, Unit (actual instances stored in ObjectValue)';
COMMENT ON TABLE Genp.Subscription IS 'Subscription types/templates (actual instances stored in SubscriptionValue)';

-- Metadata link table comments
COMMENT ON TABLE Genp.TenantInfoMetadata IS 'Links TenantInfo types to their metadata with IsUnique support';
COMMENT ON TABLE Genp.ProductMetadata IS 'Links Product types to their metadata with IsUnique support';
COMMENT ON TABLE Genp.FeatureMetadata IS 'Links Feature types to their metadata with IsUnique support';
COMMENT ON TABLE Genp.RoleMetadata IS 'Links Role types to their metadata with IsUnique support';
COMMENT ON TABLE Genp.UserMetadata IS 'Links User types to their metadata with IsUnique support';
COMMENT ON TABLE Genp.ObjectMetadata IS 'Links Object types to their metadata with IsUnique support';
COMMENT ON TABLE Genp.SubscriptionMetadata IS 'Links Subscription types to their metadata with IsUnique support';

-- Value table comments
COMMENT ON TABLE Genp.TenantInfoValue IS 'Actual tenant information data (e.g., "Prestige Jindal City")';
COMMENT ON TABLE Genp.ProductValue IS 'Actual product instance data';
COMMENT ON TABLE Genp.FeatureValue IS 'Actual feature instance data';
COMMENT ON TABLE Genp.RoleValue IS 'Actual role instance data';
COMMENT ON TABLE Genp.UserValue IS 'Actual user instance data';
COMMENT ON TABLE Genp.ObjectValue IS 'Actual object instance data (e.g., "Tower A", "Unit A101")';
COMMENT ON TABLE Genp.SubscriptionValue IS 'Actual subscription instance data';

-- Reset search path
RESET search_path;

-- =============================================================================
-- COMPLETION MESSAGE
-- =============================================================================

DO $$
BEGIN
    RAISE NOTICE '=============================================================================';
    RAISE NOTICE 'ENTERPRISE MULTI-TENANT DATABASE SCHEMA CREATED SUCCESSFULLY!';
    RAISE NOTICE '=============================================================================';
    RAISE NOTICE 'Features Implemented:';
    RAISE NOTICE '- Type-based architecture: Main tables store types, Value tables store instances';
    RAISE NOTICE '- Metadata-driven: All field definitions through global Metadata table';
    RAISE NOTICE '- UUID primary keys throughout for distributed systems';
    RAISE NOTICE '- TenantId isolation in all *Metadata and *Value tables';
    RAISE NOTICE '- IsUnique support with RefId grouping for related values';
    RAISE NOTICE '- Comprehensive DataTypes with 20 field types and validation rules';
    RAISE NOTICE '- Partitioned value tables for optimal performance';
    RAISE NOTICE '- Complete indexing strategy for fast queries';
    RAISE NOTICE '- Data integrity triggers and validation functions';
    RAISE NOTICE '- Safe insertion functions to prevent duplication';
    RAISE NOTICE '- Helper views and utility functions';
    RAISE NOTICE '=============================================================================';
    RAISE NOTICE 'Usage:';
    RAISE NOTICE '1. Create type templates in main tables (Product, Feature, Object, etc.)';
    RAISE NOTICE '2. Define metadata globally in Metadata table';
    RAISE NOTICE '3. Link metadata to types in *Metadata tables';
    RAISE NOTICE '4. Store actual business data in *Value tables using RefId grouping';
    RAISE NOTICE '5. Use SafeAddMetadata() and SafeAddValue() functions for safe operations';
    RAISE NOTICE '=============================================================================';
    RAISE NOTICE 'Ready for Prestige Jindal City implementation!';
    RAISE NOTICE '=============================================================================';
END $$;
