{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"Domain/1.0.0": {"dependencies": {"Finbuckle.MultiTenant": "6.13.1", "Finbuckle.MultiTenant.AspNetCore": "6.13.1", "Finbuckle.MultiTenant.EntityFrameworkCore": "6.13.1", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "8.0.10", "Microsoft.Identity.Web": "3.2.2", "Shared": "1.0.0"}, "runtime": {"Domain.dll": {}}}, "Azure.Core/1.38.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.ClientModel": "1.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.4", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.38.0.0", "fileVersion": "1.3800.24.12602"}}}, "Azure.Identity/1.11.4": {"dependencies": {"Azure.Core": "1.38.0", "Microsoft.Identity.Client": "4.65.0", "Microsoft.Identity.Client.Extensions.Msal": "4.61.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "4.7.0", "System.Text.Json": "8.0.4", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.11.4.0", "fileVersion": "1.1100.424.31005"}}}, "Azure.Security.KeyVault.Certificates/4.6.0": {"dependencies": {"Azure.Core": "1.38.0", "System.Memory": "4.5.4", "System.Text.Json": "8.0.4", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Security.KeyVault.Certificates.dll": {"assemblyVersion": "4.6.0.0", "fileVersion": "4.600.24.11403"}}}, "Azure.Security.KeyVault.Secrets/4.6.0": {"dependencies": {"Azure.Core": "1.38.0", "System.Memory": "4.5.4", "System.Text.Json": "8.0.4", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Security.KeyVault.Secrets.dll": {"assemblyVersion": "4.6.0.0", "fileVersion": "4.600.24.11403"}}}, "Finbuckle.MultiTenant/6.13.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}, "runtime": {"lib/net8.0/Finbuckle.MultiTenant.dll": {"assemblyVersion": "6.13.1.0", "fileVersion": "6.13.1.0"}}}, "Finbuckle.MultiTenant.AspNetCore/6.13.1": {"dependencies": {"Finbuckle.MultiTenant": "6.13.1", "Microsoft.AspNetCore.Authentication.OpenIdConnect": "8.0.1"}, "runtime": {"lib/net8.0/Finbuckle.MultiTenant.AspNetCore.dll": {"assemblyVersion": "6.13.1.0", "fileVersion": "6.13.1.0"}}}, "Finbuckle.MultiTenant.EntityFrameworkCore/6.13.1": {"dependencies": {"Finbuckle.MultiTenant": "6.13.1", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "8.0.10", "Microsoft.EntityFrameworkCore.Relational": "8.0.10", "Microsoft.Extensions.Identity.Stores": "8.0.10"}, "runtime": {"lib/net8.0/Finbuckle.MultiTenant.EntityFrameworkCore.dll": {"assemblyVersion": "6.13.1.0", "fileVersion": "6.13.1.0"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.0": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.1.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.1.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.123.58008"}}}, "Microsoft.AspNetCore.Cryptography.Internal/8.0.10": {"runtime": {"lib/net8.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46804"}}}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/8.0.10": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "8.0.10"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46804"}}}, "Microsoft.AspNetCore.DataProtection/8.0.1": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "8.0.10", "Microsoft.AspNetCore.DataProtection.Abstractions": "8.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "System.Security.Cryptography.Xml": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.DataProtection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.123.58008"}}}, "Microsoft.AspNetCore.DataProtection.Abstractions/8.0.1": {"runtime": {"lib/net8.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.123.58008"}}}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/8.0.10": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "8.0.10", "Microsoft.Extensions.Identity.Stores": "8.0.10"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1024.46804"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}}, "Microsoft.Bcl.TimeProvider/8.0.1": {"runtime": {"lib/net8.0/Microsoft.Bcl.TimeProvider.dll": {"assemblyVersion": "8.0.0.1", "fileVersion": "8.0.123.58001"}}}, "Microsoft.EntityFrameworkCore/8.0.10": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "8.0.10", "Microsoft.EntityFrameworkCore.Analyzers": "8.0.10", "Microsoft.Extensions.Caching.Memory": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1024.46708"}}}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.10": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1024.46708"}}}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.10": {}, "Microsoft.EntityFrameworkCore.Relational/8.0.10": {"dependencies": {"Microsoft.EntityFrameworkCore": "8.0.10", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1024.46708"}}}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Caching.Memory/8.0.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Diagnostics/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "System.Diagnostics.DiagnosticSource": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.2"}}, "Microsoft.Extensions.Http/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}}, "Microsoft.Extensions.Identity.Core/8.0.10": {"dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "8.0.10", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Identity.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46804"}}}, "Microsoft.Extensions.Identity.Stores/8.0.10": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.Identity.Core": "8.0.10", "Microsoft.Extensions.Logging": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Identity.Stores.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46804"}}}, "Microsoft.Extensions.Logging/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Options/8.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.224.6711"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives/8.0.0": {}, "Microsoft.Identity.Abstractions/7.1.0": {"runtime": {"lib/net8.0/Microsoft.Identity.Abstractions.dll": {"assemblyVersion": "7.1.0.0", "fileVersion": "7.1.0.0"}}}, "Microsoft.Identity.Client/4.65.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.1.2", "System.Diagnostics.DiagnosticSource": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.65.0.0", "fileVersion": "4.65.0.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"dependencies": {"Microsoft.Identity.Client": "4.65.0", "System.Security.Cryptography.ProtectedData": "4.7.0"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.Identity.Web/3.2.2": {"dependencies": {"Microsoft.Extensions.Http": "8.0.0", "Microsoft.Identity.Web.Certificate": "3.2.2", "Microsoft.Identity.Web.Certificateless": "3.2.2", "Microsoft.Identity.Web.TokenAcquisition": "3.2.2", "Microsoft.Identity.Web.TokenCache": "3.2.2", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.1.2", "Microsoft.IdentityModel.Validators": "8.1.2", "System.Formats.Asn1": "8.0.1", "System.IdentityModel.Tokens.Jwt": "8.1.2", "System.Text.Json": "8.0.4"}, "runtime": {"lib/net8.0/Microsoft.Identity.Web.dll": {"assemblyVersion": "3.2.2.0", "fileVersion": "3.2.2.0"}}}, "Microsoft.Identity.Web.Certificate/3.2.2": {"dependencies": {"Azure.Identity": "1.11.4", "Azure.Security.KeyVault.Certificates": "4.6.0", "Azure.Security.KeyVault.Secrets": "4.6.0", "Microsoft.Identity.Abstractions": "7.1.0", "Microsoft.Identity.Web.Certificateless": "3.2.2", "Microsoft.Identity.Web.Diagnostics": "3.2.2", "System.Text.Json": "8.0.4"}, "runtime": {"lib/net8.0/Microsoft.Identity.Web.Certificate.dll": {"assemblyVersion": "3.2.2.0", "fileVersion": "3.2.2.0"}}}, "Microsoft.Identity.Web.Certificateless/3.2.2": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Identity.Client": "4.65.0", "Microsoft.IdentityModel.JsonWebTokens": "8.1.2", "System.Text.Json": "8.0.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Identity.Web.Certificateless.dll": {"assemblyVersion": "3.2.2.0", "fileVersion": "3.2.2.0"}}}, "Microsoft.Identity.Web.Diagnostics/3.2.2": {"runtime": {"lib/net8.0/Microsoft.Identity.Web.Diagnostics.dll": {"assemblyVersion": "3.2.2.0", "fileVersion": "3.2.2.0"}}}, "Microsoft.Identity.Web.TokenAcquisition/3.2.2": {"dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.0", "Microsoft.AspNetCore.Authentication.OpenIdConnect": "8.0.1", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Microsoft.Identity.Abstractions": "7.1.0", "Microsoft.Identity.Web.Certificate": "3.2.2", "Microsoft.Identity.Web.Certificateless": "3.2.2", "Microsoft.Identity.Web.TokenCache": "3.2.2", "Microsoft.IdentityModel.Logging": "8.1.2", "Microsoft.IdentityModel.LoggingExtensions": "8.1.2", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.1.2", "System.IdentityModel.Tokens.Jwt": "8.1.2"}, "runtime": {"lib/net8.0/Microsoft.Identity.Web.TokenAcquisition.dll": {"assemblyVersion": "3.2.2.0", "fileVersion": "3.2.2.0"}}}, "Microsoft.Identity.Web.TokenCache/3.2.2": {"dependencies": {"Microsoft.AspNetCore.DataProtection": "8.0.1", "Microsoft.Extensions.Caching.Memory": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Identity.Client": "4.65.0", "Microsoft.Identity.Web.Diagnostics": "3.2.2", "System.Formats.Asn1": "8.0.1", "System.Security.Cryptography.Pkcs": "8.0.0", "System.Security.Cryptography.Xml": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.Identity.Web.TokenCache.dll": {"assemblyVersion": "3.2.2.0", "fileVersion": "3.2.2.0"}}}, "Microsoft.IdentityModel.Abstractions/8.1.2": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "8.1.2.0", "fileVersion": "8.1.2.51008"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.1.2": {"dependencies": {"Microsoft.Bcl.TimeProvider": "8.0.1", "Microsoft.IdentityModel.Tokens": "8.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "8.1.2.0", "fileVersion": "8.1.2.51008"}}}, "Microsoft.IdentityModel.Logging/8.1.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "8.1.2.0", "fileVersion": "8.1.2.51008"}}}, "Microsoft.IdentityModel.LoggingExtensions/8.1.2": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.IdentityModel.Abstractions": "8.1.2"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.LoggingExtensions.dll": {"assemblyVersion": "8.1.2.0", "fileVersion": "8.1.2.51008"}}}, "Microsoft.IdentityModel.Protocols/8.1.2": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "8.1.2.0", "fileVersion": "8.1.2.51008"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.1.2": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.1.2", "System.IdentityModel.Tokens.Jwt": "8.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "8.1.2.0", "fileVersion": "8.1.2.51008"}}}, "Microsoft.IdentityModel.Tokens/8.1.2": {"dependencies": {"Microsoft.Bcl.TimeProvider": "8.0.1", "Microsoft.IdentityModel.Logging": "8.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "8.1.2.0", "fileVersion": "8.1.2.51008"}}}, "Microsoft.IdentityModel.Validators/8.1.2": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.1.2", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.1.2", "Microsoft.IdentityModel.Tokens": "8.1.2", "System.IdentityModel.Tokens.Jwt": "8.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Validators.dll": {"assemblyVersion": "8.1.2.0", "fileVersion": "8.1.2.51008"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "System.ClientModel/1.0.0": {"dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "8.0.4"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.24.5302"}}}, "System.Diagnostics.DiagnosticSource/8.0.0": {}, "System.Formats.Asn1/8.0.1": {}, "System.IdentityModel.Tokens.Jwt/8.1.2": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.1.2", "Microsoft.IdentityModel.Tokens": "8.1.2"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "8.1.2.0", "fileVersion": "8.1.2.51008"}}}, "System.Memory/4.5.4": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.4"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "1.0.2.0", "fileVersion": "1.0.221.20802"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Security.Cryptography.Pkcs/8.0.0": {"dependencies": {"System.Formats.Asn1": "8.0.1"}}, "System.Security.Cryptography.ProtectedData/4.7.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "4.0.5.0", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.5.0", "fileVersion": "4.700.19.56404"}}}, "System.Security.Cryptography.Xml/8.0.1": {"dependencies": {"System.Security.Cryptography.Pkcs": "8.0.0"}, "runtime": {"lib/net8.0/System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}}}, "System.Text.Encodings.Web/8.0.0": {}, "System.Text.Json/8.0.4": {"dependencies": {"System.Text.Encodings.Web": "8.0.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "Shared/1.0.0": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Azure.Core/1.38.0": {"type": "package", "serviceable": true, "sha512": "sha512-IuEgCoVA0ef7E4pQtpC3+TkPbzaoQfa77HlfJDmfuaJUCVJmn7fT0izamZiryW5sYUFKizsftIxMkXKbgIcPMQ==", "path": "azure.core/1.38.0", "hashPath": "azure.core.1.38.0.nupkg.sha512"}, "Azure.Identity/1.11.4": {"type": "package", "serviceable": true, "sha512": "sha512-Sf4BoE6Q3jTgFkgBkx7qztYOFELBCo+wQgpYDwal/qJ1unBH73ywPztIJKXBXORRzAeNijsuxhk94h0TIMvfYg==", "path": "azure.identity/1.11.4", "hashPath": "azure.identity.1.11.4.nupkg.sha512"}, "Azure.Security.KeyVault.Certificates/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-e2ATU/n2ZDL/S8A8EdrcfKEvKc2BojCrrSpmM+JKnrSTQS32x/W0Ldu8utk+epLKwXvSJRSWtlgdo7X8hG1mCg==", "path": "azure.security.keyvault.certificates/4.6.0", "hashPath": "azure.security.keyvault.certificates.4.6.0.nupkg.sha512"}, "Azure.Security.KeyVault.Secrets/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-vwPceoznuT6glvirZcXlaCQrh1uzTSxpZUi2hRFNumHiS3hVyqIXI5fgWiLtlBzwqPJMTr0flUoSvGKjXXQlfg==", "path": "azure.security.keyvault.secrets/4.6.0", "hashPath": "azure.security.keyvault.secrets.4.6.0.nupkg.sha512"}, "Finbuckle.MultiTenant/6.13.1": {"type": "package", "serviceable": true, "sha512": "sha512-KCO5j2Ehl+R9ZoiC5LSS0jUCLhmGKUCmIaBAB8pymUPhFsTVO1jBDCTD8DXvP177yZUm1v/iJ28/uIoyBUesdQ==", "path": "finbuckle.multitenant/6.13.1", "hashPath": "finbuckle.multitenant.6.13.1.nupkg.sha512"}, "Finbuckle.MultiTenant.AspNetCore/6.13.1": {"type": "package", "serviceable": true, "sha512": "sha512-WLJmfHEQOVToAHOYwzbfW/XXGCna0wTaOyTqLvsiFFTnajXakjjgB8ytZYz0EcLScbP/05IFNzoxp/q8LE+XhQ==", "path": "finbuckle.multitenant.aspnetcore/6.13.1", "hashPath": "finbuckle.multitenant.aspnetcore.6.13.1.nupkg.sha512"}, "Finbuckle.MultiTenant.EntityFrameworkCore/6.13.1": {"type": "package", "serviceable": true, "sha512": "sha512-YjmTR4Mq3DKE2gzM6CYeCtXujYufuTONuc3GkVccFsIHZyDPmufbOfEtOJUKGzXjmRbIVC3vTn/SwekZOxuiWg==", "path": "finbuckle.multitenant.entityframeworkcore/6.13.1", "hashPath": "finbuckle.multitenant.entityframeworkcore.6.13.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rwxaZYHips5M9vqxRkGfJthTx+Ws4O4yCuefn17J371jL3ouC5Ker43h2hXb5yd9BMnImE9rznT75KJHm6bMgg==", "path": "microsoft.aspnetcore.authentication.jwtbearer/8.0.0", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-gx344DYqTjhotuG56JchlqArEvytZGgYuIuaNP/B+FcvXMuexO3mujamvs4qWXH+5g96poi2+p/Cek5KpVmaQA==", "path": "microsoft.aspnetcore.authentication.openidconnect/8.0.1", "hashPath": "microsoft.aspnetcore.authentication.openidconnect.8.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/8.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-MT/jvNoiXUB82drzqtqZqyAfxQH2b0kpEyjjMYrSLmqgAvBkMEKJelbqHazEo5Lxtq43uquPgeBtTuSrVog5lQ==", "path": "microsoft.aspnetcore.cryptography.internal/8.0.10", "hashPath": "microsoft.aspnetcore.cryptography.internal.8.0.10.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/8.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-4jd0g3k2R1L1bhhpVmJOp7rAs76V9XLVuhl8J3sTAcl2dKMS78PsKG1HX75U73WEEwrsM4Bui2/N1/Blwgt5iw==", "path": "microsoft.aspnetcore.cryptography.keyderivation/8.0.10", "hashPath": "microsoft.aspnetcore.cryptography.keyderivation.8.0.10.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-DX2Quy+WRLXPGeWNGtOKyPT2vusNfFI15S9M2DIpaeBqZmXpheoQdlvsPzyn7K8uorcnfUW5ML/vSo26FOQ9BA==", "path": "microsoft.aspnetcore.dataprotection/8.0.1", "hashPath": "microsoft.aspnetcore.dataprotection.8.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4n79+eJnSXaqZIx5c6A+Dtl2bIYwcrAujKDfnDJnTkJa0n5NH4UBCCDNEyONW11UeBYzZb1G4DTE7YWOFbw+9Q==", "path": "microsoft.aspnetcore.dataprotection.abstractions/8.0.1", "hashPath": "microsoft.aspnetcore.dataprotection.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/8.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-vMeY9F3Sq+AiZlquf84rwHOAQBS8nb8kd1RcuoXKPBhHNGBxMLYnr8/e/FCwu7kb14hH/rqWoEuyO4WXpAO6Rw==", "path": "microsoft.aspnetcore.identity.entityframeworkcore/8.0.10", "hashPath": "microsoft.aspnetcore.identity.entityframeworkcore.8.0.10.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512"}, "Microsoft.Bcl.TimeProvider/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-C7kWHJnMRY7EvJev2S8+yJHZ1y7A4ZlLbA4NE+O23BDIAN5mHeqND1m+SKv1ChRS5YlCDW7yAMUe7lttRsJaAA==", "path": "microsoft.bcl.timeprovider/8.0.1", "hashPath": "microsoft.bcl.timeprovider.8.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/8.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-PPkQdIqfR1nU3n6YgGGDk8G+eaYbaAKM1AzIQtlPNTKf10Osg3N9T+iK9AlnSA/ujsK00flPpFHVfJrbuBFS1A==", "path": "microsoft.entityframeworkcore/8.0.10", "hashPath": "microsoft.entityframeworkcore.8.0.10.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-FV0QlcX9INY4kAD2o72uPtyOh0nZut2jB11Jf9mNYBtHay8gDLe+x4AbXFwuQg+eSvofjT7naV82e827zGfyMg==", "path": "microsoft.entityframeworkcore.abstractions/8.0.10", "hashPath": "microsoft.entityframeworkcore.abstractions.8.0.10.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-51KkPIc0EMv/gVXhPIUi6cwJE9Mvh+PLr4Lap4naLcsoGZ0lF2SvOPgUUprwRV3MnN7nyD1XPhT5RJ/p+xFAXw==", "path": "microsoft.entityframeworkcore.analyzers/8.0.10", "hashPath": "microsoft.entityframeworkcore.analyzers.8.0.10.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/8.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-OefBEE47kGKPRPV3OT+FAW6o5BFgLk2D9EoeWVy7NbOepzUneayLQxbVE098FfedTyMwxvZQoDD9LrvZc3MadA==", "path": "microsoft.entityframeworkcore.relational/8.0.10", "hashPath": "microsoft.entityframeworkcore.relational.8.0.10.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "path": "microsoft.extensions.caching.abstractions/8.0.0", "hashPath": "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-HFDnhYLccngrzyGgHkjEDU5FMLn4MpOsr5ElgsBMC4yx6lJh4jeWO7fHS8+TXPq+dgxCmUa/Trl8svObmwW4QA==", "path": "microsoft.extensions.caching.memory/8.0.1", "hashPath": "microsoft.extensions.caching.memory.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "path": "microsoft.extensions.configuration.binder/8.0.0", "hashPath": "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-BmANAnR5Xd4Oqw7yQ75xOAYODybZQRzdeNucg7kS5wWKd2PNnMdYtJ2Vciy0QLylRmv42DGl5+AFL9izA6F1Rw==", "path": "microsoft.extensions.dependencyinjection/8.0.1", "hashPath": "microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-3iE7UF7MQkCv1cxzCahz+Y/guQbTqieyxyaWKhrRO91itI9cOKO76OHeQDahqG4MmW5umr3CcCvGmK92lWNlbg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.2", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3PZp/YSkIXrF7QK7PfC1bkyRYwqOHpWFad8Qx+4wkuumAeXo1NHaxpS9LboNA9OvNSAu+QOVlXbMyoY+pHSqcw==", "path": "microsoft.extensions.diagnostics/8.0.0", "hashPath": "microsoft.extensions.diagnostics.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "path": "microsoft.extensions.hosting.abstractions/8.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cWz4caHwvx0emoYe7NkHPxII/KkTI8R/LC9qdqJqnKv2poTJ4e2qqPGQqvRoQ5kaSA4FU5IV3qFAuLuOhoqULQ==", "path": "microsoft.extensions.http/8.0.0", "hashPath": "microsoft.extensions.http.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Identity.Core/8.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-tS0lNRccAxuAeIVxLBDdklSOL2vAzVUcYqY0njsRbJpNYrXNIKVeQGmhPJgBU0Vrq+iu0LLJ4KLCqGxsOIWpyw==", "path": "microsoft.extensions.identity.core/8.0.10", "hashPath": "microsoft.extensions.identity.core.8.0.10.nupkg.sha512"}, "Microsoft.Extensions.Identity.Stores/8.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-Mwxhj2pLwFcT8BOJ4g7y/WQyQSmZNOalIHmyISFlWykPEKgaQXOlddOCOftSIUqh4IZEYDsVXjeecjl9RLC8Lw==", "path": "microsoft.extensions.identity.stores/8.0.10", "hashPath": "microsoft.extensions.identity.stores.8.0.10.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4x+pzsQEbqxhNf1QYRr5TDkLP9UsLT3A6MdRKDDEgrW7h1ljiEPgTNhKYUhNCCAaVpQECVQ+onA91PTPnIp6Lw==", "path": "microsoft.extensions.logging/8.0.1", "hashPath": "microsoft.extensions.logging.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-nroMDjS7hNBPtkZqVBbSiQaQjWRDxITI8Y7XnDs97rqG3EbzVTNLZQf7bIeUJcaHOV8bca47s1Uxq94+2oGdxA==", "path": "microsoft.extensions.logging.abstractions/8.0.2", "hashPath": "microsoft.extensions.logging.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "path": "microsoft.extensions.options/8.0.2", "hashPath": "microsoft.extensions.options.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.Identity.Abstractions/7.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-/lycPG0hdzmlD1MEpkzYQz5uoveO0pTDc32wKlVQ1c0GB1kLqxA7EV5XsSQp6VPTn/7KOa0crwidp15VLZs3eQ==", "path": "microsoft.identity.abstractions/7.1.0", "hashPath": "microsoft.identity.abstractions.7.1.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.65.0": {"type": "package", "serviceable": true, "sha512": "sha512-RV35ZcJ5/P7n+Zu6J3wmtiDdK6MW5h6EpZ0ojjB9sMwNhGHEJCv829cb3kZ4PZ664llYFv8sbUITWWGvBTqv0Q==", "path": "microsoft.identity.client/4.65.0", "hashPath": "microsoft.identity.client.4.65.0.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-PWnJcznrSGr25MN8ajlc2XIDW4zCFu0U6FkpaNLEWLgd1NgFCp5uDY3mqLDgM8zCN8hqj8yo5wHYfLB2HjcdGw==", "path": "microsoft.identity.client.extensions.msal/4.61.3", "hashPath": "microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512"}, "Microsoft.Identity.Web/3.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-N75Gyb0RAxydepwVEhKTDS4Db/GO2pYsSeMvqmQizIKdmQQmCFYMeHdtSTiEsv/bng9zmNOmwarz/d7ONnTMuQ==", "path": "microsoft.identity.web/3.2.2", "hashPath": "microsoft.identity.web.3.2.2.nupkg.sha512"}, "Microsoft.Identity.Web.Certificate/3.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-Xe1xAvGx9ZLy0wLEsmbsSCpIVjWzhsUAl5EN7RWqd+zeb2fEOqRInCxgS9eLCAD4wfrcATzHt3ELHtB5to/b8w==", "path": "microsoft.identity.web.certificate/3.2.2", "hashPath": "microsoft.identity.web.certificate.3.2.2.nupkg.sha512"}, "Microsoft.Identity.Web.Certificateless/3.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-U/liNxeAitSQ/zmSf9W93PdWvXxIVqsrPF0qGY80dsRQy8yu7SxB2X1a7lgzTOqn2BtczemGLyj1QdP/86wNAQ==", "path": "microsoft.identity.web.certificateless/3.2.2", "hashPath": "microsoft.identity.web.certificateless.3.2.2.nupkg.sha512"}, "Microsoft.Identity.Web.Diagnostics/3.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-ms4/yJzRuZ7Qp0lH73tRWz2XnjHopHxrZv0VgJXrqB1HvTP1uCnoq/DM+qs8XVwG2lvejMFplsv5vU3l2xqOOg==", "path": "microsoft.identity.web.diagnostics/3.2.2", "hashPath": "microsoft.identity.web.diagnostics.3.2.2.nupkg.sha512"}, "Microsoft.Identity.Web.TokenAcquisition/3.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-WWqmmG9nE3C+yzduvh8ieb7Hti26ltfGKy9BtzpMoqLTc/VKbHxFl3d9elRztmNeukjUvOmKvtqbykFkeTtCqQ==", "path": "microsoft.identity.web.tokenacquisition/3.2.2", "hashPath": "microsoft.identity.web.tokenacquisition.3.2.2.nupkg.sha512"}, "Microsoft.Identity.Web.TokenCache/3.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-IdblF2ySKDuv12hP/WT1LPovuVOqYepV6UYzABDaJQaFnA8C4fFAV+ez8yp7rT7Mf3iDtRO0W66ESSnlb0NdQQ==", "path": "microsoft.identity.web.tokencache/3.2.2", "hashPath": "microsoft.identity.web.tokencache.3.2.2.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-QSSDer3kvyTdNq6BefgX4EYi1lsia2zJUh5CfIMZFQUh6BhrXK1WE4i2C9ltUmmuUjoeVVX6AaSo9NZfpTGNdw==", "path": "microsoft.identitymodel.abstractions/8.1.2", "hashPath": "microsoft.identitymodel.abstractions.8.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-AWQINMvtamdYBqtG8q8muyYTfA9i5xRBEsMKQdzOn5xRzhVVDSzsNGYof1docfF3pX4hNRUpHlzs61RP0reZMw==", "path": "microsoft.identitymodel.jsonwebtokens/8.1.2", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-pEn//qKJcEXDsLHLzACFrT3a2kkpIGOXLEYkcuxjqWoeDnbeotu0LY9fF8+Ds9WWpVE9ZGlxXamT0VR8rxaQeA==", "path": "microsoft.identitymodel.logging/8.1.2", "hashPath": "microsoft.identitymodel.logging.8.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.LoggingExtensions/8.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-eS/IBBE19VkUL1DtoIhTOO8X0DyYJ1nWEJ9kpxua2tkjnwvn/H3GZBYOoev0B1bB1rcRv+neYEpA6dyhOR9fxQ==", "path": "microsoft.identitymodel.loggingextensions/8.1.2", "hashPath": "microsoft.identitymodel.loggingextensions.8.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/8.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-Yu3UJWIFX2/5m2MZskECqByr62L8A0uTtTblWIxy0wJNUg0OJGhIK6oRdpcZ8xbSJYD/SOE8psjo5IXRqC3Bsw==", "path": "microsoft.identitymodel.protocols/8.1.2", "hashPath": "microsoft.identitymodel.protocols.8.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-eEtnzZiYymJYaguYeIXyviUocltBQzeYI0bEtot1Nrnl+qklCZARgk+SAaeYfdmc9CYo7aqP5UJ78rTTSTpQGQ==", "path": "microsoft.identitymodel.protocols.openidconnect/8.1.2", "hashPath": "microsoft.identitymodel.protocols.openidconnect.8.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-ZSzGsAA3BY20XHnsp8OjrHFtpd+pQtiu4UJDjPtXwCtEzcE5CjWP/8iZEJXy5AxVEFB0z6EwLSN+T1Fsdpjifw==", "path": "microsoft.identitymodel.tokens/8.1.2", "hashPath": "microsoft.identitymodel.tokens.8.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Validators/8.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-gC4VQ1CGBFlPbx8T6kMLSuzs9SPTpK53L+oT+cbIVvwJFPorw/kvgfwvASGN2BN1Rh8naz5wfVXSKm25LpYlKQ==", "path": "microsoft.identitymodel.validators/8.1.2", "hashPath": "microsoft.identitymodel.validators.8.1.2.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "System.ClientModel/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-I3CVkvxeqFYjIVEP59DnjbeoGNfo/+SZrCLpRz2v/g0gpCHaEMPtWSY0s9k/7jR1rAsLNg2z2u1JRB76tPjnIw==", "path": "system.clientmodel/1.0.0", "hashPath": "system.clientmodel.1.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ==", "path": "system.diagnostics.diagnosticsource/8.0.0", "hashPath": "system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512"}, "System.Formats.Asn1/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqKba7Mm/koKSjKMfW82olQdmfbI5yqeoLV/tidRp7fbh5rmHAQ5raDI/7SU0swTzv+jgqtUGkzmFxuUg0it1A==", "path": "system.formats.asn1/8.0.1", "hashPath": "system.formats.asn1.8.0.1.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-UoidlNYjML1ZbV5s8bLP84VpxDzv8uhHzyt5YkZwqLmFTmtOQheNuTKpR/5UWmO5Ka4JT3kVmhUNq5Li733wTg==", "path": "system.identitymodel.tokens.jwt/8.1.2", "hashPath": "system.identitymodel.tokens.jwt.8.1.2.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ULmp3xoOwNYjOYp4JZ2NK/6NdTgiN1GQXzVVN1njQ7LOZ0d0B9vyMnhyqbIi9Qw4JXj1JgCsitkTShboHRx7Eg==", "path": "system.security.cryptography.pkcs/8.0.0", "hashPath": "system.security.cryptography.pkcs.8.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ehYW0m9ptxpGWvE4zgqongBVWpSDU/JCFD4K7krxkQwSz/sFQjEXCUqpvencjy6DYDbn7Ig09R8GFffu8TtneQ==", "path": "system.security.cryptography.protecteddata/4.7.0", "hashPath": "system.security.cryptography.protecteddata.4.7.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hqu2ztecOf3BYg5q1R7QcyliX9L7r3mfsWtaRitAxcezH8hyZMB7zCmhi186hsUZXk1KxsAHXwyPEW+xvUED6g==", "path": "system.security.cryptography.xml/8.0.1", "hashPath": "system.security.cryptography.xml.8.0.1.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-bAkhgDJ88XTsqczoxEMliSrpijKZHhbJQldhAmObj/RbrN3sU5dcokuXmWJWsdQAhiMJ9bTayWsL1C9fbbCRhw==", "path": "system.text.json/8.0.4", "hashPath": "system.text.json.8.0.4.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "Shared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}