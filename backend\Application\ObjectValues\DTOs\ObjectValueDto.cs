using Application.ObjectMetadataManagement.DTOs;

namespace Application.ObjectValues.DTOs;

/// <summary>
/// ObjectValue DTO
/// </summary>
public class ObjectValueDto
{
    /// <summary>
    /// ObjectValue ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Object metadata ID
    /// </summary>
    public Guid ObjectMetadataId { get; set; }

    /// <summary>
    /// Object name
    /// </summary>
    public string? ObjectName { get; set; }

    /// <summary>
    /// Metadata key
    /// </summary>
    public string? MetadataKey { get; set; }

    /// <summary>
    /// Reference ID for grouping related values
    /// </summary>
    public Guid? RefId { get; set; }

    /// <summary>
    /// Parent object value ID for hierarchical structure
    /// </summary>
    public Guid? ParentObjectValueId { get; set; }

    /// <summary>
    /// Parent object value
    /// </summary>
    public string? ParentObjectValue { get; set; }

    /// <summary>
    /// The actual value as text
    /// </summary>
    public string? Value { get; set; }

    /// <summary>
    /// Number of child values
    /// </summary>
    public int ChildValuesCount { get; set; }

    /// <summary>
    /// Created at
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by
    /// </summary>
    public Guid? CreatedBy { get; set; }

    /// <summary>
    /// Modified at
    /// </summary>
    public DateTime ModifiedAt { get; set; }

    /// <summary>
    /// Modified by
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}

/// <summary>
/// ObjectValue Response DTO with full metadata information
/// </summary>
public class ObjectValueResponseDto
{
    /// <summary>
    /// ObjectValue ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Reference ID for grouping related values
    /// </summary>
    public Guid? RefId { get; set; }

    /// <summary>
    /// Parent object value ID for hierarchical structure
    /// </summary>
    public Guid? ParentObjectValueId { get; set; }

    /// <summary>
    /// The actual value as text
    /// </summary>
    public string? Value { get; set; }

    /// <summary>
    /// Created at
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Modified at
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Object metadata information
    /// </summary>
    public ObjectMetadataDto ObjectMetadata { get; set; } = new();

    /// <summary>
    /// Child values for hierarchical structure
    /// </summary>
    public List<ObjectValueResponseDto> ChildValues { get; set; } = new();
}
