using Domain.Entities;
using Ardalis.Specification;

namespace Application.Features.Specifications;

/// <summary>
/// Specification to get feature by name and product ID
/// </summary>
public class FeatureByNameAndProductIdSpec : Specification<Feature>
{
    public FeatureByNameAndProductIdSpec(string name, Guid productId)
    {
        Query.Where(f => f.Name == name && f.ProductId == productId);
    }
}
