namespace Application.SyncHistories.DTOs;

/// <summary>
/// Update Sync History DTO
/// </summary>
public class UpdateSyncHistoryDto
{
    /// <summary>
    /// Sync History ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Number of records processed in this sync operation
    /// </summary>
    public int RecordCount { get; set; }

    /// <summary>
    /// Sync operation status (e.g., "Success", "Failed", "Partial", "InProgress")
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// When the sync operation completed (null if still in progress)
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// Error log details if the sync operation failed
    /// </summary>
    public string? ErrorLog { get; set; }
}
