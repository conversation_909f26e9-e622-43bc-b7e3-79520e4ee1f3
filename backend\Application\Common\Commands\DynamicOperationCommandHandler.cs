using Abstraction.Common;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;
using System.Diagnostics;

namespace Application.Common.Commands;

/// <summary>
/// Handler for dynamic operation commands
/// </summary>
public class DynamicOperationCommandHandler : 
    IRe<PERSON>Handler<DynamicOperationCommand, Result<DynamicOperationResponse>>,
    IRequestHandler<BulkDynamicOperationCommand, Result<DynamicOperationResponse>>
{
    private readonly IDynamicRepository<Product> _productRepository;
    private readonly IDynamicRepository<Feature> _featureRepository;
    private readonly IDynamicRepository<Domain.Entities.Object> _objectRepository;
    private readonly IDynamicRepository<ProductValue> _productValueRepository;
    private readonly IDynamicRepository<FeatureValue> _featureValueRepository;
    private readonly IDynamicRepository<ObjectValue> _objectValueRepository;
    private readonly ILogger<DynamicOperationCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public DynamicOperationCommandHandler(
        IDynamicRepository<Product> productRepository,
        IDynamicRepository<Feature> featureRepository,
        IDynamicRepository<Domain.Entities.Object> objectRepository,
        IDynamicRepository<ProductValue> productValueRepository,
        IDynamicRepository<FeatureValue> featureValueRepository,
        IDynamicRepository<ObjectValue> objectValueRepository,
        ILogger<DynamicOperationCommandHandler> logger)
    {
        _productRepository = productRepository;
        _featureRepository = featureRepository;
        _objectRepository = objectRepository;
        _productValueRepository = productValueRepository;
        _featureValueRepository = featureValueRepository;
        _objectValueRepository = objectValueRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle single dynamic operation command
    /// </summary>
    public async Task<Result<DynamicOperationResponse>> Handle(DynamicOperationCommand request, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Processing dynamic operation: {EntityType} - {OperationType}", 
                request.EntityType, request.OperationType);

            var response = new DynamicOperationResponse
            {
                Success = true,
                Message = $"Dynamic {request.OperationType} operation completed successfully"
            };

            switch (request.EntityType.ToLowerInvariant())
            {
                case "product":
                    await ProcessSingleOperation(_productRepository, request, response, cancellationToken);
                    break;
                case "feature":
                    await ProcessSingleOperation(_featureRepository, request, response, cancellationToken);
                    break;
                case "object":
                    await ProcessSingleOperation(_objectRepository, request, response, cancellationToken);
                    break;
                case "productvalue":
                    await ProcessSingleOperation(_productValueRepository, request, response, cancellationToken);
                    break;
                case "featurevalue":
                    await ProcessSingleOperation(_featureValueRepository, request, response, cancellationToken);
                    break;
                case "objectvalue":
                    await ProcessSingleOperation(_objectValueRepository, request, response, cancellationToken);
                    break;
                default:
                    return Result<DynamicOperationResponse>.Failure($"Unsupported entity type: {request.EntityType}");
            }

            stopwatch.Stop();
            response.ExecutionTimeMs = stopwatch.ElapsedMilliseconds;
            response.ProcessedCount = 1;

            _logger.LogInformation("Dynamic operation completed in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
            return Result<DynamicOperationResponse>.Success(response);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Dynamic operation failed after {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
            
            var errorResponse = new DynamicOperationResponse
            {
                Success = false,
                Message = "Dynamic operation failed",
                ExecutionTimeMs = stopwatch.ElapsedMilliseconds,
                FailedCount = 1,
                Errors = new List<string> { ex.Message }
            };

            return Result<DynamicOperationResponse>.Success(errorResponse);
        }
    }

    /// <summary>
    /// Handle bulk dynamic operation command
    /// </summary>
    public async Task<Result<DynamicOperationResponse>> Handle(BulkDynamicOperationCommand request, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Processing bulk dynamic operation: {EntityType} - {OperationType} - {Count} items", 
                request.EntityType, request.OperationType, request.PropertyValuesList.Count);

            var response = new DynamicOperationResponse
            {
                Success = true,
                Message = $"Bulk dynamic {request.OperationType} operation completed successfully"
            };

            switch (request.EntityType.ToLowerInvariant())
            {
                case "product":
                    await ProcessBulkOperation(_productRepository, request, response, cancellationToken);
                    break;
                case "feature":
                    await ProcessBulkOperation(_featureRepository, request, response, cancellationToken);
                    break;
                case "object":
                    await ProcessBulkOperation(_objectRepository, request, response, cancellationToken);
                    break;
                case "productvalue":
                    await ProcessBulkOperation(_productValueRepository, request, response, cancellationToken);
                    break;
                case "featurevalue":
                    await ProcessBulkOperation(_featureValueRepository, request, response, cancellationToken);
                    break;
                case "objectvalue":
                    await ProcessBulkOperation(_objectValueRepository, request, response, cancellationToken);
                    break;
                default:
                    return Result<DynamicOperationResponse>.Failure($"Unsupported entity type: {request.EntityType}");
            }

            stopwatch.Stop();
            response.ExecutionTimeMs = stopwatch.ElapsedMilliseconds;
            response.ProcessedCount = request.PropertyValuesList.Count;

            _logger.LogInformation("Bulk dynamic operation completed in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
            return Result<DynamicOperationResponse>.Success(response);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Bulk dynamic operation failed after {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
            
            var errorResponse = new DynamicOperationResponse
            {
                Success = false,
                Message = "Bulk dynamic operation failed",
                ExecutionTimeMs = stopwatch.ElapsedMilliseconds,
                FailedCount = request.PropertyValuesList.Count,
                Errors = new List<string> { ex.Message }
            };

            return Result<DynamicOperationResponse>.Success(errorResponse);
        }
    }

    /// <summary>
    /// Process single operation for specific repository
    /// </summary>
    private async Task ProcessSingleOperation<T>(
        IDynamicRepository<T> repository, 
        DynamicOperationCommand request, 
        DynamicOperationResponse response, 
        CancellationToken cancellationToken) where T : class, Domain.Common.Contracts.IAggregateRoot
    {
        switch (request.OperationType)
        {
            case DynamicOperationType.Insert:
                var insertedEntity = await repository.DynamicInsertAsync(request.PropertyValues, cancellationToken);
                response.InsertedCount = 1;
                response.CreatedIds.Add(((Domain.Common.Contracts.IEntity<Guid>)insertedEntity).Id);
                break;

            case DynamicOperationType.Upsert:
                var upsertedEntity = await repository.DynamicUpsertAsync(request.PropertyValues, cancellationToken);
                response.InsertedCount = 1; // Could be insert or update, simplified for now
                response.CreatedIds.Add(((Domain.Common.Contracts.IEntity<Guid>)upsertedEntity).Id);
                break;

            default:
                throw new NotSupportedException($"Operation type {request.OperationType} not supported for single operations");
        }
    }

    /// <summary>
    /// Process bulk operation for specific repository
    /// </summary>
    private async Task ProcessBulkOperation<T>(
        IDynamicRepository<T> repository, 
        BulkDynamicOperationCommand request, 
        DynamicOperationResponse response, 
        CancellationToken cancellationToken) where T : class, Domain.Common.Contracts.IAggregateRoot
    {
        switch (request.OperationType)
        {
            case DynamicOperationType.BulkInsert:
                var insertedEntities = await repository.DynamicBulkInsertAsync(request.PropertyValuesList, cancellationToken);
                response.InsertedCount = insertedEntities.Count();
                response.CreatedIds.AddRange(insertedEntities.Select(e => ((Domain.Common.Contracts.IEntity<Guid>)e).Id));
                break;

            case DynamicOperationType.BulkUpsert:
                var upsertedEntities = await repository.DynamicBulkUpsertAsync(request.PropertyValuesList, cancellationToken);
                response.InsertedCount = upsertedEntities.Count(); // Could be insert or update, simplified for now
                response.CreatedIds.AddRange(upsertedEntities.Select(e => ((Domain.Common.Contracts.IEntity<Guid>)e).Id));
                break;

            default:
                throw new NotSupportedException($"Operation type {request.OperationType} not supported for bulk operations");
        }
    }
}
