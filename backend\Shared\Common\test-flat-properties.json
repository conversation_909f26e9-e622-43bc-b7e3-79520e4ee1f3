{"operationType": 5, "includeFeatures": true, "includeObjects": true, "includeMetadata": true, "productData": [{"name": "Test Product for Flat Properties", "description": "Testing automatic metadata and values extraction from flat properties", "version": "1.0.0", "isActive": true, "features": [{"name": "Test Feature", "description": "Feature with objects containing flat properties", "isDefault": true, "isActive": true, "objects": [{"name": "Project", "description": "Test project object with flat properties", "isActive": true, "code": "TEST001", "location": "Test Location", "totalArea": 15.5, "budget": 50000000, "status": "Active", "manager": "Test Manager"}, {"name": "Phase", "description": "Test phase object with flat properties", "isActive": true, "number": 1, "startDate": "2023-01-15T00:00:00Z", "endDate": "2025-06-30T00:00:00Z", "unitCount": 400, "budget": 25000000}]}]}]}