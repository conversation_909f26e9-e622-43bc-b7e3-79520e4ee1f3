using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Links Role types to their metadata with IsUnique support
/// </summary>
public class RoleMetadata : AuditableEntity, IAggregateRoot
{

    /// <summary>
    /// Role ID
    /// </summary>
    public Guid RoleId { get; set; }

    /// <summary>
    /// Metadata ID
    /// </summary>
    public Guid MetadataId { get; set; }

    /// <summary>
    /// Whether this metadata is unique for the role
    /// </summary>
    public bool IsUnique { get; set; } = false;

    /// <summary>
    /// Whether this metadata link is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// ShouldVisibleInList
    /// </summary>
    public bool ShouldVisibleInList { get; set; } = true;

    /// <summary>
    /// ShouldVisibleInList
    /// </summary>
    public bool ShouldVisibleInEdit { get; set; } = true;

    /// <summary>
    /// ShouldVisibleInList
    /// </summary>
    public bool ShouldVisibleInCreate { get; set; } = true;

    /// <summary>
    /// ShouldVisibleInList
    /// </summary>
    public bool ShouldVisibleInView { get; set; } = true;

    /// <summary>
    /// ShouldVisibleInList
    /// </summary>
    public bool IsCalculate { get; set; } = true;

    // Navigation properties
    /// <summary>
    /// Role
    /// </summary>
    public virtual Role Role { get; set; } = null!;

    /// <summary>
    /// Metadata definition
    /// </summary>
    public virtual Metadata Metadata { get; set; } = null!;

    /// <summary>
    /// Role values
    /// </summary>
    public virtual ICollection<RoleValue> RoleValues { get; set; } = new List<RoleValue>();
}
