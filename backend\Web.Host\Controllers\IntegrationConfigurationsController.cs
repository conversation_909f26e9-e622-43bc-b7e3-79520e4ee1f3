using Application.IntegrationConfigurations.Commands;
using Application.IntegrationConfigurations.DTOs;
using Application.IntegrationConfigurations.Queries;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;

namespace Web.Host.Controllers;

/// <summary>
/// Integration Configurations controller
/// </summary>
[Route("api/[controller]")]
public class IntegrationConfigurationsController : BaseApiController
{
    /// <summary>
    /// Get all integration configurations with pagination
    /// </summary>
    [HttpGet]
    [TenantIdHeader]
    public async Task<ActionResult<PaginatedResult<ViewIntegrationConfigurationDto>>> GetIntegrationConfigurations([FromQuery] GetIntegrationConfigurationsQuery query)
    {
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get integration configuration by ID
    /// </summary>
    [HttpGet("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ViewIntegrationConfigurationDto>>> GetIntegrationConfigurationById(Guid id)
    {
        return Ok(await Mediator.Send(new GetIntegrationConfigurationByIdQuery(id)));
    }

    /// <summary>
    /// Create a new integration configuration
    /// </summary>
    [HttpPost]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ViewIntegrationConfigurationDto>>> CreateIntegrationConfiguration(CreateIntegrationConfigurationCommand command)
    {
        return Ok(await Mediator.Send(command));
    }

    /// <summary>
    /// Update an existing integration configuration
    /// </summary>
    [HttpPut("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ViewIntegrationConfigurationDto>>> UpdateIntegrationConfiguration(Guid id, UpdateIntegrationConfigurationCommand command)
    {
        command.Id = id;
        return Ok(await Mediator.Send(command));
    }

    /// <summary>
    /// Delete an integration configuration
    /// </summary>
    [HttpDelete("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<bool>>> DeleteIntegrationConfiguration(Guid id)
    {
        return Ok(await Mediator.Send(new DeleteIntegrationConfigurationCommand(id)));
    }

    /// <summary>
    /// Create multiple integration configurations in bulk
    /// </summary>
    [HttpPost("bulk")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<List<ViewIntegrationConfigurationDto>>>> CreateIntegrationConfigurations(CreateIntegrationConfigurationsCommand command)
    {
        return Ok(await Mediator.Send(command));
    }
}
