using Application.Products.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Products.Commands;

/// <summary>
/// Update product command
/// </summary>
public class UpdateProductCommand : IRequest<Result<ProductDto>>
{
    /// <summary>
    /// Product ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Product name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Product description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Product version
    /// </summary>
    public string? Version { get; set; }

    /// <summary>
    /// Whether the product is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Whether user has been imported
    /// </summary>
    public bool IsUserImported { get; set; }

    /// <summary>
    /// Whether role has been assigned
    /// </summary>
    public bool IsRoleAssigned { get; set; }

    /// <summary>
    /// API key for the product
    /// </summary>
    public string? Api<PERSON>ey { get; set; }

    /// <summary>
    /// Whether onboarding process is completed
    /// </summary>
    public bool IsOnboardCompleted { get; set; }

    /// <summary>
    /// Application URL for the product
    /// </summary>
    public string? ApplicationUrl { get; set; }
}
