using Application.ProductMetadata.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.ProductMetadata.Commands;

/// <summary>
/// Update ProductMetadata command
/// </summary>
public class UpdateProductMetadataCommand : IRequest<Result<ProductMetadataDto>>
{
    /// <summary>
    /// ProductMetadata ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Product ID
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Metadata ID
    /// </summary>
    public Guid MetadataId { get; set; }

    /// <summary>
    /// Whether this metadata is unique for the product
    /// </summary>
    public bool IsUnique { get; set; } = false;

    /// <summary>
    /// Whether this metadata link is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
