using MediatR;
using Shared.Common.Response;

namespace Application.FeatureValues.Commands;

/// <summary>
/// Delete FeatureValue command
/// </summary>
public class DeleteFeatureValueCommand : IRequest<Result<bool>>
{
    /// <summary>
    /// FeatureValue ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteFeatureValueCommand(Guid id)
    {
        Id = id;
    }
}
