using Application.Features.Commands;
using Application.Features.DTOs;
using Application.Features.Queries;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;

namespace Web.Host.Controllers;

/// <summary>
/// Features controller
/// </summary>
[Route("api/[controller]")]
public class FeaturesController : BaseApiController
{
    /// <summary>
    /// Get all features with pagination
    /// </summary>
    [HttpGet]
    [TenantIdHeader]
    public async Task<ActionResult<PaginatedResult<FeatureDto>>> GetFeatures([FromQuery] GetFeaturesQuery query)
    {
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get feature by ID
    /// </summary>
    [HttpGet("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<FeatureDto>>> GetFeatureById(Guid id)
    {
        return Ok(await Mediator.Send(new GetFeatureByIdQuery(id)));
    }

    /// <summary>
    /// Create a new feature
    /// </summary>
    [HttpPost]
    [TenantIdHeader]
    public async Task<ActionResult<Result<FeatureDto>>> CreateFeature(CreateFeatureCommand command)
    {
        return Ok(await Mediator.Send(command));
    }

    /// <summary>
    /// Update an existing feature
    /// </summary>
    [HttpPut("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<FeatureDto>>> UpdateFeature(Guid id, UpdateFeatureCommand command)
    {
        command.Id = id;
        return Ok(await Mediator.Send(command));
    }

    /// <summary>
    /// Delete a feature
    /// </summary>
    [HttpDelete("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<bool>>> DeleteFeature(Guid id)
    {
        return Ok(await Mediator.Send(new DeleteFeatureCommand(id)));
    }
}
