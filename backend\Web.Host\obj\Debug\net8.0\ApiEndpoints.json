[{"ContainingType": "Web.Host.Controllers.DataTypesController", "Method": "GetDataTypes", "RelativePath": "api/datatypes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "Category", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "OrderBy", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.DataTypes.DTOs.DataTypeDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.DataTypesController", "Method": "CreateDataType", "RelativePath": "api/datatypes", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.DataTypes.Commands.CreateDataTypeCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.DataTypes.DTOs.DataTypeDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.DataTypesController", "Method": "GetDataTypeById", "RelativePath": "api/datatypes/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.DataTypes.DTOs.DataTypeDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.DataTypesController", "Method": "UpdateDataType", "RelativePath": "api/datatypes/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "command", "Type": "Application.DataTypes.Commands.UpdateDataTypeCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.DataTypes.DTOs.DataTypeDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.DataTypesController", "Method": "DeleteDataType", "RelativePath": "api/datatypes/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.DynamicOperationsController", "Method": "ExecuteBulkDynamicOperation", "RelativePath": "api/dynamicoperations/bulk-execute", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Web.Host.Controllers.BulkDynamicOperationRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.DynamicOperationsController", "Method": "ExecuteDynamicOperation", "RelativePath": "api/dynamicoperations/execute", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Web.Host.Controllers.DynamicOperationRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.DynamicOperationsController", "Method": "ExecuteDynamicFeatureOperation", "RelativePath": "api/dynamicoperations/features", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Web.Host.Controllers.DynamicFeatureOperationRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.DynamicOperationsController", "Method": "ExecuteDynamicObjectOperation", "RelativePath": "api/dynamicoperations/objects", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Web.Host.Controllers.DynamicObjectOperationRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.DynamicOperationsController", "Method": "CreateObjectWithMetadata", "RelativePath": "api/dynamicoperations/objects/create-with-metadata", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Web.Host.Controllers.CreateObjectWithMetadataRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.DynamicOperationsController", "Method": "ExecuteDynamicProductOperation", "RelativePath": "api/dynamicoperations/products", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Web.Host.Controllers.DynamicProductOperationRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.DynamicOperationsController", "Method": "GetSupportedEntityTypes", "RelativePath": "api/dynamicoperations/supported-entities", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.DynamicOperationsController", "Method": "ExecuteDynamicValueOperation", "RelativePath": "api/dynamicoperations/values", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Web.Host.Controllers.DynamicValueOperationRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Web.Host.Controllers.FeaturesController", "Method": "GetFeatures", "RelativePath": "api/features", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ProductId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.Features.DTOs.FeatureDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.FeaturesController", "Method": "CreateFeature", "RelativePath": "api/features", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Features.Commands.CreateFeatureCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Features.DTOs.FeatureDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.FeaturesController", "Method": "GetFeatureById", "RelativePath": "api/features/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Features.DTOs.FeatureDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.FeaturesController", "Method": "UpdateFeature", "RelativePath": "api/features/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "command", "Type": "Application.Features.Commands.UpdateFeatureCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Features.DTOs.FeatureDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.FeaturesController", "Method": "DeleteFeature", "RelativePath": "api/features/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.FieldMappingsController", "Method": "GetFieldMappings", "RelativePath": "api/fieldmappings", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "ApiName", "Type": "System.String", "IsRequired": false}, {"Name": "SourceType", "Type": "System.String", "IsRequired": false}, {"Name": "ObjectMetadataId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "RoleId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "TargetObjectName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.FieldMappings.DTOs.ViewFieldMappingDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.FieldMappingsController", "Method": "CreateFieldMapping", "RelativePath": "api/fieldmappings", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.FieldMappings.Commands.CreateFieldMappingCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.FieldMappings.DTOs.ViewFieldMappingDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.FieldMappingsController", "Method": "GetFieldMappingById", "RelativePath": "api/fieldmappings/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.FieldMappings.DTOs.ViewFieldMappingDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.FieldMappingsController", "Method": "UpdateFieldMapping", "RelativePath": "api/fieldmappings/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "command", "Type": "Application.FieldMappings.Commands.UpdateFieldMappingCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.FieldMappings.DTOs.ViewFieldMappingDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.FieldMappingsController", "Method": "DeleteFieldMapping", "RelativePath": "api/fieldmappings/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.FieldMappingsController", "Method": "CreateFieldMappings", "RelativePath": "api/fieldmappings/bulk", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.FieldMappings.Commands.CreateFieldMappingsCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Collections.Generic.List`1[[Application.FieldMappings.DTOs.ViewFieldMappingDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationApisController", "Method": "GetIntegrationApis", "RelativePath": "api/integrationapis", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ProductId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.IntegrationApis.DTOs.ViewIntegrationApiDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationApisController", "Method": "CreateIntegrationApi", "RelativePath": "api/integrationapis", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.IntegrationApis.Commands.CreateIntegrationApiCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.IntegrationApis.DTOs.ViewIntegrationApiDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationApisController", "Method": "GetIntegrationApiById", "RelativePath": "api/integrationapis/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.IntegrationApis.DTOs.ViewIntegrationApiDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationApisController", "Method": "UpdateIntegrationApi", "RelativePath": "api/integrationapis/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "command", "Type": "Application.IntegrationApis.Commands.UpdateIntegrationApiCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.IntegrationApis.DTOs.ViewIntegrationApiDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationApisController", "Method": "DeleteIntegrationApi", "RelativePath": "api/integrationapis/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationApisController", "Method": "CreateIntegrationApis", "RelativePath": "api/integrationapis/bulk", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.IntegrationApis.Commands.CreateIntegrationApisCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Collections.Generic.List`1[[Application.IntegrationApis.DTOs.ViewIntegrationApiDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationConfigurationsController", "Method": "GetIntegrationConfigurations", "RelativePath": "api/integrationconfigurations", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IntegrationId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IntegrationApiId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ObjectId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Direction", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.IntegrationConfigurations.DTOs.ViewIntegrationConfigurationDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationConfigurationsController", "Method": "CreateIntegrationConfiguration", "RelativePath": "api/integrationconfigurations", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.IntegrationConfigurations.Commands.CreateIntegrationConfigurationCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.IntegrationConfigurations.DTOs.ViewIntegrationConfigurationDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationConfigurationsController", "Method": "GetIntegrationConfigurationById", "RelativePath": "api/integrationconfigurations/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.IntegrationConfigurations.DTOs.ViewIntegrationConfigurationDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationConfigurationsController", "Method": "UpdateIntegrationConfiguration", "RelativePath": "api/integrationconfigurations/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "command", "Type": "Application.IntegrationConfigurations.Commands.UpdateIntegrationConfigurationCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.IntegrationConfigurations.DTOs.ViewIntegrationConfigurationDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationConfigurationsController", "Method": "DeleteIntegrationConfiguration", "RelativePath": "api/integrationconfigurations/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationConfigurationsController", "Method": "CreateIntegrationConfigurations", "RelativePath": "api/integrationconfigurations/bulk", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.IntegrationConfigurations.Commands.CreateIntegrationConfigurationsCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Collections.Generic.List`1[[Application.IntegrationConfigurations.DTOs.ViewIntegrationConfigurationDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationsController", "Method": "GetIntegrations", "RelativePath": "api/integrations", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ProductId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "AuthType", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.Integrations.DTOs.IntegrationDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationsController", "Method": "CreateIntegration", "RelativePath": "api/integrations", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Integrations.Commands.CreateIntegrationCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Integrations.DTOs.IntegrationDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationsController", "Method": "GetIntegrationById", "RelativePath": "api/integrations/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Integrations.DTOs.IntegrationDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationsController", "Method": "UpdateIntegration", "RelativePath": "api/integrations/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "command", "Type": "Application.Integrations.Commands.UpdateIntegrationCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Integrations.DTOs.IntegrationDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationsController", "Method": "DeleteIntegration", "RelativePath": "api/integrations/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.IntegrationsController", "Method": "CreateIntegrations", "RelativePath": "api/integrations/bulk", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Integrations.Commands.CreateIntegrationsCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Collections.Generic.List`1[[Application.Integrations.DTOs.IntegrationDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.MetadataController", "Method": "GetMetadata", "RelativePath": "api/metadata", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "DataTypeId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsVisible", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "OrderBy", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.MetadataManagement.DTOs.MetadataDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.MetadataController", "Method": "CreateMetadata", "RelativePath": "api/metadata", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.MetadataManagement.Commands.CreateMetadataCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.MetadataManagement.DTOs.MetadataDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.MetadataController", "Method": "GetMetadataById", "RelativePath": "api/metadata/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.MetadataManagement.DTOs.MetadataDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.MetadataController", "Method": "UpdateMetadata", "RelativePath": "api/metadata/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "command", "Type": "Application.MetadataManagement.Commands.UpdateMetadataCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.MetadataManagement.DTOs.MetadataDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.MetadataController", "Method": "DeleteMetadata", "RelativePath": "api/metadata/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.ObjectsController", "Method": "CreateObject", "RelativePath": "api/objects", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Objects.Commands.CreateObjectCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Objects.DTOs.ObjectDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.ProductsController", "Method": "GetProducts", "RelativePath": "api/products", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PaginatedResult`1[[Application.Products.DTOs.ProductDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.ProductsController", "Method": "CreateProduct", "RelativePath": "api/products", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Products.Commands.CreateProductCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Products.DTOs.ProductDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.ProductsController", "Method": "GetProductById", "RelativePath": "api/products/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Products.DTOs.ProductDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.ProductsController", "Method": "UpdateProduct", "RelativePath": "api/products/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "command", "Type": "Application.Products.Commands.UpdateProductCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Products.DTOs.ProductDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.ProductsController", "Method": "CreateProductWithSubscription", "RelativePath": "api/products/with-subscription", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Products.Commands.CreateProductWithSubscriptionCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.Products.DTOs.ProductWithSubscriptionDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.RolesController", "Method": "GetAllAsync", "RelativePath": "api/roles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Collections.Generic.List`1[[Abstraction.Identity.Dtos.RoleDto, Abstraction, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.RolesController", "Method": "CreateAsync", "RelativePath": "api/roles", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Abstraction.Identity.Dtos.CreateRoleRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.RolesController", "Method": "UpdateAsync", "RelativePath": "api/roles", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Abstraction.Identity.Dtos.UpdateRoleRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.RolesController", "Method": "GetByIdAsync", "RelativePath": "api/roles/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Abstraction.Identity.Dtos.RoleDto, Abstraction, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.RolesController", "Method": "DeleteAsync", "RelativePath": "api/roles/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.MultiTenancy.TenantsController", "Method": "GetAllAsync", "RelativePath": "api/tenants", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Collections.Generic.List`1[[Abstraction.MultiTenancy.Dtos.TenantDto, Abstraction, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.MultiTenancy.TenantsController", "Method": "CreateAsync", "RelativePath": "api/tenants", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Abstraction.MultiTenancy.Dtos.TenantDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.MultiTenancy.TenantsController", "Method": "UpdateAsync", "RelativePath": "api/tenants", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Abstraction.MultiTenancy.Dtos.TenantDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.MultiTenancy.TenantsController", "Method": "GetByIdAsync", "RelativePath": "api/tenants/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Application.MultiTenancy.DTOs.TenantWithProductDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.MultiTenancy.TenantsController", "Method": "DeleteAsync", "RelativePath": "api/tenants/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.MultiTenancy.TenantsController", "Method": "ActivateAsync", "RelativePath": "api/tenants/{id}/activate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.MultiTenancy.TenantsController", "Method": "DeactivateAsync", "RelativePath": "api/tenants/{id}/deactivate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.TokensController", "Method": "GetTokenAsync", "RelativePath": "api/tokens", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Abstraction.Identity.Dtos.TokenRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Abstraction.Identity.Dtos.TokenResponse, Abstraction, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.TokensController", "Method": "RefreshAsync", "RelativePath": "api/tokens/refresh", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Abstraction.Identity.Dtos.RefreshTokenRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Abstraction.Identity.Dtos.TokenResponse, Abstraction, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "GetAllAsync", "RelativePath": "api/users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.Collections.Generic.List`1[[Abstraction.Identity.Dtos.UserDetailsDto, Abstraction, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "RegisterAsync", "RelativePath": "api/users", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Abstraction.Identity.Dtos.RegisterUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "GetByIdAsync", "RelativePath": "api/users/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[Abstraction.Identity.Dtos.UserDetailsDto, Abstraction, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "UpdateAsync", "RelativePath": "api/users/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Abstraction.Identity.Dtos.UpdateUserRequest", "IsRequired": true}, {"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "ChangePasswordAsync", "RelativePath": "api/users/{id}/change-password", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Abstraction.Identity.Dtos.ChangePasswordRequest", "IsRequired": true}, {"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "GetUserRolesAsync", "RelativePath": "api/users/{userId}/roles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.ApiResponse`1[[System.Collections.Generic.List`1[[Application.Identity.DTOs.UserRoleDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "UpdateUserRolesAsync", "RelativePath": "api/users/{userId}/roles", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Guid", "IsRequired": true}, {"Name": "userRoles", "Type": "System.Collections.Generic.List`1[[Application.Identity.DTOs.UserRoleDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "AssignRolesToUserAsync", "RelativePath": "api/users/{userId}/roles/assign", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Guid", "IsRequired": true}, {"Name": "roleNames", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "RemoveRolesFromUserAsync", "RelativePath": "api/users/{userId}/roles/remove", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Guid", "IsRequired": true}, {"Name": "roleNames", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "BulkCreateUsersAsync", "RelativePath": "api/users/bulk-create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Identity.Commands.BulkCreateUsersCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.ApiResponse`1[[Application.Identity.Commands.BulkCreateUsersResponse, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "BulkUpdateUserRolesAsync", "RelativePath": "api/users/bulk-update-roles", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Identity.Commands.BulkUpdateUserRolesCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.ApiResponse`1[[Application.Identity.Commands.BulkUpdateUserRolesResponse, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "CreateUserAsync", "RelativePath": "api/users/create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Application.Identity.Commands.CreateUserCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.ApiResponse`1[[Application.Identity.DTOs.UserDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "ForgotPasswordAsync", "RelativePath": "api/users/forgot-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Abstraction.Identity.Dtos.ForgotPasswordRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "GetPaginatedAsync", "RelativePath": "api/users/paginated", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "searchString", "Type": "System.String", "IsRequired": false}, {"Name": "includeInactive", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "Shared.Common.Response.PagedResponse`2[[Abstraction.Identity.Dtos.UserDetailsDto, Abstraction, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "ResetPasswordAsync", "RelativePath": "api/users/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Abstraction.Identity.Dtos.ResetPasswordRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Web.Host.Controllers.Identity.UsersController", "Method": "GetUsersInRoleAsync", "RelativePath": "api/users/roles/{roleName}/users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Common.Response.ApiResponse`1[[System.Collections.Generic.List`1[[Application.Identity.DTOs.UserDto, Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]