using Ardalis.Specification;
using ObjectEntity = Domain.Entities.Object;

namespace Application.Objects.Specifications;

/// <summary>
/// Specification to get Objects with metadata information
/// </summary>
public class ObjectWithMetadataSpec : Specification<ObjectEntity>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ObjectWithMetadataSpec(
        string? searchTerm = null,
        Guid? featureId = null,
        bool? isActive = null,
        string? orderBy = null,
        int skip = 0,
        int take = 0)
    {
        Query.Where(o => !o.IsDeleted);

        // Include related data
        Query.Include(o => o.Feature);
        Query.Include(o => o.ParentObject);
        Query.Include(o => o.ObjectMetadata.Where(om => om.IsActive && !om.IsDeleted))
             .ThenInclude(om => om.Metadata)
             .ThenInclude(m => m.DataType);

        // Apply filters
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            Query.Where(o => o.Name.Contains(searchTerm) || 
                           (o.Description != null && o.Description.Contains(searchTerm)));
        }

        if (featureId.HasValue)
        {
            Query.Where(o => o.FeatureId == featureId.Value);
        }

        if (isActive.HasValue)
        {
            Query.Where(o => o.IsActive == isActive.Value);
        }

        // Apply ordering
        if (!string.IsNullOrWhiteSpace(orderBy))
        {
            switch (orderBy.ToLower())
            {
                case "name":
                    Query.OrderBy(o => o.Name);
                    break;
                case "name_desc":
                    Query.OrderByDescending(o => o.Name);
                    break;
                case "createdat":
                    Query.OrderBy(o => o.CreatedAt);
                    break;
                case "createdat_desc":
                    Query.OrderByDescending(o => o.CreatedAt);
                    break;
                case "featurename":
                    Query.OrderBy(o => o.Feature.Name);
                    break;
                case "featurename_desc":
                    Query.OrderByDescending(o => o.Feature.Name);
                    break;
                default:
                    Query.OrderBy(o => o.Name);
                    break;
            }
        }
        else
        {
            Query.OrderBy(o => o.Name);
        }

        // Apply pagination
        if (skip > 0)
        {
            Query.Skip(skip);
        }

        if (take > 0)
        {
            Query.Take(take);
        }
    }
}
