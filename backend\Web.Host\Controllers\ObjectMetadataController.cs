using Application.ObjectMetadataManagement.DTOs;
using Application.ObjectMetadataManagement.Queries;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;

namespace Web.Host.Controllers;

/// <summary>
/// ObjectMetadata controller
/// </summary>
[Route("api/[controller]")]
public class ObjectMetadataController : BaseApiController
{
    /// <summary>
    /// Get all object metadata with pagination
    /// </summary>
    [HttpGet]
    [TenantIdHeader]
    public async Task<ActionResult<PaginatedResult<ObjectMetadataDto>>> GetObjectMetadataAsync([FromQuery] GetObjectMetadataQuery query)
    {
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get object metadata by ID
    /// </summary>
    [HttpGet("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ObjectMetadataDto>>> GetObjectMetadataByIdAsync(Guid id)
    {
        return Ok(await Mediator.Send(new GetObjectMetadataByIdQuery(id)));
    }
}
