using FluentValidation;

namespace Application.Products.Commands;

/// <summary>
/// Validator for CreateProductWithSubscriptionCommand
/// </summary>
public class CreateProductWithSubscriptionCommandValidator : AbstractValidator<CreateProductWithSubscriptionCommand>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public CreateProductWithSubscriptionCommandValidator()
    {
        // Product validation
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Product name is required.")
            .MaximumLength(255)
            .WithMessage("Product name cannot exceed 255 characters.");

        RuleFor(x => x.Description)
            .MaximumLength(1000)
            .WithMessage("Product description cannot exceed 1000 characters.")
            .When(x => !string.IsNullOrEmpty(x.Description));

        RuleFor(x => x.Version)
            .MaximumLength(50)
            .WithMessage("Product version cannot exceed 50 characters.")
            .When(x => !string.IsNullOrEmpty(x.Version));

        // Subscription validation
        RuleFor(x => x.SubscriptionType)
            .MaximumLength(50)
            .WithMessage("Subscription type cannot exceed 50 characters.")
            .When(x => !string.IsNullOrEmpty(x.SubscriptionType));

        RuleFor(x => x.PricingTier)
            .MaximumLength(50)
            .WithMessage("Pricing tier cannot exceed 50 characters.")
            .When(x => !string.IsNullOrEmpty(x.PricingTier));

        RuleFor(x => x.EndDate)
            .GreaterThan(DateTime.UtcNow)
            .WithMessage("End date must be in the future.")
            .When(x => x.EndDate.HasValue);
    }
}
