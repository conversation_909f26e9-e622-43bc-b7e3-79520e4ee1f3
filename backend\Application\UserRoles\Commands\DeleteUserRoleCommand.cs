using MediatR;
using Shared.Common.Response;

namespace Application.UserRoles.Commands;

/// <summary>
/// Delete UserRole command
/// </summary>
public class DeleteUserRoleCommand : IRequest<Result<bool>>
{
    /// <summary>
    /// UserRole ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteUserRoleCommand(Guid id)
    {
        Id = id;
    }
}
