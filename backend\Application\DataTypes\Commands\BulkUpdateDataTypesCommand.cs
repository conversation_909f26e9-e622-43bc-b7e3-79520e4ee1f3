using Application.DataTypes.DTOs;
using MediatR;
using Shared.Common.Response;
using System.ComponentModel.DataAnnotations;

namespace Application.DataTypes.Commands;

/// <summary>
/// Bulk update data types command
/// </summary>
public class BulkUpdateDataTypesCommand : IRequest<Result<BulkUpdateDataTypesResponse>>
{
    /// <summary>
    /// List of data types to update
    /// </summary>
    [Required]
    public List<UpdateDataTypeCommand> DataTypes { get; set; } = new();

    /// <summary>
    /// Whether to continue processing if one item fails
    /// </summary>
    public bool ContinueOnError { get; set; } = true;

    /// <summary>
    /// Whether to validate all items before updating any
    /// </summary>
    public bool ValidateBeforeUpdate { get; set; } = true;

    /// <summary>
    /// Whether to create data types that don't exist (upsert behavior)
    /// </summary>
    public bool CreateIfNotExists { get; set; } = false;
}

/// <summary>
/// Response for bulk update data types operation
/// </summary>
public class BulkUpdateDataTypesResponse
{
    /// <summary>
    /// Total number of data types requested to be updated
    /// </summary>
    public int TotalRequested { get; set; }

    /// <summary>
    /// Number of data types successfully updated
    /// </summary>
    public int SuccessfullyUpdated { get; set; }

    /// <summary>
    /// Number of data types that failed to update
    /// </summary>
    public int Failed { get; set; }

    /// <summary>
    /// Number of data types created (when CreateIfNotExists is true)
    /// </summary>
    public int Created { get; set; }

    /// <summary>
    /// Number of data types not found
    /// </summary>
    public int NotFound { get; set; }

    /// <summary>
    /// List of successfully updated data types
    /// </summary>
    public List<DataTypeDto> UpdatedDataTypes { get; set; } = new();

    /// <summary>
    /// List of newly created data types (when CreateIfNotExists is true)
    /// </summary>
    public List<DataTypeDto> CreatedDataTypes { get; set; } = new();

    /// <summary>
    /// List of errors that occurred during update
    /// </summary>
    public List<BulkDataTypeUpdateError> Errors { get; set; } = new();

    /// <summary>
    /// Overall operation message
    /// </summary>
    public string Message { get; set; } = string.Empty;
}

/// <summary>
/// Error information for bulk data type update
/// </summary>
public class BulkDataTypeUpdateError
{
    /// <summary>
    /// Index of the data type in the original request
    /// </summary>
    public int Index { get; set; }

    /// <summary>
    /// ID of the data type that failed
    /// </summary>
    public Guid? DataTypeId { get; set; }

    /// <summary>
    /// Name of the data type that failed
    /// </summary>
    public string? DataTypeName { get; set; }

    /// <summary>
    /// Error message
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// Error type (Validation, NotFound, Database, etc.)
    /// </summary>
    public string ErrorType { get; set; } = string.Empty;
}
