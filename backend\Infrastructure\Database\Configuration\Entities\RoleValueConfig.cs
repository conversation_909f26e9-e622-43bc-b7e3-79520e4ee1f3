using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for RoleValue entity
/// </summary>
public class RoleValueConfig : IEntityTypeConfiguration<RoleValue>
{
    public void Configure(EntityTypeBuilder<RoleValue> builder)
    {
        builder.ToTable("RoleValues", "Genp");

        // Primary key
        builder.HasKey(e => e.Id);

        // Properties
        builder.Property(e => e.RoleMetadataId)
            .IsRequired();

        builder.Property(e => e.RefId);

        builder.Property(e => e.Value)
            .HasColumnType("TEXT");

        builder.Property(e => e.CreatedAt)
            .HasDefaultValueSql("NOW()")
            .IsRequired();

        builder.Property(e => e.CreatedBy);

        builder.Property(e => e.ModifiedAt)
            .HasDefaultValueSql("NOW()")
            .IsRequired();

        builder.Property(e => e.ModifiedBy);

        // Indexes
        builder.HasIndex(e => e.RoleMetadataId)
            .HasDatabaseName("IX_RoleValues_RoleMetadataId");

        builder.HasIndex(e => e.RefId)
            .HasDatabaseName("IX_RoleValues_RefId");

        builder.HasIndex(e => e.CreatedAt)
            .HasDatabaseName("IX_RoleValues_CreatedAt");

        // Multi-tenancy
        builder.IsMultiTenant();

        // Relationships
        builder.HasOne(e => e.RoleMetadata)
            .WithMany(e => e.RoleValues)
            .HasForeignKey(e => e.RoleMetadataId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
