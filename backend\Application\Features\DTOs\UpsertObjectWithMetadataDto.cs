using System.ComponentModel.DataAnnotations;
using System.Text.Json;

namespace Application.Features.DTOs;

/// <summary>
/// Request DTO for upserting an object with metadata and values
/// </summary>
public class UpsertObjectWithMetadataRequestDto
{
    /// <summary>
    /// Object information to create or update
    /// </summary>
    [Required]
    public ObjectUpsertDto Object { get; set; } = new();
}

/// <summary>
/// Object upsert DTO
/// </summary>
public class ObjectUpsertDto
{
    /// <summary>
    /// Object ID - if null, creates new object; if provided, updates existing object
    /// </summary>
    public Guid? Id { get; set; }

    /// <summary>
    /// Feature ID this object belongs to (required for new objects)
    /// </summary>
    public Guid? FeatureId { get; set; }

    /// <summary>
    /// Parent Object ID (for child objects)
    /// </summary>
    public Guid? ParentObjectId { get; set; }

    /// <summary>
    /// Object name
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Object description
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Whether the object is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Object metadata as JSON (dynamic structure for metadata definition)
    /// </summary>
    public JsonElement? MetaJson { get; set; }

    /// <summary>
    /// Object metadata values (array of value instances)
    /// </summary>
    public List<Dictionary<string, object>> MetaValues { get; set; } = new();
}

/// <summary>
/// Response DTO for object upsert operation
/// </summary>
public class UpsertObjectWithMetadataResponseDto
{
    /// <summary>
    /// Object information after upsert
    /// </summary>
    public ObjectUpsertResultDto Object { get; set; } = new();

    /// <summary>
    /// Total number of metadata entries created or updated
    /// </summary>
    public int MetadataProcessed { get; set; }

    /// <summary>
    /// Total number of object values created
    /// </summary>
    public int ObjectValuesCreated { get; set; }

    /// <summary>
    /// Total number of object values updated
    /// </summary>
    public int ObjectValuesUpdated { get; set; }

    /// <summary>
    /// Processing summary
    /// </summary>
    public ObjectUpsertProcessingSummaryDto ProcessingSummary { get; set; } = new();
}

/// <summary>
/// Object upsert result DTO
/// </summary>
public class ObjectUpsertResultDto
{
    /// <summary>
    /// Object ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Object name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Object description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Feature ID this object belongs to
    /// </summary>
    public Guid FeatureId { get; set; }

    /// <summary>
    /// Parent Object ID (if this is a child object)
    /// </summary>
    public Guid? ParentObjectId { get; set; }

    /// <summary>
    /// Whether this was a new object or existing object update
    /// </summary>
    public bool IsNewObject { get; set; }

    /// <summary>
    /// Whether the object is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Number of metadata entries linked to this object
    /// </summary>
    public int MetadataLinked { get; set; }

    /// <summary>
    /// Number of new metadata entries created
    /// </summary>
    public int NewMetadataCreated { get; set; }

    /// <summary>
    /// List of metadata keys processed
    /// </summary>
    public List<string> MetadataKeysProcessed { get; set; } = new();
}

/// <summary>
/// Processing summary for object upsert operation
/// </summary>
public class ObjectUpsertProcessingSummaryDto
{
    /// <summary>
    /// Total processing time in milliseconds
    /// </summary>
    public long ProcessingTimeMs { get; set; }

    /// <summary>
    /// Number of database transactions executed
    /// </summary>
    public int TransactionsExecuted { get; set; }

    /// <summary>
    /// Any warnings encountered during processing
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// Data type mappings used during processing
    /// </summary>
    public Dictionary<string, Guid> DataTypeMappings { get; set; } = new();

    /// <summary>
    /// Operation type performed
    /// </summary>
    public string OperationType { get; set; } = string.Empty; // "Create" or "Update"

    /// <summary>
    /// Metadata operations performed
    /// </summary>
    public List<MetadataOperationDto> MetadataOperations { get; set; } = new();
}

/// <summary>
/// Metadata operation details
/// </summary>
public class MetadataOperationDto
{
    /// <summary>
    /// Metadata key
    /// </summary>
    public string MetadataKey { get; set; } = string.Empty;

    /// <summary>
    /// Operation performed (Created, Updated, Linked)
    /// </summary>
    public string Operation { get; set; } = string.Empty;

    /// <summary>
    /// Data type used
    /// </summary>
    public string DataType { get; set; } = string.Empty;

    /// <summary>
    /// Metadata ID
    /// </summary>
    public Guid MetadataId { get; set; }
}
