using Application.FieldMappings.DTOs;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.FieldMappings.Commands;

/// <summary>
/// Update field mapping command handler
/// </summary>
public class UpdateFieldMappingCommandHandler : IRequestHandler<UpdateFieldMappingCommand, Result<ViewFieldMappingDto>>
{
    private readonly IRepository<FieldMapping> _fieldMappingRepository;
    private readonly IReadRepository<Domain.Entities.ObjectMetadata> _objectMetadataRepository;
    private readonly IReadRepository<User> _userRepository;
    private readonly IReadRepository<Role> _roleRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpdateFieldMappingCommandHandler(
        IRepository<FieldMapping> fieldMappingRepository,
        IReadRepository<Domain.Entities.ObjectMetadata> objectMetadataRepository,
        IReadRepository<User> userRepository,
        IReadRepository<Role> roleRepository)
    {
        _fieldMappingRepository = fieldMappingRepository;
        _objectMetadataRepository = objectMetadataRepository;
        _userRepository = userRepository;
        _roleRepository = roleRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<ViewFieldMappingDto>> Handle(UpdateFieldMappingCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Get existing field mapping
            var fieldMapping = await _fieldMappingRepository.GetByIdAsync(request.Id, cancellationToken);
            if (fieldMapping == null)
            {
                return Result<ViewFieldMappingDto>.Failure("Field mapping not found.");
            }

            // Validate optional foreign keys
            Domain.Entities.ObjectMetadata? objectMetadata = null;
            if (request.ObjectMetadataId.HasValue)
            {
                objectMetadata = await _objectMetadataRepository.GetByIdAsync(request.ObjectMetadataId.Value, cancellationToken);
                if (objectMetadata == null)
                {
                    return Result<ViewFieldMappingDto>.Failure("Object metadata not found.");
                }
            }

            User? user = null;
            if (request.UserId.HasValue)
            {
                user = await _userRepository.GetByIdAsync(request.UserId.Value, cancellationToken);
                if (user == null)
                {
                    return Result<ViewFieldMappingDto>.Failure("User not found.");
                }
            }

            Role? role = null;
            if (request.RoleId.HasValue)
            {
                role = await _roleRepository.GetByIdAsync(request.RoleId.Value, cancellationToken);
                if (role == null)
                {
                    return Result<ViewFieldMappingDto>.Failure("Role not found.");
                }
            }

            // Update field mapping properties
            fieldMapping.ApiName = request.ApiName;
            fieldMapping.SourceField = request.SourceField;
            fieldMapping.SourceType = request.SourceType;
            fieldMapping.ObjectMetadataId = request.ObjectMetadataId;
            fieldMapping.UserId = request.UserId;
            fieldMapping.RoleId = request.RoleId;
            fieldMapping.TargetObjectName = request.TargetObjectName;
            fieldMapping.Notes = request.Notes;

            await _fieldMappingRepository.UpdateAsync(fieldMapping, cancellationToken);

            // Create view DTO
            var viewDto = new ViewFieldMappingDto
            {
                Id = fieldMapping.Id,
                ApiName = fieldMapping.ApiName,
                SourceField = fieldMapping.SourceField,
                SourceType = fieldMapping.SourceType,
                ObjectMetadataId = fieldMapping.ObjectMetadataId,
                ObjectMetadataKey = objectMetadata?.Metadata?.MetadataKey,
                UserId = fieldMapping.UserId,
                UserName = user?.UserName,
                RoleId = fieldMapping.RoleId,
                RoleName = role?.Name,
                TargetObjectName = fieldMapping.TargetObjectName,
                Notes = fieldMapping.Notes,
                CreatedAt = fieldMapping.CreatedAt,
                CreatedBy = fieldMapping.CreatedBy,
                ModifiedAt = fieldMapping.ModifiedAt,
                ModifiedBy = fieldMapping.ModifiedBy
            };

            return Result<ViewFieldMappingDto>.Success(viewDto);
        }
        catch (Exception ex)
        {
            return Result<ViewFieldMappingDto>.Failure($"Failed to update field mapping: {ex.Message}");
        }
    }
}
