using Ardalis.Specification;
using Domain.Entities;

namespace Application.ObjectValues.Specifications;

/// <summary>
/// Specification to check if RefId exists for a specific ObjectId and tenant
/// </summary>
public class RefIdExistsSpec : Specification<ObjectValue>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public RefIdExistsSpec(Guid refId, Guid objectId, string tenantId)
    {
        Query.Where(ov => ov.RefId == refId &&
                         ov.ObjectMetadata.ObjectId == objectId &&
                         !ov.IsDeleted);

        // Include object for checks
        Query.Include(ov => ov.ObjectMetadata)
             .ThenInclude(om => om.Object);

        // Active filters (tenant isolation handled by multi-tenant framework)
        Query.Where(ov => !ov.ObjectMetadata.Object.IsDeleted);

        // Take only one record for existence check
        Query.Take(1);
    }
}
