using Application.DataTypes.DTOs;
using MediatR;
using Shared.Common.Response;
using System.ComponentModel.DataAnnotations;

namespace Application.DataTypes.Commands;

/// <summary>
/// Command to update an existing data type
/// </summary>
public class UpdateDataTypeCommand : IRequest<Result<DataTypeDto>>
{
    /// <summary>
    /// ID of the data type to update
    /// </summary>
    [Required]
    public Guid Id { get; set; }

    /// <summary>
    /// Name of the data type (unique identifier)
    /// </summary>
    [Required]
    [StringLength(100, MinimumLength = 1)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Display name for the data type
    /// </summary>
    [StringLength(200)]
    public string? DisplayName { get; set; }

    /// <summary>
    /// Category of the data type
    /// </summary>
    [StringLength(100)]
    public string? Category { get; set; }

    /// <summary>
    /// UI component type for rendering
    /// </summary>
    [StringLength(50)]
    public string? UiComponent { get; set; }

    /// <summary>
    /// Validation pattern (regex)
    /// </summary>
    [StringLength(500)]
    public string? ValidationPattern { get; set; }

    /// <summary>
    /// Minimum length for string values
    /// </summary>
    public int? MinLength { get; set; }

    /// <summary>
    /// Maximum length for string values
    /// </summary>
    public int? MaxLength { get; set; }

    /// <summary>
    /// Minimum value for numeric types
    /// </summary>
    public decimal? MinValue { get; set; }

    /// <summary>
    /// Maximum value for numeric types
    /// </summary>
    public decimal? MaxValue { get; set; }

    /// <summary>
    /// Number of decimal places for numeric types
    /// </summary>
    public int? DecimalPlaces { get; set; }

    /// <summary>
    /// Default value for the data type
    /// </summary>
    [StringLength(500)]
    public string? DefaultValue { get; set; }

    /// <summary>
    /// Whether this data type is required
    /// </summary>
    public bool IsRequired { get; set; } = false;

    /// <summary>
    /// Whether this data type is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Whether multiple values are allowed
    /// </summary>
    public bool AllowMultiple { get; set; } = false;

    /// <summary>
    /// Sort order for display
    /// </summary>
    public int SortOrder { get; set; } = 0;

    /// <summary>
    /// Description of the data type
    /// </summary>
    [StringLength(1000)]
    public string? Description { get; set; }
}
