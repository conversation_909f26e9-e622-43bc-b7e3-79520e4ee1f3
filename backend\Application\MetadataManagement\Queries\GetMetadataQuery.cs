using Application.MetadataManagement.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.MetadataManagement.Queries;

/// <summary>
/// Get Metadata query
/// </summary>
public class GetMetadataQuery : IRequest<PaginatedResult<MetadataDto>>
{
    /// <summary>
    /// Page number
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Search term
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Data type ID filter
    /// </summary>
    public Guid? DataTypeId { get; set; }

    /// <summary>
    /// Is visible filter
    /// </summary>
    public bool? IsVisible { get; set; }

    /// <summary>
    /// Order by
    /// </summary>
    public string? OrderBy { get; set; }
}
