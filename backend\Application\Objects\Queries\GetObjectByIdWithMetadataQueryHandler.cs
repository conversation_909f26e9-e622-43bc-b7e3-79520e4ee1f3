using Application.Objects.DTOs;
using Application.Objects.Specifications;
using Application.ObjectMetadataManagement.DTOs;
using Abstraction.Database.Repositories;
using MediatR;
using Shared.Common.Response;
using ObjectEntity = Domain.Entities.Object;

namespace Application.Objects.Queries;

/// <summary>
/// Get Object by ID with metadata query handler
/// </summary>
public class GetObjectByIdWithMetadataQueryHandler : IRequestHandler<GetObjectByIdWithMetadataQuery, Result<ObjectWithMetadataDto>>
{
    private readonly IReadRepository<ObjectEntity> _objectRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetObjectByIdWithMetadataQueryHandler(IReadRepository<ObjectEntity> objectRepository)
    {
        _objectRepository = objectRepository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<ObjectWithMetadataDto>> Handle(GetObjectByIdWithMetadataQuery request, CancellationToken cancellationToken)
    {
        // Get object using specification (tenant isolation handled by Finbuckle.MultiTenant)
        var objectSpec = new ObjectByIdWithMetadataSpec(request.Id);
        var obj = await _objectRepository.GetBySpecAsync(objectSpec, cancellationToken);

        if (obj == null)
        {
            return Result<ObjectWithMetadataDto>.Failure("Object not found.");
        }

        var objectDto = new ObjectWithMetadataDto
        {
            Id = obj.Id,
            FeatureId = obj.FeatureId,
            FeatureName = obj.Feature?.Name,
            ParentObjectId = obj.ParentObjectId,
            ParentObjectName = obj.ParentObject?.Name,
            Name = obj.Name,
            Description = obj.Description,
            IsActive = obj.IsActive,
            ChildObjectsCount = obj.ChildObjects?.Count(co => co.IsActive && !co.IsDeleted) ?? 0,
            Metadata = obj.ObjectMetadata?
                .Where(om => om.IsActive && !om.IsDeleted)
                .OrderBy(om => om.Metadata.FieldOrder ?? int.MaxValue)
                .ThenBy(om => om.Metadata.MetadataKey)
                .Select(om => new ObjectMetadataDto
                {
                    Id = om.Id,
                    ObjectId = om.ObjectId,
                    ObjectName = obj.Name,
                    MetadataId = om.MetadataId,
                    MetadataKey = om.Metadata.MetadataKey,
                    MetadataDisplayLabel = om.Metadata.DisplayLabel,
                    IsUnique = om.IsUnique,
                    IsActive = om.IsActive,
                    ValuesCount = 0, // TODO: Calculate actual values count if needed
                    ShouldVisibleInList = om.ShouldVisibleInList,
                    ShouldVisibleInEdit = om.ShouldVisibleInEdit,
                    ShouldVisibleInCreate = om.ShouldVisibleInCreate,
                    ShouldVisibleInView = om.ShouldVisibleInView,
                    IsCalculate = om.IsCalculate,
                    DataTypeId = om.Metadata.DataTypeId,
                    DataTypeName = om.Metadata.DataType?.Name,
                    CreatedAt = om.CreatedAt,
                    CreatedBy = om.CreatedBy ?? Guid.Empty,
                    ModifiedAt = om.ModifiedAt,
                    ModifiedBy = om.ModifiedBy
                }).ToList() ?? new List<ObjectMetadataDto>(),
            
        };

        return Result<ObjectWithMetadataDto>.Success(objectDto);
    }
}
