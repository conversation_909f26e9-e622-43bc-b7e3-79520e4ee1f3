using Application.ObjectMetadataManagement.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.ObjectMetadataManagement.Queries;

/// <summary>
/// Get ObjectMetadata by ID query
/// </summary>
public class GetObjectMetadataByIdQuery : IRequest<Result<ObjectMetadataDto>>
{
    /// <summary>
    /// ObjectMetadata ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public GetObjectMetadataByIdQuery(Guid id)
    {
        Id = id;
    }
}
