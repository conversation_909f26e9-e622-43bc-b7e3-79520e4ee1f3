using Abstraction.Common;
using Application.Common.Commands;
using Infrastructure.OpenApi;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Web.Host.Controllers;

/// <summary>
/// Controller for dynamic database operations
/// </summary>
[Route("api/[controller]")]
public class DynamicOperationsController : BaseApiController
{
    private readonly IMediator _mediator;
    private readonly ILogger<DynamicOperationsController> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public DynamicOperationsController(IMediator mediator, ILogger<DynamicOperationsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Validate tenant header
    /// </summary>
    private IActionResult ValidateTenantHeader()
    {
        if (!Request.Headers.ContainsKey("tenant"))
        {
            return BadRequest("Tenant header is required");
        }
        return null!;
    }

    /// <summary>
    /// Execute single dynamic operation
    /// </summary>
    /// <param name="request">Dynamic operation request</param>
    /// <returns>Operation response</returns>
    [HttpPost("execute")]
    [TenantIdHeader]
    public async Task<IActionResult> ExecuteDynamicOperation([FromBody] DynamicOperationRequest request)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            var command = new DynamicOperationCommand
            {
                EntityType = request.EntityType,
                OperationType = request.OperationType,
                PropertyValues = request.PropertyValues,
                KeyProperties = request.KeyProperties
            };

            var result = await _mediator.Send(command);
            
            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing dynamic operation for entity type {EntityType}", request.EntityType);
            return StatusCode(500, "An error occurred while executing the dynamic operation");
        }
    }

    /// <summary>
    /// Execute bulk dynamic operation
    /// </summary>
    /// <param name="request">Bulk dynamic operation request</param>
    /// <returns>Operation response</returns>
    [HttpPost("bulk-execute")]
    [TenantIdHeader]
    public async Task<IActionResult> ExecuteBulkDynamicOperation([FromBody] BulkDynamicOperationRequest request)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            var command = new BulkDynamicOperationCommand
            {
                EntityType = request.EntityType,
                OperationType = request.OperationType,
                PropertyValuesList = request.PropertyValuesList,
                KeyProperties = request.KeyProperties,
                BatchSize = request.BatchSize
            };

            var result = await _mediator.Send(command);
            
            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing bulk dynamic operation for entity type {EntityType}", request.EntityType);
            return StatusCode(500, "An error occurred while executing the bulk dynamic operation");
        }
    }

    /// <summary>
    /// Execute dynamic product operation with related entities
    /// </summary>
    /// <param name="request">Dynamic product operation request</param>
    /// <returns>Operation response</returns>
    [HttpPost("products")]
    [TenantIdHeader]
    public async Task<IActionResult> ExecuteDynamicProductOperation([FromBody] DynamicProductOperationRequest request)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            var command = new DynamicProductOperationCommand
            {
                OperationType = request.OperationType,
                ProductData = request.ProductData,
                IncludeFeatures = request.IncludeFeatures,
                IncludeObjects = request.IncludeObjects,
                IncludeMetadata = request.IncludeMetadata
            };

            var result = await _mediator.Send(command);
            
            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing dynamic product operation");
            return StatusCode(500, "An error occurred while executing the dynamic product operation");
        }
    }

    /// <summary>
    /// Execute dynamic feature operation with related entities
    /// </summary>
    /// <param name="request">Dynamic feature operation request</param>
    /// <returns>Operation response</returns>
    [HttpPost("features")]
    [TenantIdHeader]
    public async Task<IActionResult> ExecuteDynamicFeatureOperation([FromBody] DynamicFeatureOperationRequest request)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            var command = new DynamicFeatureOperationCommand
            {
                OperationType = request.OperationType,
                FeatureData = request.FeatureData,
                IncludeObjects = request.IncludeObjects,
                IncludeMetadata = request.IncludeMetadata
            };

            var result = await _mediator.Send(command);
            
            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing dynamic feature operation");
            return StatusCode(500, "An error occurred while executing the dynamic feature operation");
        }
    }

    /// <summary>
    /// Execute dynamic object operation with related entities
    /// </summary>
    /// <param name="request">Dynamic object operation request</param>
    /// <returns>Operation response</returns>
    [HttpPost("objects")]
    [TenantIdHeader]
    public async Task<IActionResult> ExecuteDynamicObjectOperation([FromBody] DynamicObjectOperationRequest request)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            var command = new DynamicObjectOperationCommand
            {
                OperationType = request.OperationType,
                ObjectData = request.ObjectData,
                IncludeMetadata = request.IncludeMetadata,
                IncludeValues = request.IncludeValues
            };

            var result = await _mediator.Send(command);
            
            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing dynamic object operation");
            return StatusCode(500, "An error occurred while executing the dynamic object operation");
        }
    }

    /// <summary>
    /// Execute dynamic value operation (ProductValue, FeatureValue, ObjectValue, etc.)
    /// </summary>
    /// <param name="request">Dynamic value operation request</param>
    /// <returns>Operation response</returns>
    [HttpPost("values")]
    [TenantIdHeader]
    public async Task<IActionResult> ExecuteDynamicValueOperation([FromBody] DynamicValueOperationRequest request)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            var command = new DynamicValueOperationCommand
            {
                ValueEntityType = request.ValueEntityType,
                OperationType = request.OperationType,
                ValueData = request.ValueData,
                RefId = request.RefId,
                ValidateMetadata = request.ValidateMetadata
            };

            var result = await _mediator.Send(command);
            
            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing dynamic value operation for {ValueEntityType}", request.ValueEntityType);
            return StatusCode(500, "An error occurred while executing the dynamic value operation");
        }
    }

    /// <summary>
    /// Create or update object with metadata and values
    /// </summary>
    /// <param name="request">Object creation request</param>
    /// <returns>Operation response</returns>
    [HttpPost("objects/create-with-metadata")]
    [TenantIdHeader]
    public async Task<IActionResult> CreateObjectWithMetadata([FromBody] CreateObjectWithMetadataRequest request)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            var command = new CreateObjectWithMetadataCommand
            {
                ProductId = request.ProductId,
                FeatureId = request.FeatureId,
                ObjectName = request.ObjectName,
                Metadata = request.Metadata,
                Values = request.Values
            };

            var result = await _mediator.Send(command);

            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating object with metadata");
            return StatusCode(500, "An error occurred while creating object with metadata");
        }
    }

    /// <summary>
    /// Get supported entity types for dynamic operations
    /// </summary>
    /// <returns>List of supported entity types</returns>
    [HttpGet("supported-entities")]
    public IActionResult GetSupportedEntityTypes()
    {
        var supportedEntities = new
        {
            CoreEntities = new[] { "Product", "Feature", "Object", "Subscription" },
            ValueEntities = new[] { "ProductValue", "FeatureValue", "ObjectValue", "SubscriptionValue", "UserValue", "TenantInfoValue" },
            MetadataEntities = new[] { "ProductMetadata", "FeatureMetadata", "ObjectMetadata", "SubscriptionMetadata", "UserMetadata" },
            SupportedOperations = Enum.GetNames<DynamicOperationType>()
        };

        return Ok(supportedEntities);
    }
}

/// <summary>
/// Request DTOs for API endpoints
/// </summary>
public class DynamicOperationRequest : DynamicOperationDto
{
    public Dictionary<string, object?> PropertyValues { get; set; } = new();
    public List<string> KeyProperties { get; set; } = new();
}

public class BulkDynamicOperationRequest : DynamicOperationDto
{
    public List<Dictionary<string, object?>> PropertyValuesList { get; set; } = new();
    public List<string> KeyProperties { get; set; } = new();
    public int BatchSize { get; set; } = 1000;
}

public class DynamicProductOperationRequest
{
    public DynamicOperationType OperationType { get; set; }
    public List<Dictionary<string, object?>> ProductData { get; set; } = new();
    public bool IncludeFeatures { get; set; } = false;
    public bool IncludeObjects { get; set; } = false;
    public bool IncludeMetadata { get; set; } = false;
}

public class DynamicFeatureOperationRequest
{
    public DynamicOperationType OperationType { get; set; }
    public List<Dictionary<string, object?>> FeatureData { get; set; } = new();
    public bool IncludeObjects { get; set; } = false;
    public bool IncludeMetadata { get; set; } = false;
}

public class DynamicObjectOperationRequest
{
    public DynamicOperationType OperationType { get; set; }
    public List<Dictionary<string, object?>> ObjectData { get; set; } = new();
    public bool IncludeMetadata { get; set; } = false;
    public bool IncludeValues { get; set; } = false;
}

public class DynamicValueOperationRequest
{
    public string ValueEntityType { get; set; } = string.Empty;
    public DynamicOperationType OperationType { get; set; }
    public List<Dictionary<string, object?>> ValueData { get; set; } = new();
    public Guid? RefId { get; set; }
    public bool ValidateMetadata { get; set; } = true;
}

public class CreateObjectWithMetadataRequest
{
    /// <summary>
    /// Product ID that the object belongs to
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Feature ID that the object belongs to
    /// </summary>
    public Guid FeatureId { get; set; }

    /// <summary>
    /// Object name - will be checked for existence
    /// </summary>
    public string ObjectName { get; set; } = string.Empty;

    /// <summary>
    /// Metadata definitions for the object
    /// Each dictionary should contain metadata properties like MetadataKey, DataTypeId, etc.
    /// </summary>
    public List<Dictionary<string, object?>> Metadata { get; set; } = new();

    /// <summary>
    /// Values to be stored in ObjectValues table
    /// Each dictionary should contain MetadataKey and Value pairs
    /// </summary>
    public List<Dictionary<string, object?>> Values { get; set; } = new();
}
