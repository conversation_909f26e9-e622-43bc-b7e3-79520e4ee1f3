using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for ProductMetadata entity
/// </summary>
public class ProductMetadataConfig : IEntityTypeConfiguration<ProductMetadata>
{
    public void Configure(EntityTypeBuilder<ProductMetadata> builder)
    {
        builder.ToTable("ProductMetadata", "Genp");

        // Properties
        builder.Property(e => e.ProductId)
            .IsRequired();

        builder.Property(e => e.MetadataId)
            .IsRequired();

        builder.Property(e => e.IsUnique)
            .HasDefaultValue(false);

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        // Indexes
        builder.HasIndex(e => e.ProductId)
            .HasDatabaseName("IX_ProductMetadata_ProductId");

        builder.HasIndex(e => e.MetadataId)
            .HasDatabaseName("IX_ProductMetadata_MetadataId");

        builder.HasIndex(e => e.IsUnique)
            .HasDatabaseName("IX_ProductMetadata_IsUnique");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_ProductMetadata_IsActive")
            .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

        // Unique constraint
        builder.HasIndex(e => new { e.ProductId, e.MetadataId })
            .IsUnique()
            .HasDatabaseName("IX_ProductMetadata_ProductId_MetadataId");

        // Multi-tenancy
        builder.IsMultiTenant();

        // Relationships
        builder.HasOne(e => e.Product)
            .WithMany(e => e.ProductMetadata)
            .HasForeignKey(e => e.ProductId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.Metadata)
            .WithMany(e => e.ProductMetadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.ProductValues)
            .WithOne(e => e.ProductMetadata)
            .HasForeignKey(e => e.ProductMetadataId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
