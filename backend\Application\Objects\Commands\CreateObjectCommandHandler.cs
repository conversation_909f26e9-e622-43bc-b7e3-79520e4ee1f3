using Application.Objects.DTOs;
using Application.Objects.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.Objects.Commands;

/// <summary>
/// Create Object command handler
/// </summary>
public class CreateObjectCommandHandler : IRequestHandler<CreateObjectCommand, Result<ObjectDto>>
{
    private readonly IRepository<Domain.Entities.Object> _repository;
    private readonly IRepository<Feature> _featureRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateObjectCommandHandler(
        IRepository<Domain.Entities.Object> repository,
        IRepository<Feature> featureRepository)
    {
        _repository = repository;
        _featureRepository = featureRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<ObjectDto>> Handle(CreateObjectCommand request, CancellationToken cancellationToken)
    {
        // Validate feature exists
        var feature = await _featureRepository.GetByIdAsync(request.FeatureId, cancellationToken);
        if (feature == null)
        {
            return Result<ObjectDto>.Failure($"Feature with ID '{request.FeatureId}' not found.");
        }

        // Validate parent object exists if specified
        if (request.ParentObjectId.HasValue)
        {
            var parentObject = await _repository.GetByIdAsync(request.ParentObjectId.Value, cancellationToken);
            if (parentObject == null)
            {
                return Result<ObjectDto>.Failure($"Parent object with ID '{request.ParentObjectId}' not found.");
            }

            // Ensure parent object belongs to the same feature
            if (parentObject.FeatureId != request.FeatureId)
            {
                return Result<ObjectDto>.Failure("Parent object must belong to the same feature.");
            }
        }

        // Check if Object with same name already exists in the feature
        var existingObject = await _repository.GetBySpecAsync(
            new ObjectByFeatureAndNameSpec(request.FeatureId, request.Name), cancellationToken);
        if (existingObject != null)
        {
            return Result<ObjectDto>.Failure($"Object with name '{request.Name}' already exists in this feature.");
        }

        // Create new Object
        var obj = new Domain.Entities.Object
        {
            FeatureId = request.FeatureId,
            ParentObjectId = request.ParentObjectId,
            Name = request.Name,
            Description = request.Description,
            IsActive = request.IsActive
        };

        var createdObject = await _repository.AddAsync(obj, cancellationToken);

        var dto = new ObjectDto
        {
            Id = createdObject.Id,
            FeatureId = createdObject.FeatureId,
            FeatureName = feature.Name,
            ParentObjectId = createdObject.ParentObjectId,
            Name = createdObject.Name,
            Description = createdObject.Description,
            IsActive = createdObject.IsActive,
            ChildObjectsCount = 0,
            MetadataCount = 0,
            CreatedAt = createdObject.CreatedAt,
            CreatedBy = createdObject.CreatedBy ?? Guid.Empty,
            ModifiedAt = createdObject.ModifiedAt,
            ModifiedBy = createdObject.ModifiedBy
        };

        return Result<ObjectDto>.Success(dto);
    }
}
