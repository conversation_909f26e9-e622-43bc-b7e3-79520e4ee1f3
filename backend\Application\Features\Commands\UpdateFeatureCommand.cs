using Application.Features.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Features.Commands;

/// <summary>
/// Update feature command
/// </summary>
public class UpdateFeatureCommand : IRequest<Result<FeatureDto>>
{
    /// <summary>
    /// Feature ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Feature name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Feature description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Whether this is a default feature
    /// </summary>
    public bool IsDefault { get; set; }

    /// <summary>
    /// Whether the feature is active
    /// </summary>
    public bool IsActive { get; set; }
}
