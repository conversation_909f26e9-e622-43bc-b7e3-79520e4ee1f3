using Application.Features.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.Features.Commands;

/// <summary>
/// Delete feature command handler
/// </summary>
public class DeleteFeatureCommandHandler : IRequestHandler<DeleteFeatureCommand, Result<bool>>
{
    private readonly IRepository<Feature> _featureRepository;
    private readonly IReadRepository<Domain.Entities.Object> _objectRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteFeatureCommandHandler(
        IRepository<Feature> featureRepository,
        IReadRepository<Domain.Entities.Object> objectRepository)
    {
        _featureRepository = featureRepository;
        _objectRepository = objectRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<bool>> Handle(DeleteFeatureCommand request, CancellationToken cancellationToken)
    {
        // Get feature using specification (tenant isolation handled by Finbuckle.MultiTenant)
        var featureSpec = new FeatureByIdSpec(request.Id);
        var feature = await _featureRepository.GetBySpecAsync(featureSpec, cancellationToken);

        if (feature == null)
        {
            return Result<bool>.Failure("Feature not found.");
        }

        // Check if feature has objects using specification
        var objectSpec = new ObjectByFeatureIdSpec(request.Id);
        var hasObjects = await _objectRepository.AnyAsync(objectSpec, cancellationToken);

        if (hasObjects)
        {
            return Result<bool>.Failure("Cannot delete feature that has objects. Delete objects first.");
        }

        await _featureRepository.DeleteAsync(feature, cancellationToken);

        return Result<bool>.Success(true);
    }
}
