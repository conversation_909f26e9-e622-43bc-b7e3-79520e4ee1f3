using Application.Objects.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Objects.Commands;

/// <summary>
/// Update Object command
/// </summary>
public class UpdateObjectCommand : IRequest<Result<ObjectDto>>
{
    /// <summary>
    /// Object ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Feature ID
    /// </summary>
    public Guid FeatureId { get; set; }

    /// <summary>
    /// Object name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Object description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Whether the object is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
