using Application.ObjectValues.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.ObjectValues.Queries.GetObjectValue;

/// <summary>
/// Get ObjectValue by ID with full metadata information
/// </summary>
public class GetObjectValueByIdQuery : IRequest<Result<ObjectValueResponseDto>>
{
    /// <summary>
    /// ObjectValue ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Include child values in hierarchical structure
    /// </summary>
    public bool IncludeChildValues { get; set; } = true;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetObjectValueByIdQuery(Guid id)
    {
        Id = id;
    }
}
