namespace Application.ProductValues.DTOs;

/// <summary>
/// ProductValue DTO
/// </summary>
public class ProductValueDto
{
    /// <summary>
    /// ProductValue ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Product metadata ID
    /// </summary>
    public Guid ProductMetadataId { get; set; }

    /// <summary>
    /// Product name
    /// </summary>
    public string? ProductName { get; set; }

    /// <summary>
    /// Metadata key
    /// </summary>
    public string? MetadataKey { get; set; }

    /// <summary>
    /// Reference ID for grouping related values
    /// </summary>
    public Guid? RefId { get; set; }

    /// <summary>
    /// Parent product value ID for hierarchical structure
    /// </summary>
    public Guid? ParentProductValueId { get; set; }

    /// <summary>
    /// Parent product value
    /// </summary>
    public string? ParentProductValue { get; set; }

    /// <summary>
    /// The actual value as text
    /// </summary>
    public string? Value { get; set; }

    /// <summary>
    /// Number of child values
    /// </summary>
    public int ChildValuesCount { get; set; }

    /// <summary>
    /// Created at
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by
    /// </summary>
    public Guid CreatedBy { get; set; }

    /// <summary>
    /// Modified at
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Modified by
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}
