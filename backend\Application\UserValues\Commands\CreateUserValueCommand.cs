using Application.UserValues.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.UserValues.Commands;

/// <summary>
/// Create UserValue command
/// </summary>
public class CreateUserValueCommand : IRequest<Result<UserValueDto>>
{
    /// <summary>
    /// User metadata ID
    /// </summary>
    public Guid UserMetadataId { get; set; }

    /// <summary>
    /// Reference ID for grouping related values
    /// </summary>
    public Guid? RefId { get; set; }

    /// <summary>
    /// Parent user value ID for hierarchical structure
    /// </summary>
    public Guid? ParentUserValueId { get; set; }

    /// <summary>
    /// The actual value as text
    /// </summary>
    public string? Value { get; set; }
}
