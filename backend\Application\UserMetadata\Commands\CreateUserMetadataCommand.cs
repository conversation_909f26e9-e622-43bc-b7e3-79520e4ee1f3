using Application.UserMetadata.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.UserMetadata.Commands;

/// <summary>
/// Create UserMetadata command
/// </summary>
public class CreateUserMetadataCommand : IRequest<Result<UserMetadataDto>>
{
    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Metadata ID
    /// </summary>
    public Guid MetadataId { get; set; }

    /// <summary>
    /// Whether this metadata is unique for the user
    /// </summary>
    public bool IsUnique { get; set; } = false;

    /// <summary>
    /// Whether this metadata link is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
