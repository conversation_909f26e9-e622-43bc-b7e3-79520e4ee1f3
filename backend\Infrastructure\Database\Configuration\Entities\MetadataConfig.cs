using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for Metadata entity
/// </summary>
public class MetadataConfig : IEntityTypeConfiguration<Metadata>
{
    public void Configure(EntityTypeBuilder<Metadata> builder)
    {
        builder.ToTable("Metadata", "Genp");

        // Properties
        builder.Property(e => e.MetadataKey)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.DataTypeId)
            .IsRequired();

        builder.Property(e => e.CustomValidationPattern)
            .HasMaxLength(500);

        builder.Property(e => e.CustomPlaceholder)
            .HasMaxLength(255);

        builder.Property(e => e.CustomOptions)
            .HasColumnType("TEXT");

        builder.Property(e => e.CustomAllowedFileTypes)
            .HasMaxLength(255);

        builder.Property(e => e.CustomErrorMessage)
            .HasMaxLength(255);

        builder.Property(e => e.DisplayLabel)
            .HasMaxLength(255);

        builder.Property(e => e.HelpText)
            .HasColumnType("TEXT");

        builder.Property(e => e.IsVisible)
            .HasDefaultValue(true);

        builder.Property(e => e.IsReadonly)
            .HasDefaultValue(false);

        // Indexes
        builder.HasIndex(e => e.MetadataKey)
            .IsUnique()
            .HasDatabaseName("IX_Metadata_MetadataKey");

        builder.HasIndex(e => e.DataTypeId)
            .HasDatabaseName("IX_Metadata_DataTypeId");

        builder.HasIndex(e => e.IsVisible)
            .HasDatabaseName("IX_Metadata_IsVisible")
            .HasFilter("\"IsVisible\" = true AND \"IsDeleted\" = false");

        // Multi-tenancy
        builder.IsMultiTenant();

        // Relationships
        builder.HasOne(e => e.DataType)
            .WithMany(e => e.Metadata)
            .HasForeignKey(e => e.DataTypeId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(e => e.TenantInfoMetadata)
            .WithOne(e => e.Metadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.ProductMetadata)
            .WithOne(e => e.Metadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.FeatureMetadata)
            .WithOne(e => e.Metadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.RoleMetadata)
            .WithOne(e => e.Metadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.UserMetadata)
            .WithOne(e => e.Metadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.ObjectMetadata)
            .WithOne(e => e.Metadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.SubscriptionMetadata)
            .WithOne(e => e.Metadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
