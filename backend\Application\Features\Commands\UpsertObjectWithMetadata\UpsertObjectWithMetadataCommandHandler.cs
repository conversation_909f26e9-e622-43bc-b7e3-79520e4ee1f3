using Abstraction.Common;
using Application.Features.DTOs;
using Application.Features.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;
using System.Diagnostics;

namespace Application.Features.Commands.UpsertObjectWithMetadata;

/// <summary>
/// Handler for upserting objects with metadata and values
/// </summary>
public class UpsertObjectWithMetadataCommandHandler : IRequestHandler<UpsertObjectWithMetadataCommand, Result<UpsertObjectWithMetadataResponseDto>>
{
    private readonly IObjectUpsertService _objectUpsertService;
    private readonly ICurrentUser _currentUser;
    private readonly ILogger<UpsertObjectWithMetadataCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpsertObjectWithMetadataCommandHandler(
        IObjectUpsertService objectUpsertService,
        ICurrentUser currentUser,
        ILogger<UpsertObjectWithMetadataCommandHandler> logger)
    {
        _objectUpsertService = objectUpsertService;
        _currentUser = currentUser;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<UpsertObjectWithMetadataResponseDto>> Handle(
        UpsertObjectWithMetadataCommand request,
        CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Starting upsert of object with metadata: {ObjectName}, ObjectId: {ObjectId}", 
                request.Object.Name, request.Object.Id);

            // Get tenant and user context
            var tenantId = _currentUser.GetTenant();
            if (string.IsNullOrEmpty(tenantId))
            {
                return Result<UpsertObjectWithMetadataResponseDto>.Failure("Tenant context is required");
            }

            var userId = Guid.Empty.ToString();
            // Validate input
            var validationResult = ValidateRequest(request.Object);
            if (!validationResult.Succeeded)
            {
                return Result<UpsertObjectWithMetadataResponseDto>.Failure(validationResult.Message ?? "Invalid request");
            }

            // Perform upsert operation
            var result = await _objectUpsertService.UpsertObjectWithMetadataAsync(
                request.Object,
                tenantId,
                userId,
                cancellationToken);

            if (!result.Succeeded)
            {
                _logger.LogError("Failed to upsert object with metadata: {Error}", result.Message);
                return Result<UpsertObjectWithMetadataResponseDto>.Failure(result.Message ?? "Failed to upsert object");
            }

            stopwatch.Stop();
            result.Data!.ProcessingSummary.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;

            _logger.LogInformation("Successfully upserted object with metadata: {ObjectName}, ObjectId: {ObjectId} in {ElapsedMs}ms",
                request.Object.Name, result.Data.Object.Id, stopwatch.ElapsedMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error upserting object with metadata: {ObjectName}", request.Object.Name);
            return Result<UpsertObjectWithMetadataResponseDto>.Failure("An error occurred while upserting the object with metadata");
        }
    }

    /// <summary>
    /// Validate the request
    /// </summary>
    private static Result<bool> ValidateRequest(ObjectUpsertDto objectData)
    {
        if (string.IsNullOrWhiteSpace(objectData.Name))
        {
            return Result<bool>.Failure("Object name is required");
        }

        // For new objects, FeatureId is required
        if (!objectData.Id.HasValue && !objectData.FeatureId.HasValue)
        {
            return Result<bool>.Failure("FeatureId is required when creating a new object");
        }

        // Validate that we have either metadata or values to process
        if ((!objectData.MetaJson.HasValue || objectData.MetaJson.Value.ValueKind == System.Text.Json.JsonValueKind.Null) 
            && !objectData.MetaValues.Any())
        {
            return Result<bool>.Failure("Either MetaJson or MetaValues must be provided");
        }

        return Result<bool>.Success(true);
    }
}
