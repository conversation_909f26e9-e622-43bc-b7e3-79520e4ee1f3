[2025-06-05 19:10:48.900 +05:30 INF] Executed DbCommand (60ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-05 19:10:49.294 +05:30 INF] Executed DbCommand (45ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-05 19:10:49.403 +05:30 INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-05 19:10:49.858 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-05 19:10:50.289 +05:30 INF] Executed DbCommand (51ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-05 19:10:50.584 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-05 19:10:50.856 +05:30 INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-05 19:10:51.035 +05:30 INF] Connection to black's Database Succeeded.
[2025-06-05 19:10:51.412 +05:30 INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-05 19:10:51.605 +05:30 INF] Connection to lrbnewqa's Database Succeeded.
[2025-06-05 19:10:51.931 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-05 19:10:52.130 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-05 19:10:52.190 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger - null null
[2025-06-05 19:10:52.358 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-05 19:10:52.359 +05:30 INF] Hosting environment: dev
[2025-06-05 19:10:52.359 +05:30 INF] Content root path: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host
[2025-06-05 19:10:53.181 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:10:53.195 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-05 19:10:53.198 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-05 19:10:53.199 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:10:53.200 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:10:53.201 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-05 19:10:53.201 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-05 19:10:53.229 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/index.html - null null
[2025-06-05 19:10:53.257 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger - 301 null null 1075.1894ms
[2025-06-05 19:10:53.258 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:10:53.259 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-05 19:10:53.259 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-05 19:10:53.264 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:10:53.265 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:10:53.269 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-05 19:10:53.269 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-05 19:10:53.351 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/index.html - 200 null text/html;charset=utf-8 122.3353ms
[2025-06-05 19:10:53.364 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/index.css - null null
[2025-06-05 19:10:53.365 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/swagger-ui.css - null null
[2025-06-05 19:10:53.371 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/swagger-ui-bundle.js - null null
[2025-06-05 19:10:53.377 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:10:53.377 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:10:53.377 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:10:53.377 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-05 19:10:53.377 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-05 19:10:53.377 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-05 19:10:53.377 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-05 19:10:53.377 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-05 19:10:53.377 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-05 19:10:53.377 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:10:53.377 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:10:53.377 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:10:53.377 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:10:53.377 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:10:53.377 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:10:53.377 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-05 19:10:53.377 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-05 19:10:53.377 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-05 19:10:53.377 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-05 19:10:53.377 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-05 19:10:53.377 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-05 19:10:53.401 +05:30 INF] Sending file. Request path: '/index.css'. Physical path: 'N/A'
[2025-06-05 19:10:53.401 +05:30 INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
[2025-06-05 19:10:53.402 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/index.css - 200 202 text/css 37.91ms
[2025-06-05 19:10:53.405 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/swagger-ui.css - 200 154949 text/css 40.0935ms
[2025-06-05 19:10:53.424 +05:30 INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
[2025-06-05 19:10:53.442 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/index.js - null null
[2025-06-05 19:10:53.442 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/swagger-ui-standalone-preset.js - null null
[2025-06-05 19:10:53.443 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:10:53.443 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:10:53.443 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-05 19:10:53.443 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-05 19:10:53.444 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-05 19:10:53.444 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-05 19:10:53.444 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:10:53.444 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:10:53.444 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:10:53.444 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:10:53.444 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-05 19:10:53.444 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-05 19:10:53.444 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-05 19:10:53.444 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-05 19:10:53.447 +05:30 INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
[2025-06-05 19:10:53.447 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/index.js - 200 null application/javascript;charset=utf-8 4.4317ms
[2025-06-05 19:10:53.455 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/swagger-ui-bundle.js - 200 1466908 text/javascript 84.3312ms
[2025-06-05 19:10:53.461 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-05 19:10:53.461 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/swagger-ui-standalone-preset.js - 200 229223 text/javascript 18.4235ms
[2025-06-05 19:10:53.466 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - 200 16501 application/javascript; charset=utf-8 3.6185ms
[2025-06-05 19:10:53.470 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_vs/browserLink - null null
[2025-06-05 19:10:53.533 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_vs/browserLink - 200 null text/javascript; charset=UTF-8 62.121ms
[2025-06-05 19:10:54.005 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - null null
[2025-06-05 19:10:54.006 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:10:54.006 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-05 19:10:54.006 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-05 19:10:54.006 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:10:54.006 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:10:54.006 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-05 19:10:54.006 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-05 19:10:54.093 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 88.7639ms
[2025-06-05 19:14:39.671 +05:30 INF] Executed DbCommand (66ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-05 19:14:40.084 +05:30 INF] Executed DbCommand (41ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-05 19:14:40.177 +05:30 INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-05 19:14:41.246 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-05 19:14:41.714 +05:30 INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-05 19:14:42.093 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-05 19:14:42.446 +05:30 INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-05 19:14:42.698 +05:30 INF] Connection to black's Database Succeeded.
[2025-06-05 19:14:43.095 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-05 19:14:43.323 +05:30 INF] Connection to lrbnewqa's Database Succeeded.
[2025-06-05 19:14:44.046 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-05 19:14:44.248 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-05 19:14:44.371 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-05 19:14:44.371 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_vs/browserLink - null null
[2025-06-05 19:14:44.483 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - 200 16501 application/javascript; charset=utf-8 135.4876ms
[2025-06-05 19:14:44.523 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_vs/browserLink - 200 null text/javascript; charset=UTF-8 178.3925ms
[2025-06-05 19:14:44.666 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-05 19:14:44.667 +05:30 INF] Hosting environment: dev
[2025-06-05 19:14:44.668 +05:30 INF] Content root path: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host
[2025-06-05 19:14:44.857 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - null null
[2025-06-05 19:14:45.288 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:14:45.292 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-05 19:14:45.299 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-05 19:14:45.300 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:14:45.302 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:14:45.304 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-05 19:14:45.305 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-05 19:14:45.756 +05:30 ERR] Connection ID "18230571309580943366", Request ID "40000007-0004-fd00-b63f-84710c7967bb": An unhandled exception was thrown by the application.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - Web.Host.Controllers.SubscriptionsController.GetSubscriptionsAsync (Web.Host). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate schema for type - Shared.Common.Response.PaginatedResult`1[Application.Subscriptions.DTOs.SubscriptionDto]. See inner exception
 ---> System.InvalidOperationException: Can't use schemaId "$SubscriptionDto" for type "$Application.Subscriptions.DTOs.SubscriptionDto". The same schemaId is already used for type "$Application.Products.DTOs.SubscriptionDto"
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaRepository.RegisterType(Type type, String schemaId)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateArraySchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__1()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForMember(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, DataProperty dataProperty)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.CreateObjectSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.<>c__DisplayClass12_0.<GenerateConcreteSchema>b__3()
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateReferencedSchema(DataContract dataContract, SchemaRepository schemaRepository, Func`1 definitionFactory)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateConcreteSchema(DataContract dataContract, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchemaForType(Type modelType, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SchemaGenerator.GenerateSchema(Type modelType, SchemaRepository schemaRepository, MemberInfo memberInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateSchema(Type type, SchemaRepository schemaRepository, PropertyInfo propertyInfo, ParameterInfo parameterInfo, ApiParameterRouteInfo routeInfo)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.CreateResponseMediaType(Type modelType, SchemaRepository schemaRespository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.<>c__DisplayClass42_0.<GenerateResponse>b__2(String contentType)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector, IEqualityComparer`1 comparer)
   at System.Linq.Enumerable.ToDictionary[TSource,TKey,TElement](IEnumerable`1 source, Func`2 keySelector, Func`2 elementSelector)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponse(ApiDescription apiDescription, SchemaRepository schemaRepository, String statusCode, ApiResponseType apiResponseType)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateResponses(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Infrastructure.Common.CurrentUserMiddleware.InvokeAsync(HttpContext context, ICurrentUserInitializer currentUserInitializer) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\Common\CurrentUserMiddleware.cs:line 27
   at Infrastructure.MultiTenancy.Middleware.TenantMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\source\repos\this-applications\backend\Infrastructure\MultiTenancy\Middleware\TenantMiddleware.cs:line 155
   at Finbuckle.MultiTenant.AspNetCore.MultiTenantMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.WebTools.BrowserLink.Net.BrowserLinkMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Watch.BrowserRefresh.BrowserRefreshMiddleware.InvokeAsync(HttpContext context)
   at Microsoft.AspNetCore.Server.IIS.Core.IISHttpContextOfT`1.ProcessRequestAsync()
[2025-06-05 19:14:45.797 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - 500 null null 940.6013ms
[2025-06-05 19:19:45.188 +05:30 INF] Executed DbCommand (44ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-05 19:19:45.584 +05:30 INF] Executed DbCommand (37ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-05 19:19:45.665 +05:30 INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-05 19:19:46.145 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-05 19:19:46.604 +05:30 INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-05 19:19:46.971 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-05 19:19:47.298 +05:30 INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-05 19:19:47.499 +05:30 INF] Connection to black's Database Succeeded.
[2025-06-05 19:19:47.896 +05:30 INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-05 19:19:48.098 +05:30 INF] Connection to lrbnewqa's Database Succeeded.
[2025-06-05 19:19:48.722 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-05 19:19:49.039 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-05 19:19:49.141 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-05 19:19:49.140 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_vs/browserLink - null null
[2025-06-05 19:19:49.250 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - 200 16501 application/javascript; charset=utf-8 131.8968ms
[2025-06-05 19:19:49.273 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_vs/browserLink - 200 null text/javascript; charset=UTF-8 156.6991ms
[2025-06-05 19:19:49.375 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - null null
[2025-06-05 19:19:49.390 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-05 19:19:49.391 +05:30 INF] Hosting environment: dev
[2025-06-05 19:19:49.391 +05:30 INF] Content root path: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host
[2025-06-05 19:19:50.167 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:19:50.172 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-05 19:19:50.179 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-05 19:19:50.180 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:19:50.183 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:19:50.184 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-05 19:19:50.186 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-05 19:19:50.592 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 1216.9432ms
[2025-06-05 19:20:08.633 +05:30 INF] Request starting HTTP/2 POST https://localhost:44391/api/users/bulk-create - application/json 779
[2025-06-05 19:20:08.637 +05:30 INF] CORS policy execution successful.
[2025-06-05 19:20:08.669 +05:30 DBG] GetIdentifierAsync: Found identifier: "SADF"
[2025-06-05 19:20:08.670 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier "SADF"
[2025-06-05 19:20:08.670 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-05 19:20:08.670 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-05 19:20:08.670 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:20:08.670 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:20:08.671 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-05 19:20:08.671 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-05 19:20:08.701 +05:30 INF] Request finished HTTP/2 POST https://localhost:44391/api/users/bulk-create - 400 null application/json; charset=utf-8 67.8932ms
[2025-06-05 19:21:53.050 +05:30 INF] Request starting HTTP/1.1 POST https://localhost:44391/api/users/bulk-create - application/json 11193
[2025-06-05 19:21:53.058 +05:30 DBG] GetIdentifierAsync: Found identifier: "SADF"
[2025-06-05 19:21:53.058 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier "SADF"
[2025-06-05 19:21:53.058 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-05 19:21:53.058 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-05 19:21:53.058 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:21:53.058 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-05 19:21:53.059 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-05 19:21:53.059 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-05 19:21:53.060 +05:30 INF] Request finished HTTP/1.1 POST https://localhost:44391/api/users/bulk-create - 400 null application/json; charset=utf-8 10.3709ms
[2025-06-05 19:22:16.361 +05:30 INF] Request starting HTTP/1.1 POST https://localhost:44391/api/users/bulk-create - application/json 11193
[2025-06-05 19:22:16.362 +05:30 DBG] GetIdentifierAsync: Found identifier: "lrbnewqa"
[2025-06-05 19:22:16.363 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "lrbnewqa"
[2025-06-05 19:22:16.381 +05:30 INF] Executing endpoint 'Web.Host.Controllers.Identity.UsersController.BulkCreateUsersAsync (Web.Host)'
[2025-06-05 19:22:16.425 +05:30 INF] Route matched with {action = "BulkCreateUsers", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Shared.Common.Response.ApiResponse`1[Application.Identity.Commands.BulkCreateUsersResponse]]] BulkCreateUsersAsync(Application.Identity.Commands.BulkCreateUsersCommand) on controller Web.Host.Controllers.Identity.UsersController (Web.Host).
[2025-06-05 19:22:16.672 +05:30 INF] Executing action method Web.Host.Controllers.Identity.UsersController.BulkCreateUsersAsync (Web.Host) - Validation state: "Valid"
[2025-06-05 19:22:29.048 +05:30 INF] Executed DbCommand (34ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:22:29.184 +05:30 INF] Executed DbCommand (54ms) [Parameters=[@__normalizedUserName_0='LRBNEWQAUSER1'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:22:29.218 +05:30 INF] Executed DbCommand (30ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:22:29.250 +05:30 INF] Executed DbCommand (30ms) [Parameters=[@__normalizedUserName_0='MOUNIKAPAMPANA.LRBNEWQA'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:22:29.288 +05:30 INF] Executed DbCommand (31ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:22:29.319 +05:30 INF] Executed DbCommand (29ms) [Parameters=[@__normalizedUserName_0='INCENTIVEUSER'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:22:29.346 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:22:29.374 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedUserName_0='INCENTIVECUSTOMERADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:22:29.406 +05:30 INF] Executed DbCommand (30ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:22:29.434 +05:30 INF] Executed DbCommand (26ms) [Parameters=[@__normalizedUserName_0='INCENTIVEADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:22:29.462 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:22:29.494 +05:30 INF] Executed DbCommand (30ms) [Parameters=[@__normalizedUserName_0='DOCUMENTUSER'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:22:29.522 +05:30 INF] Executed DbCommand (28ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:22:29.554 +05:30 INF] Executed DbCommand (30ms) [Parameters=[@__normalizedUserName_0='DOCUMENTCUSTOMERADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:22:29.582 +05:30 INF] Executed DbCommand (26ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:22:29.610 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedUserName_0='DOCUMENTADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:22:29.642 +05:30 INF] Executed DbCommand (30ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:22:29.693 +05:30 INF] Executed DbCommand (48ms) [Parameters=[@__normalizedUserName_0='PERFORMANCEUSER'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:22:29.722 +05:30 INF] Executed DbCommand (28ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:22:29.750 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedUserName_0='PERFORMANCECUSTOMERADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:22:29.780 +05:30 INF] Executed DbCommand (29ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:22:29.810 +05:30 INF] Executed DbCommand (29ms) [Parameters=[@__normalizedUserName_0='PERFORMANCEADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:22:29.839 +05:30 INF] Executed DbCommand (28ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:22:29.867 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedUserName_0='INVOICEUSER'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:22:29.894 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:22:29.923 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedUserName_0='INVOICECUSTOMERADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:22:29.950 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:22:29.984 +05:30 INF] Executed DbCommand (32ms) [Parameters=[@__normalizedUserName_0='INVOICEADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:22:30.015 +05:30 INF] Executed DbCommand (29ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:22:30.069 +05:30 INF] Executed DbCommand (52ms) [Parameters=[@__normalizedUserName_0='INVENTORYUSER'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:22:30.098 +05:30 INF] Executed DbCommand (29ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:22:30.126 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedUserName_0='INVENTORYCUSTOMERADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:22:30.155 +05:30 INF] Executed DbCommand (28ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:22:30.183 +05:30 INF] Executed DbCommand (28ms) [Parameters=[@__normalizedUserName_0='INVENTORYADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:23:24.004 +05:30 INF] Executed DbCommand (30ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:23:24.728 +05:30 INF] Executed DbCommand (28ms) [Parameters=[@__normalizedUserName_0='LRBNEWQAUSER1'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:23:39.810 +05:30 INF] Executed DbCommand (28ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:23:39.838 +05:30 INF] Executed DbCommand (28ms) [Parameters=[@__normalizedUserName_0='MOUNIKAPAMPANA.LRBNEWQA'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:23:39.866 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:23:39.894 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedUserName_0='INCENTIVEUSER'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:23:39.923 +05:30 INF] Executed DbCommand (28ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:23:39.954 +05:30 INF] Executed DbCommand (30ms) [Parameters=[@__normalizedUserName_0='INCENTIVECUSTOMERADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:23:39.981 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:23:40.010 +05:30 INF] Executed DbCommand (28ms) [Parameters=[@__normalizedUserName_0='INCENTIVEADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:23:40.042 +05:30 INF] Executed DbCommand (31ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:23:40.070 +05:30 INF] Executed DbCommand (28ms) [Parameters=[@__normalizedUserName_0='DOCUMENTUSER'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:23:40.099 +05:30 INF] Executed DbCommand (28ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:23:40.126 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedUserName_0='DOCUMENTCUSTOMERADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:23:40.157 +05:30 INF] Executed DbCommand (30ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:23:40.187 +05:30 INF] Executed DbCommand (29ms) [Parameters=[@__normalizedUserName_0='DOCUMENTADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:23:40.218 +05:30 INF] Executed DbCommand (29ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:23:40.249 +05:30 INF] Executed DbCommand (29ms) [Parameters=[@__normalizedUserName_0='PERFORMANCEUSER'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:23:40.278 +05:30 INF] Executed DbCommand (28ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:23:40.305 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedUserName_0='PERFORMANCECUSTOMERADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:23:40.334 +05:30 INF] Executed DbCommand (28ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:23:40.362 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedUserName_0='PERFORMANCEADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:23:40.389 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:23:40.422 +05:30 INF] Executed DbCommand (32ms) [Parameters=[@__normalizedUserName_0='INVOICEUSER'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:23:40.450 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:23:40.484 +05:30 INF] Executed DbCommand (33ms) [Parameters=[@__normalizedUserName_0='INVOICECUSTOMERADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:23:40.514 +05:30 INF] Executed DbCommand (30ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:23:40.542 +05:30 INF] Executed DbCommand (28ms) [Parameters=[@__normalizedUserName_0='INVOICEADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:23:40.570 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:23:40.598 +05:30 INF] Executed DbCommand (28ms) [Parameters=[@__normalizedUserName_0='INVENTORYUSER'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:23:40.626 +05:30 INF] Executed DbCommand (28ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:23:40.653 +05:30 INF] Executed DbCommand (26ms) [Parameters=[@__normalizedUserName_0='INVENTORYCUSTOMERADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:23:40.682 +05:30 INF] Executed DbCommand (29ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:23:40.711 +05:30 INF] Executed DbCommand (28ms) [Parameters=[@__normalizedUserName_0='INVENTORYADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:23:50.395 +05:30 INF] Executed action method Web.Host.Controllers.Identity.UsersController.BulkCreateUsersAsync (Web.Host), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 93720.6081ms.
[2025-06-05 19:23:50.404 +05:30 INF] Executing OkObjectResult, writing value of type 'Shared.Common.Response.ApiResponse`1[[Application.Identity.Commands.BulkCreateUsersResponse, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
[2025-06-05 19:23:56.089 +05:30 INF] Executed action Web.Host.Controllers.Identity.UsersController.BulkCreateUsersAsync (Web.Host) in 99660.1229ms
[2025-06-05 19:23:56.089 +05:30 INF] Executed endpoint 'Web.Host.Controllers.Identity.UsersController.BulkCreateUsersAsync (Web.Host)'
[2025-06-05 19:23:56.093 +05:30 INF] Request finished HTTP/1.1 POST https://localhost:44391/api/users/bulk-create - 200 null application/json; charset=utf-8 99732.1897ms
[2025-06-05 19:24:01.783 +05:30 INF] Request starting HTTP/1.1 POST https://localhost:44391/api/users/bulk-create - application/json 11193
[2025-06-05 19:24:01.784 +05:30 DBG] GetIdentifierAsync: Found identifier: "lrbnewqa"
[2025-06-05 19:24:01.784 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "lrbnewqa"
[2025-06-05 19:24:01.786 +05:30 INF] Executing endpoint 'Web.Host.Controllers.Identity.UsersController.BulkCreateUsersAsync (Web.Host)'
[2025-06-05 19:24:01.787 +05:30 INF] Route matched with {action = "BulkCreateUsers", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Shared.Common.Response.ApiResponse`1[Application.Identity.Commands.BulkCreateUsersResponse]]] BulkCreateUsersAsync(Application.Identity.Commands.BulkCreateUsersCommand) on controller Web.Host.Controllers.Identity.UsersController (Web.Host).
[2025-06-05 19:24:01.808 +05:30 INF] Executing action method Web.Host.Controllers.Identity.UsersController.BulkCreateUsersAsync (Web.Host) - Validation state: "Valid"
[2025-06-05 19:24:20.509 +05:30 INF] Executed DbCommand (29ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:24:20.541 +05:30 INF] Executed DbCommand (30ms) [Parameters=[@__normalizedUserName_0='LRBNEWQAUSER1'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:24:20.572 +05:30 INF] Executed DbCommand (29ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:24:20.604 +05:30 INF] Executed DbCommand (31ms) [Parameters=[@__normalizedUserName_0='MOUNIKAPAMPANA.LRBNEWQA'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:24:20.632 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:24:20.664 +05:30 INF] Executed DbCommand (31ms) [Parameters=[@__normalizedUserName_0='INCENTIVEUSER'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:24:20.697 +05:30 INF] Executed DbCommand (32ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:24:20.728 +05:30 INF] Executed DbCommand (29ms) [Parameters=[@__normalizedUserName_0='INCENTIVECUSTOMERADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:24:20.757 +05:30 INF] Executed DbCommand (29ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:24:20.789 +05:30 INF] Executed DbCommand (31ms) [Parameters=[@__normalizedUserName_0='INCENTIVEADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:24:20.821 +05:30 INF] Executed DbCommand (31ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:24:20.852 +05:30 INF] Executed DbCommand (30ms) [Parameters=[@__normalizedUserName_0='DOCUMENTUSER'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:24:20.880 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:24:20.912 +05:30 INF] Executed DbCommand (32ms) [Parameters=[@__normalizedUserName_0='DOCUMENTCUSTOMERADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:24:20.944 +05:30 INF] Executed DbCommand (31ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:24:20.977 +05:30 INF] Executed DbCommand (32ms) [Parameters=[@__normalizedUserName_0='DOCUMENTADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:24:21.008 +05:30 INF] Executed DbCommand (30ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:24:21.036 +05:30 INF] Executed DbCommand (26ms) [Parameters=[@__normalizedUserName_0='PERFORMANCEUSER'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:24:21.067 +05:30 INF] Executed DbCommand (29ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:24:21.100 +05:30 INF] Executed DbCommand (29ms) [Parameters=[@__normalizedUserName_0='PERFORMANCECUSTOMERADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:24:21.129 +05:30 INF] Executed DbCommand (28ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:24:21.160 +05:30 INF] Executed DbCommand (29ms) [Parameters=[@__normalizedUserName_0='PERFORMANCEADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:24:21.188 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:24:21.219 +05:30 INF] Executed DbCommand (31ms) [Parameters=[@__normalizedUserName_0='INVOICEUSER'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:24:21.249 +05:30 INF] Executed DbCommand (28ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:24:21.279 +05:30 INF] Executed DbCommand (30ms) [Parameters=[@__normalizedUserName_0='INVOICECUSTOMERADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:24:21.309 +05:30 INF] Executed DbCommand (29ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:24:21.340 +05:30 INF] Executed DbCommand (30ms) [Parameters=[@__normalizedUserName_0='INVOICEADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:24:21.367 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:24:21.395 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedUserName_0='INVENTORYUSER'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:24:21.424 +05:30 INF] Executed DbCommand (28ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:24:21.454 +05:30 INF] Executed DbCommand (29ms) [Parameters=[@__normalizedUserName_0='INVENTORYCUSTOMERADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:24:21.485 +05:30 INF] Executed DbCommand (31ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:24:21.519 +05:30 INF] Executed DbCommand (33ms) [Parameters=[@__normalizedUserName_0='INVENTORYADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:24:44.146 +05:30 INF] Executed action method Web.Host.Controllers.Identity.UsersController.BulkCreateUsersAsync (Web.Host), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 42337.6085ms.
[2025-06-05 19:24:44.147 +05:30 INF] Executing OkObjectResult, writing value of type 'Shared.Common.Response.ApiResponse`1[[Application.Identity.Commands.BulkCreateUsersResponse, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
[2025-06-05 19:24:46.366 +05:30 INF] Executed action Web.Host.Controllers.Identity.UsersController.BulkCreateUsersAsync (Web.Host) in 44579.272ms
[2025-06-05 19:24:46.366 +05:30 INF] Executed endpoint 'Web.Host.Controllers.Identity.UsersController.BulkCreateUsersAsync (Web.Host)'
[2025-06-05 19:24:46.367 +05:30 INF] Request finished HTTP/1.1 POST https://localhost:44391/api/users/bulk-create - 200 null application/json; charset=utf-8 44583.8193ms
[2025-06-05 19:27:57.889 +05:30 INF] Request starting HTTP/1.1 POST https://localhost:44391/api/users/bulk-create - application/json 11209
[2025-06-05 19:27:57.890 +05:30 DBG] GetIdentifierAsync: Found identifier: "lrbnewqa"
[2025-06-05 19:27:57.890 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "lrbnewqa"
[2025-06-05 19:27:57.891 +05:30 INF] Executing endpoint 'Web.Host.Controllers.Identity.UsersController.BulkCreateUsersAsync (Web.Host)'
[2025-06-05 19:27:57.891 +05:30 INF] Route matched with {action = "BulkCreateUsers", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Shared.Common.Response.ApiResponse`1[Application.Identity.Commands.BulkCreateUsersResponse]]] BulkCreateUsersAsync(Application.Identity.Commands.BulkCreateUsersCommand) on controller Web.Host.Controllers.Identity.UsersController (Web.Host).
[2025-06-05 19:27:57.951 +05:30 INF] Executing action method Web.Host.Controllers.Identity.UsersController.BulkCreateUsersAsync (Web.Host) - Validation state: "Valid"
[2025-06-05 19:28:03.975 +05:30 INF] Executed DbCommand (26ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:28:04.003 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedUserName_0='LRBNEWQAUSER1'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:28:04.029 +05:30 INF] Executed DbCommand (25ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:28:04.059 +05:30 INF] Executed DbCommand (29ms) [Parameters=[@__normalizedUserName_0='MOUNIKAPAMPANA.LRBNEWQA'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:28:04.087 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:28:04.114 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedUserName_0='INCENTIVEUSER'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:28:04.142 +05:30 INF] Executed DbCommand (26ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:28:04.169 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedUserName_0='INCENTIVECUSTOMERADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:28:04.193 +05:30 INF] Executed DbCommand (23ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:28:04.218 +05:30 INF] Executed DbCommand (24ms) [Parameters=[@__normalizedUserName_0='INCENTIVEADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:28:04.245 +05:30 INF] Executed DbCommand (26ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:28:04.270 +05:30 INF] Executed DbCommand (24ms) [Parameters=[@__normalizedUserName_0='DOCUMENTUSER'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:28:04.294 +05:30 INF] Executed DbCommand (24ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:28:04.321 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedUserName_0='DOCUMENTCUSTOMERADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:28:04.345 +05:30 INF] Executed DbCommand (24ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:28:04.369 +05:30 INF] Executed DbCommand (23ms) [Parameters=[@__normalizedUserName_0='DOCUMENTADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:28:04.395 +05:30 INF] Executed DbCommand (25ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:28:04.421 +05:30 INF] Executed DbCommand (25ms) [Parameters=[@__normalizedUserName_0='PERFORMANCEUSER'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:28:04.449 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:28:04.475 +05:30 INF] Executed DbCommand (26ms) [Parameters=[@__normalizedUserName_0='PERFORMANCECUSTOMERADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:28:04.502 +05:30 INF] Executed DbCommand (26ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:28:04.530 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedUserName_0='PERFORMANCEADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:28:04.558 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:28:04.581 +05:30 INF] Executed DbCommand (23ms) [Parameters=[@__normalizedUserName_0='INVOICEUSER'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:28:04.605 +05:30 INF] Executed DbCommand (23ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:28:04.629 +05:30 INF] Executed DbCommand (23ms) [Parameters=[@__normalizedUserName_0='INVOICECUSTOMERADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:28:04.658 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:28:04.686 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedUserName_0='INVOICEADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:28:04.714 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:28:04.742 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedUserName_0='INVENTORYUSER'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:28:04.770 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:28:04.793 +05:30 INF] Executed DbCommand (23ms) [Parameters=[@__normalizedUserName_0='INVENTORYCUSTOMERADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:28:04.819 +05:30 INF] Executed DbCommand (24ms) [Parameters=[@__normalizedEmail_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
[2025-06-05 19:28:04.846 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__normalizedUserName_0='INVENTORYADMIN'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
[2025-06-05 19:28:15.800 +05:30 INF] Executed action method Web.Host.Controllers.Identity.UsersController.BulkCreateUsersAsync (Web.Host), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 17849.0924ms.
[2025-06-05 19:28:15.800 +05:30 INF] Executing OkObjectResult, writing value of type 'Shared.Common.Response.ApiResponse`1[[Application.Identity.Commands.BulkCreateUsersResponse, Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
[2025-06-05 19:28:18.828 +05:30 INF] Executed action Web.Host.Controllers.Identity.UsersController.BulkCreateUsersAsync (Web.Host) in 20936.9682ms
[2025-06-05 19:28:18.830 +05:30 INF] Executed endpoint 'Web.Host.Controllers.Identity.UsersController.BulkCreateUsersAsync (Web.Host)'
[2025-06-05 19:28:18.831 +05:30 INF] Request finished HTTP/1.1 POST https://localhost:44391/api/users/bulk-create - 200 null application/json; charset=utf-8 20942.3569ms
[2025-06-05 19:41:08.528 +05:30 INF] Request starting HTTP/1.1 POST https://localhost:44391/api/users/bulk-update-roles - application/json 948
[2025-06-05 19:41:08.532 +05:30 INF] CORS policy execution successful.
[2025-06-05 19:41:08.532 +05:30 DBG] GetIdentifierAsync: Found identifier: "lrbnewqa"
[2025-06-05 19:41:08.532 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "lrbnewqa"
[2025-06-05 19:41:08.533 +05:30 INF] Executing endpoint 'Web.Host.Controllers.Identity.UsersController.BulkUpdateUserRolesAsync (Web.Host)'
[2025-06-05 19:41:08.536 +05:30 INF] Route matched with {action = "BulkUpdateUserRoles", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Shared.Common.Response.ApiResponse`1[Application.Identity.Commands.BulkUpdateUserRolesResponse]]] BulkUpdateUserRolesAsync(Application.Identity.Commands.BulkUpdateUserRolesCommand) on controller Web.Host.Controllers.Identity.UsersController (Web.Host).
[2025-06-05 19:41:08.565 +05:30 INF] Executing action method Web.Host.Controllers.Identity.UsersController.BulkUpdateUserRolesAsync (Web.Host) - Validation state: "Valid"
[2025-06-05 19:41:31.295 +05:30 INF] Executed DbCommand (29ms) [Parameters=[@__p_0='0197405e-bdbc-734e-8845-ffcfd8ef8aa3' (Nullable = true)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."Id" = @__p_0
LIMIT 1
[2025-06-05 19:41:31.394 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__roleId_0='01974062-7b5c-7651-843c-fdceeea99fff'], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."ConcurrencyStamp", r."CreatedAt", r."CreatedBy", r."Description", r."IsActive", r."IsDeleted", r."IsSystemRole", r."ModifiedAt", r."ModifiedBy", r."Name", r."NormalizedName", r."Permissions", r."ProductId", r."TenantId"
FROM "Genp"."Roles" AS r
WHERE NOT (r."IsDeleted") AND r."Id" = @__roleId_0
LIMIT 1
[2025-06-05 19:41:31.449 +05:30 INF] Executed DbCommand (26ms) [Parameters=[@__roleId_0='01974062-49d6-7817-a571-19f16396b8e4'], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."ConcurrencyStamp", r."CreatedAt", r."CreatedBy", r."Description", r."IsActive", r."IsDeleted", r."IsSystemRole", r."ModifiedAt", r."ModifiedBy", r."Name", r."NormalizedName", r."Permissions", r."ProductId", r."TenantId"
FROM "Genp"."Roles" AS r
WHERE NOT (r."IsDeleted") AND r."Id" = @__roleId_0
LIMIT 1
[2025-06-05 19:41:31.477 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__p_0='0197405e-bd4b-730e-89f8-fdcc00693100' (Nullable = true)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."Id" = @__p_0
LIMIT 1
[2025-06-05 19:41:31.502 +05:30 INF] Executed DbCommand (24ms) [Parameters=[@__roleId_0='01974062-49d6-7817-a571-19f16396b8e4'], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."ConcurrencyStamp", r."CreatedAt", r."CreatedBy", r."Description", r."IsActive", r."IsDeleted", r."IsSystemRole", r."ModifiedAt", r."ModifiedBy", r."Name", r."NormalizedName", r."Permissions", r."ProductId", r."TenantId"
FROM "Genp"."Roles" AS r
WHERE NOT (r."IsDeleted") AND r."Id" = @__roleId_0
LIMIT 1
[2025-06-05 19:41:31.530 +05:30 INF] Executed DbCommand (28ms) [Parameters=[@__p_0='0197405e-bcd7-7c63-bd9a-4d93aa51bba1' (Nullable = true)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."Id" = @__p_0
LIMIT 1
[2025-06-05 19:41:31.558 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__roleId_0='01974062-49d6-7817-a571-19f16396b8e4'], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."ConcurrencyStamp", r."CreatedAt", r."CreatedBy", r."Description", r."IsActive", r."IsDeleted", r."IsSystemRole", r."ModifiedAt", r."ModifiedBy", r."Name", r."NormalizedName", r."Permissions", r."ProductId", r."TenantId"
FROM "Genp"."Roles" AS r
WHERE NOT (r."IsDeleted") AND r."Id" = @__roleId_0
LIMIT 1
[2025-06-05 19:41:31.586 +05:30 INF] Executed DbCommand (27ms) [Parameters=[@__p_0='0197405e-bc7d-7fdf-a7e3-64129696d339' (Nullable = true)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AccessFailedCount", u."ConcurrencyStamp", u."CreatedAt", u."CreatedBy", u."Email", u."EmailConfirmed", u."ExternalUserId", u."FirstName", u."IsActive", u."IsDeleted", u."LastLoginAt", u."LastName", u."LockoutEnabled", u."LockoutEnd", u."ModifiedAt", u."ModifiedBy", u."NormalizedEmail", u."NormalizedUserName", u."PasswordHash", u."PhoneNumber", u."PhoneNumberConfirmed", u."RefreshToken", u."RefreshTokenExpiryTime", u."SecurityStamp", u."TenantId", u."TwoFactorEnabled", u."UserName"
FROM "Genp"."Users" AS u
WHERE NOT (u."IsDeleted") AND u."Id" = @__p_0
LIMIT 1
[2025-06-05 19:41:31.619 +05:30 INF] Executed DbCommand (32ms) [Parameters=[@__roleId_0='01974062-49d6-7817-a571-19f16396b8e4'], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."ConcurrencyStamp", r."CreatedAt", r."CreatedBy", r."Description", r."IsActive", r."IsDeleted", r."IsSystemRole", r."ModifiedAt", r."ModifiedBy", r."Name", r."NormalizedName", r."Permissions", r."ProductId", r."TenantId"
FROM "Genp"."Roles" AS r
WHERE NOT (r."IsDeleted") AND r."Id" = @__roleId_0
LIMIT 1
[2025-06-05 19:41:58.262 +05:30 INF] Executed DbCommand (24ms) [Parameters=[@__roleId_0='01974062-7b5c-7651-843c-fdceeea99fff'], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."ConcurrencyStamp", r."CreatedAt", r."CreatedBy", r."Description", r."IsActive", r."IsDeleted", r."IsSystemRole", r."ModifiedAt", r."ModifiedBy", r."Name", r."NormalizedName", r."Permissions", r."ProductId", r."TenantId"
FROM "Genp"."Roles" AS r
WHERE NOT (r."IsDeleted") AND r."Id" = @__roleId_0
LIMIT 1
[2025-06-05 19:42:08.803 +05:30 INF] Executed DbCommand (28ms) [Parameters=[@__roleId_0='01974062-49d6-7817-a571-19f16396b8e4'], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."ConcurrencyStamp", r."CreatedAt", r."CreatedBy", r."Description", r."IsActive", r."IsDeleted", r."IsSystemRole", r."ModifiedAt", r."ModifiedBy", r."Name", r."NormalizedName", r."Permissions", r."ProductId", r."TenantId"
FROM "Genp"."Roles" AS r
WHERE NOT (r."IsDeleted") AND r."Id" = @__roleId_0
LIMIT 1
[2025-06-05 19:42:18.265 +05:30 ERR] Failed executing DbCommand (33ms) [Parameters=[@__ef_filter__Id_0='lrbnewqa', @__userId_0='0197405e-bdbc-734e-8845-ffcfd8ef8aa3'], CommandType='"Text"', CommandTimeout='30']
SELECT r0."Name"
FROM "AspNetUserRoles" AS a
INNER JOIN (
    SELECT r."Id", r."Name"
    FROM "Genp"."Roles" AS r
    WHERE NOT (r."IsDeleted")
) AS r0 ON a."RoleId" = r0."Id"
WHERE a."TenantId" = @__ef_filter__Id_0 AND a."UserId" = @__userId_0
[2025-06-05 19:42:18.283 +05:30 ERR] An exception occurred while iterating over the results of a query for context type 'Infrastructure.Database.ApplicationDbContext'.
Npgsql.PostgresException (0x80004005): 42P01: relation "AspNetUserRoles" does not exist

POSITION: 24
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42P01
    MessageText: relation "AspNetUserRoles" does not exist
    Position: 24
    File: parse_relation.c
    Line: 1449
    Routine: parserOpenTable
Npgsql.PostgresException (0x80004005): 42P01: relation "AspNetUserRoles" does not exist

POSITION: 24
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42P01
    MessageText: relation "AspNetUserRoles" does not exist
    Position: 24
    File: parse_relation.c
    Line: 1449
    Routine: parserOpenTable
[2025-06-05 19:42:43.419 +05:30 ERR] Failed executing DbCommand (28ms) [Parameters=[@__ef_filter__Id_0='lrbnewqa', @__userId_0='0197405e-bdbc-734e-8845-ffcfd8ef8aa3'], CommandType='"Text"', CommandTimeout='30']
SELECT r0."Name"
FROM "AspNetUserRoles" AS a
INNER JOIN (
    SELECT r."Id", r."Name"
    FROM "Genp"."Roles" AS r
    WHERE NOT (r."IsDeleted")
) AS r0 ON a."RoleId" = r0."Id"
WHERE a."TenantId" = @__ef_filter__Id_0 AND a."UserId" = @__userId_0
[2025-06-05 19:42:43.420 +05:30 ERR] An exception occurred while iterating over the results of a query for context type 'Infrastructure.Database.ApplicationDbContext'.
Npgsql.PostgresException (0x80004005): 42P01: relation "AspNetUserRoles" does not exist

POSITION: 24
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42P01
    MessageText: relation "AspNetUserRoles" does not exist
    Position: 24
    File: parse_relation.c
    Line: 1449
    Routine: parserOpenTable
Npgsql.PostgresException (0x80004005): 42P01: relation "AspNetUserRoles" does not exist

POSITION: 24
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42P01
    MessageText: relation "AspNetUserRoles" does not exist
    Position: 24
    File: parse_relation.c
    Line: 1449
    Routine: parserOpenTable
