using Abstraction.Database.Repositories;
using Abstraction.MultiTenancy;
using Application.MultiTenancy.DTOs;
using Application.Products.DTOs;
using Application.Products.Specifications;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.MultiTenancy.Queries;

/// <summary>
/// Get tenant with product information query handler
/// </summary>
public class GetTenantWithProductQueryHandler : IRequestHandler<GetTenantWithProductQuery, Result<TenantWithProductDto>>
{
    private readonly ITenantService _tenantService;
    private readonly IRepository<Product> _productRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetTenantWithProductQueryHandler(
        ITenantService tenantService,
        IRepository<Product> productRepository)
    {
        _tenantService = tenantService;
        _productRepository = productRepository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<TenantWithProductDto>> Handle(GetTenantWithProductQuery request, CancellationToken cancellationToken)
    {
        try
        {
            // Get tenant information
            var tenant = await _tenantService.GetByIdAsync(request.TenantId);
            if (tenant == null)
            {
                return Result<TenantWithProductDto>.Failure($"Tenant with ID {request.TenantId} not found");
            }

            // Create the response DTO with tenant information
            var result = new TenantWithProductDto
            {
                Id = tenant.Id,
                Name = tenant.Name,
                ConnectionString = tenant.ConnectionString,
                AdminEmail = tenant.AdminEmail,
                IsActive = tenant.IsActive,
                ValidUpto = tenant.ValidUpto,
                Issuer = tenant.Issuer
            };
            return Result<TenantWithProductDto>.Success(result);
        }
        catch (Exception ex)
        {
            return Result<TenantWithProductDto>.Failure($"Error retrieving tenant with product: {ex.Message}");
        }
    }
}
