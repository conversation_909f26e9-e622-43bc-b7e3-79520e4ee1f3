using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for UserMetadata entity
/// </summary>
public class UserMetadataConfig : IEntityTypeConfiguration<UserMetadata>
{
    public void Configure(EntityTypeBuilder<UserMetadata> builder)
    {
        builder.ToTable("UserMetadata", "Genp");

        // Properties
        builder.Property(e => e.UserId)
            .IsRequired();

        builder.Property(e => e.MetadataId)
            .IsRequired();

        builder.Property(e => e.IsUnique)
            .HasDefaultValue(false);

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        // Indexes
        builder.HasIndex(e => e.UserId)
            .HasDatabaseName("IX_UserMetadata_UserId");

        builder.HasIndex(e => e.MetadataId)
            .HasDatabaseName("IX_UserMetadata_MetadataId");

        builder.HasIndex(e => e.IsUnique)
            .HasDatabaseName("IX_UserMetadata_IsUnique");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_UserMetadata_IsActive")
            .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

        // Unique constraint
        builder.HasIndex(e => new { e.UserId, e.MetadataId })
            .IsUnique()
            .HasDatabaseName("IX_UserMetadata_UserId_MetadataId");

        // Multi-tenancy
        builder.IsMultiTenant();

        // Relationships
        builder.HasOne(e => e.User)
            .WithMany(e => e.UserMetadata)
            .HasForeignKey(e => e.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.Metadata)
            .WithMany(e => e.UserMetadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.UserValues)
            .WithOne(e => e.UserMetadata)
            .HasForeignKey(e => e.UserMetadataId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
