using Ardalis.Specification;
using Domain.Entities;

namespace Application.Integrations.Specifications;

/// <summary>
/// Specification to count integrations with filters
/// </summary>
public class IntegrationsCountSpec : Specification<Integration>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public IntegrationsCountSpec(
        string? searchTerm,
        bool? isActive,
        Guid? productId,
        string? authType)
    {
        Query.Where(i => true); // Base query

        if (!string.IsNullOrEmpty(searchTerm))
        {
            Query.Where(i => i.Name.Contains(searchTerm));
        }

        if (isActive.HasValue)
        {
            Query.Where(i => i.IsActive == isActive.Value);
        }

        if (productId.HasValue)
        {
            Query.Where(i => i.ProductId == productId.Value);
        }

        if (!string.IsNullOrEmpty(authType))
        {
            Query.Where(i => i.AuthType == authType);
        }
    }
}
