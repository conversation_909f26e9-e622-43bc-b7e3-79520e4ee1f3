using Application.Identity.DTOs;
using Abstraction.Identity;
using Abstraction.Identity.Dtos;
using MediatR;
using Shared.Common.Response;
using Mapster;

namespace Application.Identity.Queries.GetUserRoles;

/// <summary>
/// Handler for GetUserRolesQuery
/// </summary>
public class GetUserRolesQueryHandler : IRequestHandler<GetUserRolesQuery, ApiResponse<List<UserRoleDto>>>
{
    private readonly IIdentityService _identityService;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetUserRolesQueryHandler(IIdentityService identityService)
    {
        _identityService = identityService;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<ApiResponse<List<UserRoleDto>>> Handle(GetUserRolesQuery request, CancellationToken cancellationToken)
    {
        try
        {
            // Validate the request
            if (string.IsNullOrWhiteSpace(request.UserId))
            {
                return new ApiResponse<List<UserRoleDto>>(false, "User ID is required.", new List<UserRoleDto>());
            }

            // Check if user exists
            var user = await _identityService.GetUserByIdAsync(request.UserId);
            if (user == null)
            {
                return new ApiResponse<List<UserRoleDto>>(false, "User not found.", new List<UserRoleDto>());
            }

            // Get user roles with detailed information
            var userRoleDetails = await _identityService.GetUserRolesDetailedAsync(request.UserId);

            // Convert to UserRoleDto
            var userRoles = userRoleDetails.Adapt<List<UserRoleDto>>();

            return new ApiResponse<List<UserRoleDto>>(true, "User roles retrieved successfully.", userRoles);
        }
        catch (Exception ex)
        {
            return new ApiResponse<List<UserRoleDto>>(false, $"Error retrieving user roles: {ex.Message}", new List<UserRoleDto>());
        }
    }
}
