using Application.Objects.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Objects.Queries;

/// <summary>
/// Get Object by ID query
/// </summary>
public class GetObjectByIdQuery : IRequest<Result<ObjectDto>>
{
    /// <summary>
    /// Object ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public GetObjectByIdQuery(Guid id)
    {
        Id = id;
    }
}
