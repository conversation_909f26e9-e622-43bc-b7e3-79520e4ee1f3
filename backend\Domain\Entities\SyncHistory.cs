using System.ComponentModel.DataAnnotations;
using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Sync History entity - stores synchronization operation history and results
/// </summary>
public class SyncHistory : AuditableEntity, IAggregateRoot
{
    /// <summary>
    /// Integration ID this sync history belongs to
    /// </summary>
    public Guid IntegrationId { get; set; }

    /// <summary>
    /// Object ID this sync operation was performed on
    /// </summary>
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Type of sync operation (e.g., "Full", "Incremental", "Manual")
    /// </summary>
    [MaxLength(50)]
    public string SyncType { get; set; } = string.Empty;

    /// <summary>
    /// Data flow direction (e.g., "In", "Out", "Both")
    /// </summary>
    [MaxLength(10)]
    public string Direction { get; set; } = string.Empty;

    /// <summary>
    /// Sync operation status (e.g., "Success", "Failed", "Partial", "InProgress")
    /// </summary>
    [MaxLength(50)]
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// Number of records processed in this sync operation
    /// </summary>
    public int RecordsProcessed { get; set; } = 0;

    /// <summary>
    /// Number of records that succeeded
    /// </summary>
    public int RecordsSucceeded { get; set; } = 0;

    /// <summary>
    /// Number of records that failed
    /// </summary>
    public int RecordsFailed { get; set; } = 0;

    /// <summary>
    /// When the sync operation started
    /// </summary>
    public DateTime StartedAt { get; set; }

    /// <summary>
    /// When the sync operation completed (null if still in progress)
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// Error message if the sync operation failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Detailed sync operation information stored as JSON
    /// </summary>
    public string? SyncDetails { get; set; }

    // Navigation properties
    /// <summary>
    /// Integration this sync history belongs to
    /// </summary>
    public virtual Integration Integration { get; set; } = null!;

    /// <summary>
    /// Object this sync operation was performed on
    /// </summary>
    public virtual Object Object { get; set; } = null!;
}
