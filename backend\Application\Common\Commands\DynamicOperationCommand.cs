using Abstraction.Common;
using MediatR;
using Shared.Common.Response;

namespace Application.Common.Commands;

/// <summary>
/// Command for single dynamic operation
/// </summary>
public class DynamicOperationCommand : IRequest<Result<DynamicOperationResponse>>
{
    /// <summary>
    /// Entity type name
    /// </summary>
    public string EntityType { get; set; } = string.Empty;

    /// <summary>
    /// Operation type
    /// </summary>
    public DynamicOperationType OperationType { get; set; }

    /// <summary>
    /// Property values as key-value pairs
    /// </summary>
    public Dictionary<string, object?> PropertyValues { get; set; } = new();

    /// <summary>
    /// Key properties for upsert operations
    /// </summary>
    public List<string> KeyProperties { get; set; } = new();
}

/// <summary>
/// Command for bulk dynamic operations
/// </summary>
public class BulkDynamicOperationCommand : IRequest<Result<DynamicOperationResponse>>
{
    /// <summary>
    /// Entity type name
    /// </summary>
    public string EntityType { get; set; } = string.Empty;

    /// <summary>
    /// Operation type
    /// </summary>
    public DynamicOperationType OperationType { get; set; }

    /// <summary>
    /// List of property values for bulk operations
    /// </summary>
    public List<Dictionary<string, object?>> PropertyValuesList { get; set; } = new();

    /// <summary>
    /// Key properties for upsert operations
    /// </summary>
    public List<string> KeyProperties { get; set; } = new();

    /// <summary>
    /// Batch size for processing
    /// </summary>
    public int BatchSize { get; set; } = 1000;
}

/// <summary>
/// Command for dynamic product operations
/// </summary>
public class DynamicProductOperationCommand : IRequest<Result<DynamicOperationResponse>>
{
    /// <summary>
    /// Operation type
    /// </summary>
    public DynamicOperationType OperationType { get; set; }

    /// <summary>
    /// Product data
    /// </summary>
    public List<Dictionary<string, object?>> ProductData { get; set; } = new();

    /// <summary>
    /// Include related features
    /// </summary>
    public bool IncludeFeatures { get; set; } = false;

    /// <summary>
    /// Include related objects
    /// </summary>
    public bool IncludeObjects { get; set; } = false;

    /// <summary>
    /// Include metadata
    /// </summary>
    public bool IncludeMetadata { get; set; } = false;
}

/// <summary>
/// Command for dynamic feature operations
/// </summary>
public class DynamicFeatureOperationCommand : IRequest<Result<DynamicOperationResponse>>
{
    /// <summary>
    /// Operation type
    /// </summary>
    public DynamicOperationType OperationType { get; set; }

    /// <summary>
    /// Feature data
    /// </summary>
    public List<Dictionary<string, object?>> FeatureData { get; set; } = new();

    /// <summary>
    /// Include related objects
    /// </summary>
    public bool IncludeObjects { get; set; } = false;

    /// <summary>
    /// Include metadata
    /// </summary>
    public bool IncludeMetadata { get; set; } = false;
}

/// <summary>
/// Command for dynamic object operations
/// </summary>
public class DynamicObjectOperationCommand : IRequest<Result<DynamicOperationResponse>>
{
    /// <summary>
    /// Operation type
    /// </summary>
    public DynamicOperationType OperationType { get; set; }

    /// <summary>
    /// Object data
    /// </summary>
    public List<Dictionary<string, object?>> ObjectData { get; set; } = new();

    /// <summary>
    /// Include metadata
    /// </summary>
    public bool IncludeMetadata { get; set; } = false;

    /// <summary>
    /// Include values
    /// </summary>
    public bool IncludeValues { get; set; } = false;
}

/// <summary>
/// Command for dynamic value operations (ProductValue, FeatureValue, ObjectValue, etc.)
/// </summary>
public class DynamicValueOperationCommand : IRequest<Result<DynamicOperationResponse>>
{
    /// <summary>
    /// Value entity type (ProductValue, FeatureValue, ObjectValue, etc.)
    /// </summary>
    public string ValueEntityType { get; set; } = string.Empty;

    /// <summary>
    /// Operation type
    /// </summary>
    public DynamicOperationType OperationType { get; set; }

    /// <summary>
    /// Value data
    /// </summary>
    public List<Dictionary<string, object?>> ValueData { get; set; } = new();

    /// <summary>
    /// Reference ID for grouping related values
    /// </summary>
    public Guid? RefId { get; set; }

    /// <summary>
    /// Validate metadata existence
    /// </summary>
    public bool ValidateMetadata { get; set; } = true;
}

/// <summary>
/// Command for complex hierarchical operations
/// </summary>
public class DynamicHierarchicalOperationCommand : IRequest<Result<DynamicOperationResponse>>
{
    /// <summary>
    /// Root entity type
    /// </summary>
    public string RootEntityType { get; set; } = string.Empty;

    /// <summary>
    /// Operation type
    /// </summary>
    public DynamicOperationType OperationType { get; set; }

    /// <summary>
    /// Hierarchical data structure
    /// </summary>
    public List<HierarchicalEntityData> HierarchicalData { get; set; } = new();

    /// <summary>
    /// Process children recursively
    /// </summary>
    public bool ProcessChildrenRecursively { get; set; } = true;
}

/// <summary>
/// Command for creating object with metadata and values
/// </summary>
public class CreateObjectWithMetadataCommand : IRequest<Result<DynamicOperationResponse>>
{
    /// <summary>
    /// Product ID that the object belongs to
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Feature ID that the object belongs to
    /// </summary>
    public Guid FeatureId { get; set; }

    /// <summary>
    /// Object name - will be checked for existence
    /// </summary>
    public string ObjectName { get; set; } = string.Empty;

    /// <summary>
    /// Metadata definitions for the object
    /// </summary>
    public List<Dictionary<string, object?>> Metadata { get; set; } = new();

    /// <summary>
    /// Values to be stored in ObjectValues table
    /// </summary>
    public List<Dictionary<string, object?>> Values { get; set; } = new();
}

/// <summary>
/// Hierarchical entity data structure
/// </summary>
public class HierarchicalEntityData
{
    /// <summary>
    /// Entity type
    /// </summary>
    public string EntityType { get; set; } = string.Empty;

    /// <summary>
    /// Property values
    /// </summary>
    public Dictionary<string, object?> PropertyValues { get; set; } = new();

    /// <summary>
    /// Child entities
    /// </summary>
    public List<HierarchicalEntityData> Children { get; set; } = new();

    /// <summary>
    /// Related metadata
    /// </summary>
    public List<Dictionary<string, object?>> Metadata { get; set; } = new();

    /// <summary>
    /// Related values
    /// </summary>
    public List<Dictionary<string, object?>> Values { get; set; } = new();
}
