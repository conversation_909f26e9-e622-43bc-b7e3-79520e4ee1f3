using Application.RoleValues.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.RoleValues.Commands;

/// <summary>
/// Create RoleValue command
/// </summary>
public class CreateRoleValueCommand : IRequest<Result<RoleValueDto>>
{
    /// <summary>
    /// Role metadata ID
    /// </summary>
    public Guid RoleMetadataId { get; set; }

    /// <summary>
    /// Reference ID for grouping related values
    /// </summary>
    public Guid? RefId { get; set; }

    /// <summary>
    /// Parent role value ID for hierarchical structure
    /// </summary>
    public Guid? ParentRoleValueId { get; set; }

    /// <summary>
    /// The actual value as text
    /// </summary>
    public string? Value { get; set; }
}
