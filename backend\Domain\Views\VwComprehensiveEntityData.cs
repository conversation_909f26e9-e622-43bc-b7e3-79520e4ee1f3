namespace Domain.Views;

/// <summary>
/// View model for VwComprehensiveEntityData database view
/// </summary>
public class VwComprehensiveEntityData
{
    // Product information
    public Guid ProductId { get; set; }
    public string? TenantId { get; set; }
    public string? ProductName { get; set; }
    public string? ProductDescription { get; set; }
    public string? ProductVersion { get; set; }
    public bool ProductIsActive { get; set; }
    
    // Feature information
    public Guid? FeatureId { get; set; }
    public string? FeatureName { get; set; }
    public string? FeatureDescription { get; set; }
    public bool? FeatureIsActive { get; set; }
    
    // Object information (with hierarchy)
    public Guid? ObjectId { get; set; }
    public Guid? ParentObjectId { get; set; }
    public string? ObjectName { get; set; }
    public string? ObjectDescription { get; set; }
    public bool? ObjectIsActive { get; set; }
    
    // Parent Object information (for hierarchy)
    public string? ParentObjectName { get; set; }
    public string? ParentObjectDescription { get; set; }
    
    // Metadata information
    public Guid? MetadataId { get; set; }
    public string? MetadataKey { get; set; }
    public string? DisplayLabel { get; set; }
    public string? HelpText { get; set; }
    public int? FieldOrder { get; set; }
    public bool? IsVisible { get; set; }
    public bool? IsReadonly { get; set; }
    public string? CustomPlaceholder { get; set; }
    public string? CustomOptions { get; set; }
    public bool? CustomIsRequired { get; set; }
    
    // DataType information
    public Guid? DataTypeId { get; set; }
    public string? DataTypeName { get; set; }
    public string? DataTypeDisplayName { get; set; }
    public string? DataTypeCategory { get; set; }
    public string? UiComponent { get; set; }
    public string? InputType { get; set; }
    public string? InputMask { get; set; }
    public string? Placeholder { get; set; }
    public string? DefaultOptions { get; set; }
    public string? ValidationPattern { get; set; }
    public int? MinLength { get; set; }
    public int? MaxLength { get; set; }
    public decimal? MinValue { get; set; }
    public decimal? MaxValue { get; set; }
    public int? DecimalPlaces { get; set; }
    public decimal? StepValue { get; set; }
    public bool? IsRequired { get; set; }
    public bool? AllowsMultiple { get; set; }
    public bool? AllowsCustomOptions { get; set; }
    public string? HtmlAttributes { get; set; }
    
    // Metadata links
    public Guid? ProductMetadataId { get; set; }
    public bool? ProductMetadataIsUnique { get; set; }
    public bool? ProductMetadataIsActive { get; set; }
    
    public Guid? FeatureMetadataId { get; set; }
    public bool? FeatureMetadataIsUnique { get; set; }
    public bool? FeatureMetadataIsActive { get; set; }
    
    public Guid? ObjectMetadataId { get; set; }
    public bool? ObjectMetadataIsUnique { get; set; }
    public bool? ObjectMetadataIsActive { get; set; }
    
    // Values
    public Guid? ProductValueId { get; set; }
    public Guid? ProductValueRefId { get; set; }
    public string? ProductValue { get; set; }

    public Guid? FeatureValueId { get; set; }
    public Guid? FeatureValueRefId { get; set; }
    public string? FeatureValue { get; set; }
}
