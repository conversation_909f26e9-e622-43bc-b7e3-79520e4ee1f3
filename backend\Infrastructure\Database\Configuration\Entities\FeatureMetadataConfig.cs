using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for FeatureMetadata entity
/// </summary>
public class FeatureMetadataConfig : IEntityTypeConfiguration<FeatureMetadata>
{
    public void Configure(EntityTypeBuilder<FeatureMetadata> builder)
    {
        builder.ToTable("FeatureMetadata", "Genp");

        // Properties
        builder.Property(e => e.FeatureId)
            .IsRequired();

        builder.Property(e => e.MetadataId)
            .IsRequired();

        builder.Property(e => e.IsUnique)
            .HasDefaultValue(false);

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        // Indexes
        builder.HasIndex(e => e.FeatureId)
            .HasDatabaseName("IX_FeatureMetadata_FeatureId");

        builder.HasIndex(e => e.MetadataId)
            .HasDatabaseName("IX_FeatureMetadata_MetadataId");

        builder.HasIndex(e => e.IsUnique)
            .HasDatabaseName("IX_FeatureMetadata_IsUnique");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_FeatureMetadata_IsActive")
            .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

        // Unique constraint
        builder.HasIndex(e => new { e.FeatureId, e.MetadataId })
            .IsUnique()
            .HasDatabaseName("IX_FeatureMetadata_FeatureId_MetadataId");

        // Multi-tenancy
        builder.IsMultiTenant();

        // Relationships
        builder.HasOne(e => e.Feature)
            .WithMany(e => e.FeatureMetadata)
            .HasForeignKey(e => e.FeatureId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.Metadata)
            .WithMany(e => e.FeatureMetadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.FeatureValues)
            .WithOne(e => e.FeatureMetadata)
            .HasForeignKey(e => e.FeatureMetadataId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
