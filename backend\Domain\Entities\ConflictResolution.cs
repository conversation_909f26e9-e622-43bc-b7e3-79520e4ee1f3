using System.ComponentModel.DataAnnotations;
using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Conflict Resolution entity - stores conflict resolution strategies for data synchronization
/// </summary>
public class ConflictResolution : AuditableEntity, IAggregateRoot
{
    /// <summary>
    /// Integration ID this conflict resolution applies to
    /// </summary>
    public Guid IntegrationId { get; set; }

    /// <summary>
    /// Object ID this conflict resolution applies to
    /// </summary>
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Field name where the conflict occurred
    /// </summary>
    [MaxLength(255)]
    public string FieldName { get; set; } = string.Empty;

    /// <summary>
    /// Type of conflict (e.g., "ValueMismatch", "TypeMismatch", "MissingField")
    /// </summary>
    [MaxLength(50)]
    public string ConflictType { get; set; } = string.Empty;

    /// <summary>
    /// Resolution strategy (e.g., "SourceWins", "TargetWins", "Manual", "Merge")
    /// </summary>
    [MaxLength(50)]
    public string ResolutionStrategy { get; set; } = string.Empty;

    /// <summary>
    /// Source value that caused the conflict
    /// </summary>
    public string? SourceValue { get; set; }

    /// <summary>
    /// Target value that caused the conflict
    /// </summary>
    public string? TargetValue { get; set; }

    /// <summary>
    /// Resolved value after conflict resolution
    /// </summary>
    public string? ResolvedValue { get; set; }

    /// <summary>
    /// User ID who resolved the conflict
    /// </summary>
    public Guid? ResolvedBy { get; set; }

    /// <summary>
    /// When the conflict was resolved
    /// </summary>
    public DateTime? ResolvedAt { get; set; }

    /// <summary>
    /// Whether the conflict has been resolved
    /// </summary>
    public bool IsResolved { get; set; } = false;

    /// <summary>
    /// Merge rules configuration stored as JSON
    /// </summary>
    public string? MergeRules { get; set; }

    // Navigation properties
    /// <summary>
    /// Integration this conflict resolution applies to
    /// </summary>
    public virtual Integration Integration { get; set; } = null!;

    /// <summary>
    /// Object this conflict resolution applies to
    /// </summary>
    public virtual Object Object { get; set; } = null!;

    /// <summary>
    /// User who resolved the conflict
    /// </summary>
    public virtual User? ResolvedByUser { get; set; }
}
