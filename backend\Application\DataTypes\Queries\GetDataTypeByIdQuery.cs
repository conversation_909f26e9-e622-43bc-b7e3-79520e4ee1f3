using Application.DataTypes.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.DataTypes.Queries;

/// <summary>
/// Get DataType by ID query
/// </summary>
public class GetDataTypeByIdQuery : IRequest<Result<DataTypeDto>>
{
    /// <summary>
    /// DataType ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public GetDataTypeByIdQuery(Guid id)
    {
        Id = id;
    }
}
