using Application.Identity.DTOs;
using Abstraction.Identity;
using Abstraction.Identity.Dtos;
using MediatR;
using Shared.Common.Response;
using Mapster;
using Abstraction.Database.Repositories;

namespace Application.Identity.Commands;

/// <summary>
/// Handler for BulkUpdateUserRolesCommand
/// </summary>
public class BulkUpdateUserRolesCommandHandler : IRequestHandler<BulkUpdateUserRolesCommand, ApiResponse<BulkUpdateUserRolesResponse>>
{
    private readonly IIdentityService _identityService;
    private readonly IRoleService _roleService;
    private readonly IRepository<Domain.Entities.Product> _repository;

    /// <summary>
    /// Constructor
    /// </summary>
    public BulkUpdateUserRolesCommandHandler(IIdentityService identityService, IRoleService roleService, IRepository<Domain.Entities.Product> repository)
    {
        _identityService = identityService;
        _roleService = roleService;
        _repository = repository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<ApiResponse<BulkUpdateUserRolesResponse>> Handle(BulkUpdateUserRolesCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var response = new BulkUpdateUserRolesResponse
            {
                TotalRequested = request.UserRoleUpdates.Count,
                Message = "Bulk user role update completed."
            };

            if (!request.UserRoleUpdates.Any())
            {
                return new ApiResponse<BulkUpdateUserRolesResponse>(false, "No user role updates provided.", response);
            }

            // Validate all users and roles first if requested
            if (request.ValidateBeforeUpdate)
            {
                var validationErrors = await ValidateUserRoleUpdatesAsync(request.UserRoleUpdates);
                if (validationErrors.Any())
                {
                    response.Errors.AddRange(validationErrors);
                    response.Failed = validationErrors.Count;
                    return new ApiResponse<BulkUpdateUserRolesResponse>(false,
                        $"Validation failed for {validationErrors.Count} user role updates. No users were updated.", response);
                }
            }

            // Process each user role update
            for (int i = 0; i < request.UserRoleUpdates.Count; i++)
            {
                var userRoleUpdate = request.UserRoleUpdates[i];

                try
                {
                    // Get user information
                    var user = await _identityService.GetUserByIdAsync(userRoleUpdate.UserId);
                    if (user == null)
                    {
                        response.Errors.Add(new BulkUserRoleUpdateError
                        {
                            UserIndex = i,
                            UserId = userRoleUpdate.UserId,
                            AttemptedRoleIds = userRoleUpdate.RoleIds,
                            ErrorMessage = "User not found",
                            ErrorDetails = $"User with ID {userRoleUpdate.UserId} does not exist"
                        });
                        response.Failed++;

                        if (!request.ContinueOnError)
                        {
                            response.Message = $"Bulk update stopped at user {i + 1} due to error. {response.SuccessfullyUpdated} users updated successfully.";
                            break;
                        }
                        continue;
                    }

                    // Convert role IDs to role names for the existing UpdateUserRolesAsync method
                    var roleNames = new List<string>();
                    var userRoleDtos = new List<UserRoleDetailsDto>();

                    foreach (var roleId in userRoleUpdate.RoleIds)
                    {
                        var role = await _roleService.GetByIdAsync(roleId);
                        if (role != null)
                        {
                            roleNames.Add(role.Name);
                            userRoleDtos.Add(new UserRoleDetailsDto
                            {
                                RoleId = role.Id,
                                RoleName = role.Name,
                                Description = role.Description,
                                Enabled = true
                            });
                        }
                    }

                    // Update user roles using the existing service method
                    var updateResult = await _identityService.UpdateUserRolesAsync(userRoleUpdate.UserId, userRoleDtos);

                    if (updateResult.Succeeded)
                    {
                        var updateResultItem = new UserRoleUpdateResult
                        {
                            UserId = userRoleUpdate.UserId,
                            Email = user.Email ?? string.Empty,
                            UserName = user.UserName ?? string.Empty,
                            Message = "Roles updated successfully",
                            AssignedRoles = userRoleDtos.Adapt<List<UserRoleDto>>()
                        };

                        response.UpdatedUsers.Add(updateResultItem);
                        response.SuccessfullyUpdated++;
                    }
                    else
                    {
                        response.Errors.Add(new BulkUserRoleUpdateError
                        {
                            UserIndex = i,
                            UserId = userRoleUpdate.UserId,
                            AttemptedRoleIds = userRoleUpdate.RoleIds,
                            ErrorMessage = updateResult.Message ?? "Unknown error occurred during role update",
                            ErrorDetails = updateResult.Message
                        });
                        response.Failed++;

                        // Stop processing if ContinueOnError is false
                        if (!request.ContinueOnError)
                        {
                            response.Message = $"Bulk update stopped at user {i + 1} due to error. {response.SuccessfullyUpdated} users updated successfully.";
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    response.Errors.Add(new BulkUserRoleUpdateError
                    {
                        UserIndex = i,
                        UserId = userRoleUpdate.UserId,
                        AttemptedRoleIds = userRoleUpdate.RoleIds,
                        ErrorMessage = $"Exception occurred: {ex.Message}",
                        ErrorDetails = ex.ToString()
                    });
                    response.Failed++;

                    // Stop processing if ContinueOnError is false
                    if (!request.ContinueOnError)
                    {
                        response.Message = $"Bulk update stopped at user {i + 1} due to exception. {response.SuccessfullyUpdated} users updated successfully.";
                        break;
                    }
                }
            }

            // Update final message
            if (response.Failed == 0)
            {
                var product = await _repository.GetByIdAsync(request.ProductId);
                if (product != null)
                {
                    product.IsUserImported = true;
                    await _repository.UpdateAsync(product);
                }
                response.Message = $"All {response.SuccessfullyUpdated} user role updates completed successfully.";
                return new ApiResponse<BulkUpdateUserRolesResponse>(true, response.Message, response);
            }
            else if (response.SuccessfullyUpdated > 0)
            {
                response.Message = $"Partial success: {response.SuccessfullyUpdated} users updated, {response.Failed} failed.";
                return new ApiResponse<BulkUpdateUserRolesResponse>(true, response.Message, response);
            }
            else
            {
                response.Message = $"All {response.Failed} user role updates failed.";
                return new ApiResponse<BulkUpdateUserRolesResponse>(false, response.Message, response);
            }
        }
        catch (Exception ex)
        {
            var errorResponse = new BulkUpdateUserRolesResponse
            {
                TotalRequested = request.UserRoleUpdates.Count,
                Failed = request.UserRoleUpdates.Count,
                Message = $"Bulk user role update failed: {ex.Message}"
            };

            return new ApiResponse<BulkUpdateUserRolesResponse>(false, errorResponse.Message, errorResponse);
        }
    }

    /// <summary>
    /// Validate user role updates before processing
    /// </summary>
    private async Task<List<BulkUserRoleUpdateError>> ValidateUserRoleUpdatesAsync(List<UserRoleUpdateRequest> userRoleUpdates)
    {
        var errors = new List<BulkUserRoleUpdateError>();

        for (int i = 0; i < userRoleUpdates.Count; i++)
        {
            var userRoleUpdate = userRoleUpdates[i];
            var userErrors = new List<string>();

            // Basic validation
            if (string.IsNullOrWhiteSpace(userRoleUpdate.UserId))
                userErrors.Add("User ID is required");

            if (userRoleUpdate.RoleIds == null || !userRoleUpdate.RoleIds.Any())
                userErrors.Add("At least one role ID is required");

            // Check for duplicate user IDs in the batch
            var duplicateUsers = userRoleUpdates.Where((u, index) => index != i && u.UserId.Equals(userRoleUpdate.UserId, StringComparison.OrdinalIgnoreCase)).Any();
            if (duplicateUsers)
                userErrors.Add("Duplicate user ID found in the batch");

            // Check if user exists in the system
            try
            {
                if (!string.IsNullOrWhiteSpace(userRoleUpdate.UserId))
                {
                    var user = await _identityService.GetUserByIdAsync(userRoleUpdate.UserId);
                    if (user == null)
                        userErrors.Add("User does not exist");
                }
            }
            catch (Exception ex)
            {
                userErrors.Add($"Error checking user existence: {ex.Message}");
            }

            // Check if all role IDs exist
            try
            {
                if (userRoleUpdate.RoleIds != null && userRoleUpdate.RoleIds.Any())
                {
                    var invalidRoleIds = new List<string>();
                    foreach (var roleId in userRoleUpdate.RoleIds)
                    {
                        if (!string.IsNullOrWhiteSpace(roleId))
                        {
                            var role = await _roleService.GetByIdAsync(roleId);
                            if (role == null)
                                invalidRoleIds.Add(roleId);
                        }
                    }

                    if (invalidRoleIds.Any())
                        userErrors.Add($"Invalid role IDs: {string.Join(", ", invalidRoleIds)}");
                }
            }
            catch (Exception ex)
            {
                userErrors.Add($"Error checking role existence: {ex.Message}");
            }

            if (userErrors.Any())
            {
                errors.Add(new BulkUserRoleUpdateError
                {
                    UserIndex = i,
                    UserId = userRoleUpdate.UserId,
                    AttemptedRoleIds = userRoleUpdate.RoleIds,
                    ErrorMessage = string.Join("; ", userErrors)
                });
            }
        }

        return errors;
    }
}
