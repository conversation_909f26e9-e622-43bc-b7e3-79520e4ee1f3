using Application.Products.DTOs;
using Application.Products.Specifications;
using Abstraction.Common;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.Products.Commands;

/// <summary>
/// Create product command handler
/// </summary>
public class CreateProductCommandHandler : IRequestHandler<CreateProductCommand, Result<ProductDto>>
{
    private readonly IRepository<Product> _productRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateProductCommandHandler(IRepository<Product> productRepository)
    {
        _productRepository = productRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<ProductDto>> Handle(CreateProductCommand request, CancellationToken cancellationToken)
    {
        // Check if product with same name already exists (tenant isolation handled by Finbuckle.MultiTenant)
        var existingProductSpec = new ProductByNameSpec(request.Name);
        var existingProduct = await _productRepository.GetBySpecAsync(existingProductSpec, cancellationToken);

        if (existingProduct != null)
        {
            return Result<ProductDto>.Failure("Product with this name already exists.");
        }

        // Create new product
        var product = new Product
        {
            Name = request.Name,
            Description = request.Description,
            Version = request.Version ?? "1.0.0",
            IsActive = request.IsActive,
            IsUserImported = request.IsUserImported,
            IsRoleAssigned = request.IsRoleAssigned,
            ApiKey = request.ApiKey,
            IsOnboardCompleted = request.IsOnboardCompleted,
            ApplicationUrl = request.ApplicationUrl
        };

        await _productRepository.AddAsync(product, cancellationToken);

        var dto = product.Adapt<ProductDto>();
        return Result<ProductDto>.Success(dto);
    }
}
