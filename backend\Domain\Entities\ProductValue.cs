using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Actual product instance data
/// </summary>
public class ProductValue : AuditableEntity, IAggregateRoot
{

    /// <summary>
    /// Product metadata ID
    /// </summary>
    public Guid ProductMetadataId { get; set; }

    /// <summary>
    /// Reference ID for grouping related values
    /// </summary>
    public Guid? RefId { get; set; }

    /// <summary>
    /// The actual value as text
    /// </summary>
    public string? Value { get; set; }

    // Navigation properties
    /// <summary>
    /// Product metadata link
    /// </summary>
    public virtual ProductMetadata ProductMetadata { get; set; } = null!;
}
