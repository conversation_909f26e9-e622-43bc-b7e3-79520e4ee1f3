using Application.Integrations.DTOs;
using Application.Integrations.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.Integrations.Commands;

/// <summary>
/// Create integration command handler
/// </summary>
public class CreateIntegrationCommandHandler : IRequestHandler<CreateIntegrationCommand, Result<IntegrationDto>>
{
    private readonly IRepository<Integration> _integrationRepository;
    private readonly IReadRepository<Product> _productRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateIntegrationCommandHandler(
        IRepository<Integration> integrationRepository,
        IReadRepository<Product> productRepository)
    {
        _integrationRepository = integrationRepository;
        _productRepository = productRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<IntegrationDto>> Handle(CreateIntegrationCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Verify product exists
            var product = await _productRepository.GetByIdAsync(request.ProductId, cancellationToken);
            if (product == null)
            {
                return Result<IntegrationDto>.Failure("Product not found.");
            }

            // Check if integration with same name already exists for this product
            var existingIntegrationSpec = new IntegrationByNameAndProductSpec(request.Name, request.ProductId);
            var existingIntegration = await _integrationRepository.GetBySpecAsync(existingIntegrationSpec, cancellationToken);

            if (existingIntegration != null)
            {
                return Result<IntegrationDto>.Failure("Integration with this name already exists for this product.");
            }

            // Create new integration
            var integration = new Integration
            {
                ProductId = request.ProductId,
                Name = request.Name,
                AuthType = request.AuthType,
                AuthConfig = request.AuthConfig,
                IsActive = request.IsActive,
                SyncFrequency = request.SyncFrequency
            };

            var createdIntegration = await _integrationRepository.AddAsync(integration, cancellationToken);

            var integrationDto = createdIntegration.Adapt<IntegrationDto>();
            return Result<IntegrationDto>.Success(integrationDto);
        }
        catch (Exception ex)
        {
            return Result<IntegrationDto>.Failure($"Failed to create integration: {ex.Message}");
        }
    }
}
