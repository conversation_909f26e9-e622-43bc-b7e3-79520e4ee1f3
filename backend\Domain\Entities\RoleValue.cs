using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Actual role instance data
/// </summary>
public class RoleValue : AuditableEntity, IAggregateRoot
{

    /// <summary>
    /// Role metadata ID
    /// </summary>
    public Guid RoleMetadataId { get; set; }

    /// <summary>
    /// Reference ID for grouping related values
    /// </summary>
    public Guid? RefId { get; set; }

    /// <summary>
    /// The actual value as text
    /// </summary>
    public string? Value { get; set; }

    // Navigation properties
    /// <summary>
    /// Role metadata link
    /// </summary>
    public virtual RoleMetadata RoleMetadata { get; set; } = null!;
}
