using Application.ObjectValues.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.ObjectValues.Queries.GetObjectValue;

/// <summary>
/// Get ObjectValues by Feature with full metadata information
/// </summary>
public class GetObjectValuesByFeatureQuery : IRequest<Result<List<ObjectValueResponseDto>>>
{
    /// <summary>
    /// Feature ID
    /// </summary>
    public Guid FeatureId { get; set; }

    /// <summary>
    /// Object ID filter (optional)
    /// </summary>
    public Guid? ObjectId { get; set; }

    /// <summary>
    /// Include child values in hierarchical structure
    /// </summary>
    public bool IncludeChildValues { get; set; } = true;

    /// <summary>
    /// Only return visible fields
    /// </summary>
    public bool OnlyVisibleFields { get; set; } = false;

    /// <summary>
    /// Order by
    /// </summary>
    public string? OrderBy { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public GetObjectValuesByFeatureQuery(Guid featureId)
    {
        FeatureId = featureId;
    }
}
