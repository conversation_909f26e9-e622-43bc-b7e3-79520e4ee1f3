using Application.Integrations.Commands;
using Application.Integrations.DTOs;
using Application.Integrations.Queries;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;

namespace Web.Host.Controllers;

/// <summary>
/// Integrations controller
/// </summary>
[Route("api/[controller]")]
public class IntegrationsController : BaseApiController
{
    /// <summary>
    /// Get all integrations with pagination
    /// </summary>
    [HttpGet]
    [TenantIdHeader]
    public async Task<ActionResult<PaginatedResult<IntegrationDto>>> GetIntegrations([FromQuery] GetIntegrationsQuery query)
    {
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get integration by ID
    /// </summary>
    [HttpGet("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<IntegrationDto>>> GetIntegrationById(Guid id)
    {
        return Ok(await Mediator.Send(new GetIntegrationByIdQuery(id)));
    }

    /// <summary>
    /// Create a new integration
    /// </summary>
    [HttpPost]
    [TenantIdHeader]
    public async Task<ActionResult<Result<IntegrationDto>>> CreateIntegration(CreateIntegrationCommand command)
    {
        return Ok(await Mediator.Send(command));
    }

    /// <summary>
    /// Update an existing integration
    /// </summary>
    [HttpPut("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<IntegrationDto>>> UpdateIntegration(Guid id, UpdateIntegrationCommand command)
    {
        command.Id = id;
        return Ok(await Mediator.Send(command));
    }

    /// <summary>
    /// Delete an integration
    /// </summary>
    [HttpDelete("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<bool>>> DeleteIntegration(Guid id)
    {
        return Ok(await Mediator.Send(new DeleteIntegrationCommand(id)));
    }

    /// <summary>
    /// Create multiple integrations in bulk
    /// </summary>
    [HttpPost("bulk")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<List<IntegrationDto>>>> CreateIntegrations(CreateIntegrationsCommand command)
    {
        return Ok(await Mediator.Send(command));
    }
}
