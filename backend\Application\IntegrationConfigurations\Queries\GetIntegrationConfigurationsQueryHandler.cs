using Application.IntegrationConfigurations.DTOs;
using Application.IntegrationConfigurations.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.IntegrationConfigurations.Queries;

/// <summary>
/// Get integration configurations query handler
/// </summary>
public class GetIntegrationConfigurationsQueryHandler : IRequestHandler<GetIntegrationConfigurationsQuery, PaginatedResult<ViewIntegrationConfigurationDto>>
{
    private readonly IReadRepository<IntegrationConfiguration> _configurationRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetIntegrationConfigurationsQueryHandler(IReadRepository<IntegrationConfiguration> configurationRepository)
    {
        _configurationRepository = configurationRepository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<PaginatedResult<ViewIntegrationConfigurationDto>> Handle(GetIntegrationConfigurationsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            // Calculate pagination
            var skip = (request.PageNumber - 1) * request.PageSize;

            // Create specifications for data and count
            var dataSpec = new IntegrationConfigurationsWithFiltersSpec(
                request.SearchTerm,
                request.IsActive,
                request.IntegrationId,
                request.IntegrationApiId,
                request.ObjectId,
                request.Direction,
                skip,
                request.PageSize);

            var countSpec = new IntegrationConfigurationsCountSpec(
                request.SearchTerm,
                request.IsActive,
                request.IntegrationId,
                request.IntegrationApiId,
                request.ObjectId,
                request.Direction);

            // Get data and count
            var configurations = await _configurationRepository.ListAsync(dataSpec, cancellationToken);
            var totalCount = await _configurationRepository.CountAsync(countSpec, cancellationToken);

            // Map to view DTOs
            var viewDtos = configurations.Select(config => new ViewIntegrationConfigurationDto
            {
                Id = config.Id,
                IntegrationId = config.IntegrationId,
                IntegrationName = config.Integration?.Name ?? string.Empty,
                IntegrationApiId = config.IntegrationApiId,
                IntegrationApiName = config.IntegrationApi?.Name ?? string.Empty,
                ObjectId = config.ObjectId,
                ObjectName = config.Object?.Name ?? string.Empty,
                Direction = config.Direction,
                IsActive = config.IsActive,
                CreatedAt = config.CreatedAt,
                CreatedBy = config.CreatedBy,
                ModifiedAt = config.ModifiedAt,
                ModifiedBy = config.ModifiedBy
            }).ToList();

            return new PaginatedResult<ViewIntegrationConfigurationDto>(
                viewDtos,
                request.PageNumber,
                request.PageSize,
                totalCount);
        }
        catch (Exception ex)
        {
            return PaginatedResult<ViewIntegrationConfigurationDto>.Failure($"Failed to get integration configurations: {ex.Message}");
        }
    }
}
