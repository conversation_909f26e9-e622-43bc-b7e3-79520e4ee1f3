using Application.ObjectValues.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.ObjectValues.Commands;

/// <summary>
/// Update ObjectValue command
/// </summary>
public class UpdateObjectValueCommand : IRequest<Result<ObjectValueDto>>
{
    /// <summary>
    /// ObjectValue ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Object metadata ID
    /// </summary>
    public Guid ObjectMetadataId { get; set; }

    /// <summary>
    /// Reference ID for grouping related values
    /// </summary>
    public Guid? RefId { get; set; }

    /// <summary>
    /// Parent object value ID for hierarchical structure
    /// </summary>
    public Guid? ParentObjectValueId { get; set; }

    /// <summary>
    /// The actual value as text
    /// </summary>
    public string? Value { get; set; }
}
