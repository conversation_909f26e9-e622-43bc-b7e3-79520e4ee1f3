using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Product entity - stores product types/templates
/// </summary>
public class Product : AuditableEntity, IAggregateRoot
{
    /// <summary>
    /// Product name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Product description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Product version
    /// </summary>
    public string Version { get; set; } = "1.0.0";

    /// <summary>
    /// Whether the product is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Whether user has been imported
    /// </summary>
    public bool IsUserImported { get; set; } = false;

    /// <summary>
    /// Whether role has been assigned
    /// </summary>
    public bool IsRoleAssigned { get; set; } = false;

    /// <summary>
    /// API key for the product
    /// </summary>
    public string? ApiKey { get; set; }

    /// <summary>
    /// Whether onboarding process is completed
    /// </summary>
    public bool IsOnboardCompleted { get; set; } = false;

    /// <summary>
    /// Application URL for the product
    /// </summary>
    public string? ApplicationUrl { get; set; }

    /// <summary>
    /// Icon for the product
    /// </summary>
    public string? Icon { get; set; }
    // Navigation Properties
    /// <summary>
    /// Features belonging to this product
    /// </summary>
    public virtual ICollection<Feature> Features { get; set; } = new List<Feature>();

    /// <summary>
    /// Roles belonging to this product
    /// </summary>
    public virtual ICollection<Role> Roles { get; set; } = new List<Role>();

    /// <summary>
    /// Subscriptions for this product
    /// </summary>
    public virtual ICollection<Subscription> Subscriptions { get; set; } = new List<Subscription>();

    /// <summary>
    /// Product metadata links
    /// </summary>
    public virtual ICollection<ProductMetadata> ProductMetadata { get; set; } = new List<ProductMetadata>();

    /// <summary>
    /// Integrations belonging to this product
    /// </summary>
    public virtual ICollection<Integration> Integrations { get; set; } = new List<Integration>();

    /// <summary>
    /// Integration APIs belonging to this product
    /// </summary>
    public virtual ICollection<IntegrationApi> IntegrationApis { get; set; } = new List<IntegrationApi>();
}
