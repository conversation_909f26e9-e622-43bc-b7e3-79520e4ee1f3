using Domain.Common.Contracts;
using Finbuckle.MultiTenant;
using Microsoft.AspNetCore.Identity;

namespace Domain.Entities;

/// <summary>
/// User entity - combines Identity authentication with business logic
/// </summary>
public class User : IdentityUser<Guid>, IAggregateRoot
{
    /// <summary>
    /// First name
    /// </summary>
    public string? FirstName { get; set; }

    /// <summary>
    /// Last name
    /// </summary>
    public string? LastName { get; set; }

    /// <summary>
    /// External user ID (for integration with external systems)
    /// </summary>
    public string? ExternalUserId { get; set; }

    /// <summary>
    /// Whether the user is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Last login timestamp
    /// </summary>
    public DateTime? LastLoginAt { get; set; }

    /// <summary>
    /// Refresh token for JWT authentication
    /// </summary>
    public string? RefreshToken { get; set; }

    /// <summary>
    /// Refresh token expiry time
    /// </summary>
    public DateTime? RefreshTokenExpiryTime { get; set; }

    /// <summary>
    /// When the user was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Who created the user
    /// </summary>
    public Guid? CreatedBy { get; set; }

    /// <summary>
    /// When the user was last modified
    /// </summary>
    public DateTime ModifiedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Who last modified the user
    /// </summary>
    public Guid? ModifiedBy { get; set; }

    /// <summary>
    /// Whether the user is soft deleted
    /// </summary>
    public bool IsDeleted { get; set; } = false;

    // Navigation properties
    /// <summary>
    /// User role mappings
    /// </summary>
    public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();

    /// <summary>
    /// User metadata links
    /// </summary>
    public virtual ICollection<UserMetadata> UserMetadata { get; set; } = new List<UserMetadata>();

    /// <summary>
    /// Field mappings associated with this user
    /// </summary>
    public virtual ICollection<FieldMapping> FieldMappings { get; set; } = new List<FieldMapping>();

    /// <summary>
    /// Conflict resolutions resolved by this user
    /// </summary>
    public virtual ICollection<ConflictResolution> ConflictResolutions { get; set; } = new List<ConflictResolution>();
}
