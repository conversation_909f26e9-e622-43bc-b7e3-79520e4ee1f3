using Application.ObjectValues.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.ObjectValues.Queries.GetObjectValuesByRefId;

/// <summary>
/// Query to get ObjectValues by RefId with complete metadata information
/// </summary>
public class GetObjectValuesByRefIdQuery : IRequest<Result<ObjectValuesResponseDto>>
{
    /// <summary>
    /// Reference ID that groups related ObjectValues
    /// </summary>
    public Guid RefId { get; set; }

    /// <summary>
    /// Whether to include inactive records
    /// </summary>
    public bool IncludeInactive { get; set; } = false;

    /// <summary>
    /// Whether to include only visible metadata
    /// </summary>
    public bool OnlyVisible { get; set; } = true;

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="refId">Reference ID</param>
    /// <param name="includeInactive">Include inactive records</param>
    /// <param name="onlyVisible">Include only visible metadata</param>
    public GetObjectValuesByRefIdQuery(Guid refId, bool includeInactive = false, bool onlyVisible = true)
    {
        RefId = refId;
        IncludeInactive = includeInactive;
        OnlyVisible = onlyVisible;
    }
}
