using Abstraction.Repositories;
using Application.DataTypes.DTOs;
using Domain.Entities;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.DataTypes.Commands;

/// <summary>
/// Handler for creating a new data type
/// </summary>
public class CreateDataTypeCommandHandler : IRequestHandler<CreateDataTypeCommand, Result<DataTypeDto>>
{
    private readonly IDynamicRepository<DataType> _repository;
    private readonly ILogger<CreateDataTypeCommandHandler> _logger;

    public CreateDataTypeCommandHandler(
        IDynamicRepository<DataType> repository,
        ILogger<CreateDataTypeCommandHandler> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    public async Task<Result<DataTypeDto>> Handle(CreateDataTypeCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Creating new data type: {Name}", request.Name);

            // Check if data type with same name already exists
            var existingDataTypes = await _repository.GetAllAsync();
            var existingDataType = existingDataTypes.FirstOrDefault(dt => 
                !string.IsNullOrEmpty(dt.Name) && dt.Name.Equals(request.Name, StringComparison.OrdinalIgnoreCase));

            if (existingDataType != null)
            {
                _logger.LogWarning("Data type with name '{Name}' already exists", request.Name);
                return Result<DataTypeDto>.Failure(new List<string> { $"Data type with name '{request.Name}' already exists" });
            }

            // Create new data type entity
            var dataType = request.Adapt<DataType>();
            dataType.Id = Guid.NewGuid();
            dataType.CreatedAt = DateTime.UtcNow;
            dataType.ModifiedAt = DateTime.UtcNow;

            // Save to repository
            var createdDataType = await _repository.AddAsync(dataType);

            // Map to DTO
            var dataTypeDto = createdDataType.Adapt<DataTypeDto>();

            _logger.LogInformation("Successfully created data type: {Name} with ID: {Id}", request.Name, createdDataType.Id);

            return Result<DataTypeDto>.Success(dataTypeDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while creating data type: {Name}", request.Name);
            return Result<DataTypeDto>.Failure(new List<string> { ex.Message });
        }
    }
}
