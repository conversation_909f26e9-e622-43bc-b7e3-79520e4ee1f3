using Domain.Views;
using Shared.Common;

namespace Abstraction.Database.Repositories;

/// <summary>
/// Repository interface for comprehensive entity data view
/// </summary>
public interface IComprehensiveEntityDataRepository : ITransientRepository
{
    /// <summary>
    /// Get comprehensive entity data with filters
    /// </summary>
    /// <param name="productId">Product ID filter</param>
    /// <param name="featureId">Feature ID filter</param>
    /// <param name="objectId">Object ID filter</param>
    /// <param name="searchTerm">Search term for names and descriptions</param>
    /// <param name="isActive">Filter by active status</param>
    /// <param name="onlyVisibleMetadata">Include only visible metadata</param>
    /// <param name="onlyActiveMetadata">Include only active metadata</param>
    /// <param name="pageNumber">Page number for pagination</param>
    /// <param name="pageSize">Page size for pagination</param>
    /// <param name="orderBy">Order by field</param>
    /// <param name="orderDirection">Order direction (asc/desc)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of comprehensive entity data</returns>
    Task<IEnumerable<VwComprehensiveEntityData>> GetComprehensiveEntityDataAsync(
        Guid? productId = null,
        Guid? featureId = null,
        Guid? objectId = null,
        string? searchTerm = null,
        bool? isActive = null,
        bool onlyVisibleMetadata = true,
        bool onlyActiveMetadata = true,
        int pageNumber = 1,
        int pageSize = 50,
        string? orderBy = null,
        string orderDirection = "asc",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get count of comprehensive entity data with filters
    /// </summary>
    /// <param name="productId">Product ID filter</param>
    /// <param name="featureId">Feature ID filter</param>
    /// <param name="objectId">Object ID filter</param>
    /// <param name="searchTerm">Search term for names and descriptions</param>
    /// <param name="isActive">Filter by active status</param>
    /// <param name="onlyVisibleMetadata">Include only visible metadata</param>
    /// <param name="onlyActiveMetadata">Include only active metadata</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Total count</returns>
    Task<int> GetComprehensiveEntityDataCountAsync(
        Guid? productId = null,
        Guid? featureId = null,
        Guid? objectId = null,
        string? searchTerm = null,
        bool? isActive = null,
        bool onlyVisibleMetadata = true,
        bool onlyActiveMetadata = true,
        CancellationToken cancellationToken = default);
}
