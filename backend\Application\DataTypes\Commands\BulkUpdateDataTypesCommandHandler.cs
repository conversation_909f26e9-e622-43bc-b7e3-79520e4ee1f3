using Abstraction.Repositories;
using Application.DataTypes.DTOs;
using Domain.Entities;
using FluentValidation;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.DataTypes.Commands;

/// <summary>
/// Handler for bulk update data types command
/// </summary>
public class BulkUpdateDataTypesCommandHandler : IRequestHandler<BulkUpdateDataTypesCommand, Result<BulkUpdateDataTypesResponse>>
{
    private readonly IDataTypeRepository _dataTypeRepository;
    private readonly ILogger<BulkUpdateDataTypesCommandHandler> _logger;
    private readonly IValidator<UpdateDataTypeCommand> _updateValidator;
    private readonly IValidator<CreateDataTypeCommand> _createValidator;

    public BulkUpdateDataTypesCommandHandler(
        IDataTypeRepository dataTypeRepository,
        ILogger<BulkUpdateDataTypesCommandHandler> logger,
        IValidator<UpdateDataTypeCommand> updateValidator,
        IValidator<CreateDataTypeCommand> createValidator)
    {
        _dataTypeRepository = dataTypeRepository;
        _logger = logger;
        _updateValidator = updateValidator;
        _createValidator = createValidator;
    }

    public async Task<Result<BulkUpdateDataTypesResponse>> Handle(BulkUpdateDataTypesCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Starting bulk update operation for {Count} data types", request.DataTypes.Count);

            var response = new BulkUpdateDataTypesResponse
            {
                TotalRequested = request.DataTypes.Count
            };

            // Validate all items first if requested
            if (request.ValidateBeforeUpdate)
            {
                var validationErrors = await ValidateAllItems(request.DataTypes);
                if (validationErrors.Any())
                {
                    response.Errors.AddRange(validationErrors);
                    response.Failed = validationErrors.Count;
                    response.Message = $"Validation failed for {validationErrors.Count} items";
                    return Result<BulkUpdateDataTypesResponse>.Success(response);
                }
            }

            // Process each data type
            for (int i = 0; i < request.DataTypes.Count; i++)
            {
                var updateCommand = request.DataTypes[i];
                
                try
                {
                    // Validate individual item if not validated before
                    if (!request.ValidateBeforeUpdate)
                    {
                        var validationResult = await _updateValidator.ValidateAsync(updateCommand, cancellationToken);
                        if (!validationResult.IsValid)
                        {
                            response.Errors.Add(new BulkDataTypeUpdateError
                            {
                                Index = i,
                                DataTypeId = updateCommand.Id,
                                DataTypeName = updateCommand.Name,
                                ErrorMessage = string.Join("; ", validationResult.Errors.Select(e => e.ErrorMessage)),
                                ErrorType = "Validation"
                            });
                            response.Failed++;
                            
                            if (!request.ContinueOnError)
                                break;
                            continue;
                        }
                    }

                    // Try to find existing data type
                    var existingDataType = await _dataTypeRepository.GetByIdAsync(updateCommand.Id);
                    
                    if (existingDataType == null)
                    {
                        if (request.CreateIfNotExists)
                        {
                            // Create new data type
                            var createCommand = new CreateDataTypeCommand
                            {
                                Name = updateCommand.Name,
                                DisplayName = updateCommand.DisplayName,
                                Category = updateCommand.Category,
                                UiComponent = updateCommand.UiComponent,
                                ValidationPattern = updateCommand.ValidationPattern,
                                MinLength = updateCommand.MinLength,
                                MaxLength = updateCommand.MaxLength,
                                MinValue = updateCommand.MinValue,
                                MaxValue = updateCommand.MaxValue,
                                DecimalPlaces = updateCommand.DecimalPlaces,
                                DefaultValue = updateCommand.DefaultValue,
                                IsRequired = updateCommand.IsRequired,
                                IsActive = updateCommand.IsActive,
                                AllowMultiple = updateCommand.AllowMultiple,
                                SortOrder = updateCommand.SortOrder,
                                Description = updateCommand.Description
                            };

                            // Validate create command
                            var createValidationResult = await _createValidator.ValidateAsync(createCommand, cancellationToken);
                            if (!createValidationResult.IsValid)
                            {
                                response.Errors.Add(new BulkDataTypeUpdateError
                                {
                                    Index = i,
                                    DataTypeId = updateCommand.Id,
                                    DataTypeName = updateCommand.Name,
                                    ErrorMessage = string.Join("; ", createValidationResult.Errors.Select(e => e.ErrorMessage)),
                                    ErrorType = "CreateValidation"
                                });
                                response.Failed++;
                                
                                if (!request.ContinueOnError)
                                    break;
                                continue;
                            }

                            var newDataType = createCommand.Adapt<DataType>();
                            newDataType.Id = updateCommand.Id; // Use the provided ID
                            newDataType.CreatedAt = DateTime.UtcNow;
                            newDataType.ModifiedAt = DateTime.UtcNow;

                            var createdDataType = await _dataTypeRepository.AddAsync(newDataType);
                            var createdDto = createdDataType.Adapt<DataTypeDto>();
                            
                            response.CreatedDataTypes.Add(createdDto);
                            response.Created++;

                            _logger.LogDebug("Successfully created data type: {Name} with ID: {Id}", 
                                updateCommand.Name, updateCommand.Id);
                        }
                        else
                        {
                            response.Errors.Add(new BulkDataTypeUpdateError
                            {
                                Index = i,
                                DataTypeId = updateCommand.Id,
                                DataTypeName = updateCommand.Name,
                                ErrorMessage = $"Data type with ID '{updateCommand.Id}' not found",
                                ErrorType = "NotFound"
                            });
                            response.NotFound++;
                            
                            if (!request.ContinueOnError)
                                break;
                        }
                    }
                    else
                    {
                        // Update existing data type
                        updateCommand.Adapt(existingDataType);
                        existingDataType.ModifiedAt = DateTime.UtcNow;

                        var updatedDataType = await _dataTypeRepository.UpdateAsync(existingDataType);
                        var updatedDto = updatedDataType.Adapt<DataTypeDto>();
                        
                        response.UpdatedDataTypes.Add(updatedDto);
                        response.SuccessfullyUpdated++;

                        _logger.LogDebug("Successfully updated data type: {Name} with ID: {Id}", 
                            updateCommand.Name, updateCommand.Id);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error updating data type at index {Index}: {Id}", i, updateCommand.Id);
                    
                    response.Errors.Add(new BulkDataTypeUpdateError
                    {
                        Index = i,
                        DataTypeId = updateCommand.Id,
                        DataTypeName = updateCommand.Name,
                        ErrorMessage = ex.Message,
                        ErrorType = "Database"
                    });
                    response.Failed++;

                    if (!request.ContinueOnError)
                        break;
                }
            }

            // Calculate final counts
            response.Failed = response.Errors.Count(e => e.ErrorType != "NotFound");

            // Generate summary message
            response.Message = GenerateSummaryMessage(response);

            _logger.LogInformation("Bulk update operation completed. Updated: {Updated}, Created: {Created}, Failed: {Failed}, NotFound: {NotFound}",
                response.SuccessfullyUpdated, response.Created, response.Failed, response.NotFound);

            return Result<BulkUpdateDataTypesResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during bulk update operation");
            return Result<BulkUpdateDataTypesResponse>.Failure($"Bulk update operation failed: {ex.Message}");
        }
    }

    private async Task<List<BulkDataTypeUpdateError>> ValidateAllItems(List<UpdateDataTypeCommand> dataTypes)
    {
        var errors = new List<BulkDataTypeUpdateError>();
        
        for (int i = 0; i < dataTypes.Count; i++)
        {
            var validationResult = await _updateValidator.ValidateAsync(dataTypes[i]);
            if (!validationResult.IsValid)
            {
                errors.Add(new BulkDataTypeUpdateError
                {
                    Index = i,
                    DataTypeId = dataTypes[i].Id,
                    DataTypeName = dataTypes[i].Name,
                    ErrorMessage = string.Join("; ", validationResult.Errors.Select(e => e.ErrorMessage)),
                    ErrorType = "Validation"
                });
            }
        }

        return errors;
    }

    private static string GenerateSummaryMessage(BulkUpdateDataTypesResponse response)
    {
        var parts = new List<string>();
        
        if (response.SuccessfullyUpdated > 0)
            parts.Add($"{response.SuccessfullyUpdated} updated");
        
        if (response.Created > 0)
            parts.Add($"{response.Created} created");
        
        if (response.Failed > 0)
            parts.Add($"{response.Failed} failed");
        
        if (response.NotFound > 0)
            parts.Add($"{response.NotFound} not found");

        return $"Bulk update completed: {string.Join(", ", parts)}";
    }
}
