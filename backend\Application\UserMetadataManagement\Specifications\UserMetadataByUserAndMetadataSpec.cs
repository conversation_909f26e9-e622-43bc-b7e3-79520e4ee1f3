using Ardalis.Specification;
using Domain.Entities;

namespace Application.UserMetadataManagement.Specifications;

/// <summary>
/// Specification to get UserMetadata by user ID and metadata ID
/// </summary>
public class UserMetadataByUserAndMetadataSpec : Specification<Domain.Entities.UserMetadata>, ISingleResultSpecification<Domain.Entities.UserMetadata>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public UserMetadataByUserAndMetadataSpec(Guid userId, Guid metadataId)
    {
        Query.Where(um => um.UserId == userId && 
                          um.MetadataId == metadataId && 
                          !um.IsDeleted);
    }
}
