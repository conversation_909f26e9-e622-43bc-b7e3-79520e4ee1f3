using Abstraction.Identity;
using Abstraction.Identity.Dtos;
using Application.Identity.Commands;
using Application.Identity.DTOs;
using Application.Identity.Queries.GetUserRoles;
using Application.Identity.Queries.GetUsersInRole;
using Application.Identity.Queries.GetUsersWithPagination;
using Shared.Common.Response;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Infrastructure.OpenApi;

namespace Web.Host.Controllers.Identity;

/// <summary>
/// Controller for user operations
/// </summary>
public class UsersController : BaseApiController
{
    /// <summary>
    /// Identity service
    /// </summary>
    private readonly IIdentityService _identityService;

    /// <summary>
    /// Mediator
    /// </summary>
    private readonly IMediator _mediator;

    /// <summary>
    /// Constructor
    /// </summary>
    public UsersController(IIdentityService identityService, IMediator mediator)
    {
        _identityService = identityService;
        _mediator = mediator;
    }

    /// <summary>
    /// Get all users
    /// </summary>
    [HttpGet]
    [TenantIdHeader]
    public async Task<ActionResult<Result<List<UserDetailsDto>>>> GetAllAsync()
    {
        try
        {
            var users = await _identityService.GetUsersAsync();
            return Ok(Result<List<UserDetailsDto>>.Success(users));
        }
        catch (Exception ex)
        {
            return BadRequest(Result<List<UserDetailsDto>>.Failure(ex.Message));
        }
    }

    /// <summary>
    /// Get paginated users
    /// </summary>
    [HttpGet("paginated")]
    [TenantIdHeader]
    public async Task<ActionResult<PagedResponse<UserDetailsDto, object>>> GetPaginatedAsync(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? searchString = null,
        [FromQuery] bool includeInactive = false)
    {
        try
        {
            var query = new GetUsersWithPaginationQuery
            {
                PageNumber = pageNumber < 1 ? 1 : pageNumber,
                PageSize = pageSize < 1 ? 10 : pageSize,
                SearchString = searchString,
                IncludeInactive = includeInactive
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return BadRequest(PagedResponse<UserDetailsDto, object>.Failure($"Error retrieving paginated users: {ex.Message}"));
        }
    }

    /// <summary>
    /// Get a user by ID
    /// </summary>
    [HttpGet("{id:guid}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<UserDetailsDto>>> GetByIdAsync(Guid id)
    {
        try
        {
            var user = await _identityService.GetUserAsync(id.ToString());
            if (user == null)
            {
                return NotFound(Result<UserDetailsDto>.Failure($"User with ID {id} not found."));
            }

            return Ok(Result<UserDetailsDto>.Success(user));
        }
        catch (Exception ex)
        {
            return BadRequest(Result<UserDetailsDto>.Failure(ex.Message));
        }
    }

    /// <summary>
    /// Register a new user
    /// </summary>
    [HttpPost]
    [AllowAnonymous]
    [TenantIdHeader]
    public async Task<ActionResult<Result<string>>> RegisterAsync(RegisterUserRequest request)
    {
        try
        {
            var origin = Request.Headers["origin"].ToString();
            var result = await _identityService.RegisterUserAsync(request, origin);
            return Ok(Result<string>.Success(result));
        }
        catch (Exception ex)
        {
            return BadRequest(Result<string>.Failure(ex.Message));
        }
    }

    /// <summary>
    /// Update a user
    /// </summary>
    [HttpPut("{id:guid}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<string>>> UpdateAsync(UpdateUserRequest request, Guid id)
    {
        try
        {
            var result = await _identityService.UpdateUserAsync(request, id.ToString());
            return Ok(Result<string>.Success(result));
        }
        catch (Exception ex)
        {
            return BadRequest(Result<string>.Failure(ex.Message));
        }
    }

    /// <summary>
    /// Change password
    /// </summary>
    [HttpPut("{id:guid}/change-password")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<string>>> ChangePasswordAsync(ChangePasswordRequest request, Guid id)
    {
        try
        {
            var result = await _identityService.ChangePasswordAsync(request, id.ToString());
            return Ok(Result<string>.Success(result));
        }
        catch (Exception ex)
        {
            return BadRequest(Result<string>.Failure(ex.Message));
        }
    }

    /// <summary>
    /// Forgot password
    /// </summary>
    [HttpPost("forgot-password")]
    [AllowAnonymous]
    [TenantIdHeader]
    public async Task<ActionResult<Result<string>>> ForgotPasswordAsync(ForgotPasswordRequest request)
    {
        try
        {
            var origin = Request.Headers["origin"].ToString();
            var result = await _identityService.ForgotPasswordAsync(request, origin);
            return Ok(Result<string>.Success(result));
        }
        catch (Exception ex)
        {
            return BadRequest(Result<string>.Failure(ex.Message));
        }
    }

    /// <summary>
    /// Reset password
    /// </summary>
    [HttpPost("reset-password")]
    [AllowAnonymous]
    [TenantIdHeader]
    public async Task<ActionResult<Result<string>>> ResetPasswordAsync(ResetPasswordRequest request)
    {
        try
        {
            var result = await _identityService.ResetPasswordAsync(request);
            return Ok(Result<string>.Success(result));
        }
        catch (Exception ex)
        {
            return BadRequest(Result<string>.Failure(ex.Message));
        }
    }

    /// <summary>
    /// Create a new user
    /// </summary>
    [HttpPost("create")]
    [TenantIdHeader]
    public async Task<ActionResult<ApiResponse<UserDto>>> CreateUserAsync(CreateUserCommand command)
    {
        try
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return BadRequest(new ApiResponse<UserDto>(false, $"Error creating user: {ex.Message}", default!));
        }
    }

    /// <summary>
    /// Create multiple users in bulk
    /// </summary>
    [HttpPost("bulk-create")]
    [TenantIdHeader]
    public async Task<ActionResult<ApiResponse<BulkCreateUsersResponse>>> BulkCreateUsersAsync(BulkCreateUsersCommand command)
    {
        try
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            var errorResponse = new BulkCreateUsersResponse
            {
                TotalRequested = command.Users.Count,
                Failed = command.Users.Count,
                Message = $"Bulk user creation failed: {ex.Message}"
            };
            return BadRequest(new ApiResponse<BulkCreateUsersResponse>(false, errorResponse.Message, errorResponse));
        }
    }

    /// <summary>
    /// Update roles for multiple users in bulk
    /// </summary>
    [HttpPost("bulk-update-roles")]
    [TenantIdHeader]
    public async Task<ActionResult<ApiResponse<BulkUpdateUserRolesResponse>>> BulkUpdateUserRolesAsync(BulkUpdateUserRolesCommand command)
    {
        try
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            var errorResponse = new BulkUpdateUserRolesResponse
            {
                TotalRequested = command.UserRoleUpdates.Count,
                Failed = command.UserRoleUpdates.Count,
                Message = $"Bulk user role update failed: {ex.Message}"
            };
            return BadRequest(new ApiResponse<BulkUpdateUserRolesResponse>(false, errorResponse.Message, errorResponse));
        }
    }

    /// <summary>
    /// Get user roles
    /// </summary>
    [HttpGet("{userId:guid}/roles")]
    [TenantIdHeader]
    public async Task<ActionResult<ApiResponse<List<UserRoleDto>>>> GetUserRolesAsync(Guid userId)
    {
        try
        {
            var query = new GetUserRolesQuery { UserId = userId.ToString() };
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return BadRequest(new ApiResponse<List<UserRoleDto>>(false, $"Error retrieving user roles: {ex.Message}", new List<UserRoleDto>()));
        }
    }

    /// <summary>
    /// Assign roles to user
    /// </summary>
    [HttpPost("{userId:guid}/roles/assign")]
    [TenantIdHeader]
    public async Task<ActionResult<ApiResponse<string>>> AssignRolesToUserAsync(Guid userId, [FromBody] List<string> roleNames)
    {
        try
        {
            var command = new AssignRolesToUserCommand { UserId = userId.ToString(), RoleNames = roleNames };
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return BadRequest(new ApiResponse<string>(false, $"Error assigning roles: {ex.Message}", string.Empty));
        }
    }

    /// <summary>
    /// Remove roles from user
    /// </summary>
    [HttpPost("{userId:guid}/roles/remove")]
    [TenantIdHeader]
    public async Task<ActionResult<ApiResponse<string>>> RemoveRolesFromUserAsync(Guid userId, [FromBody] List<string> roleNames)
    {
        try
        {
            var command = new RemoveRolesFromUserCommand { UserId = userId.ToString(), RoleNames = roleNames };
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return BadRequest(new ApiResponse<string>(false, $"Error removing roles: {ex.Message}", string.Empty));
        }
    }

    /// <summary>
    /// Update user roles
    /// </summary>
    [HttpPut("{userId:guid}/roles")]
    [TenantIdHeader]
    public async Task<ActionResult<ApiResponse<string>>> UpdateUserRolesAsync(Guid userId, [FromBody] List<UserRoleDto> userRoles)
    {
        try
        {
            var command = new UpdateUserRolesCommand { UserId = userId.ToString(), UserRoles = userRoles };
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return BadRequest(new ApiResponse<string>(false, $"Error updating user roles: {ex.Message}", string.Empty));
        }
    }

    /// <summary>
    /// Get users in a specific role
    /// </summary>
    [HttpGet("roles/{roleName}/users")]
    [TenantIdHeader]
    public async Task<ActionResult<ApiResponse<List<UserDto>>>> GetUsersInRoleAsync(string roleName)
    {
        try
        {
            var query = new GetUsersInRoleQuery { RoleName = roleName };
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return BadRequest(new ApiResponse<List<UserDto>>(false, $"Error retrieving users in role: {ex.Message}", new List<UserDto>()));
        }
    }
}
