using Application.Products.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Products.Queries;

/// <summary>
/// Get products query
/// </summary>
public class GetProductsQuery : IRequest<PaginatedResult<ProductDto>>
{
    /// <summary>
    /// Page number
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Search term
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Filter by active status
    /// </summary>
    public bool? IsActive { get; set; }
}
