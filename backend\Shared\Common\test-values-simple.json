{"operationType": 5, "includeFeatures": true, "includeObjects": true, "includeMetadata": true, "productData": [{"name": "Test Product for Values", "description": "Simple test product to verify values processing", "version": "1.0.0", "isActive": true, "features": [{"name": "Test Feature", "description": "Simple test feature", "isDefault": true, "isActive": true, "metadata": [{"metadataId": "test-feature-priority", "isUnique": false, "isActive": true}], "values": [{"metadataKey": "test-feature-priority", "value": "High Priority", "refId": "test-ref-001"}], "objects": [{"name": "Test Object", "description": "Simple test object", "isActive": true, "metadata": [{"metadataId": "test-object-status", "isUnique": false, "isActive": true}], "values": [{"metadataKey": "test-object-status", "value": "Active Status", "refId": "test-obj-001"}]}]}], "metadata": [{"metadataId": "test-product-category", "isUnique": false, "isActive": true}], "values": [{"metadataKey": "test-product-category", "value": "Test Category", "refId": "test-prod-001"}]}]}