using Abstraction.Common;
using Application.ObjectValues.DTOs;
using Application.ObjectValues.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.ObjectValues.Commands.UpsertObjectValues;

/// <summary>
/// Handler for UpsertObjectValuesCommand
/// </summary>
public class UpsertObjectValuesCommandHandler : IRequestHandler<UpsertObjectValuesCommand, Result<UpsertObjectValuesResponseDto>>
{
    private readonly IObjectValuesRepository _objectValuesRepository;
    private readonly ICurrentUser _currentUserService;
    private readonly ILogger<UpsertObjectValuesCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="objectValuesRepository">ObjectValues repository</param>
    /// <param name="currentUserService">Current user service</param>
    /// <param name="logger">Logger</param>
    public UpsertObjectValuesCommandHandler(
        IObjectValuesRepository objectValuesRepository,
        ICurrentUser currentUserService,
        ILogger<UpsertObjectValuesCommandHandler> logger)
    {
        _objectValuesRepository = objectValuesRepository;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    /// <param name="request">Command request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Upsert response or error</returns>
    public async Task<Result<UpsertObjectValuesResponseDto>> Handle(
        UpsertObjectValuesCommand request, 
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Upserting ObjectValues for RefId {RefId} and ObjectId {ObjectId} with {ValueCount} values", 
                request.RefId, request.ObjectId, request.Values.Count);

            var tenantId = _currentUserService.GetTenant();
            var userId = _currentUserService.GetUserId();
            
            if (string.IsNullOrEmpty(tenantId))
            {
                _logger.LogWarning("TenantId is null or empty for RefId {RefId}", request.RefId);
                return Result<UpsertObjectValuesResponseDto>.Failure("TenantId is required");
            }
            
            if (string.IsNullOrEmpty(userId))
            {
                _logger.LogWarning("UserId is null or empty for RefId {RefId}", request.RefId);
                return Result<UpsertObjectValuesResponseDto>.Failure("UserId is required");
            }

            // Validate input
            var validationResult = ValidateRequest(request);
            if (!validationResult.Succeeded)
            {
                return Result<UpsertObjectValuesResponseDto>.Failure(validationResult.Message ?? "Validation failed");
            }

            // Verify object exists
            var objectEntity = await _objectValuesRepository.GetObjectByIdAsync(
                request.ObjectId, 
                tenantId, 
                cancellationToken);
                
            if (objectEntity == null)
            {
                _logger.LogWarning("Object {ObjectId} not found in tenant {TenantId}", request.ObjectId, tenantId);
                return Result<UpsertObjectValuesResponseDto>.Failure($"Object {request.ObjectId} not found");
            }

            // Create upsert request DTO
            var upsertRequest = new UpsertObjectValuesRequestDto
            {
                RefId = request.RefId,
                ObjectId = request.ObjectId,
                Values = request.Values,
                AutoCreateMetadata = request.AutoCreateMetadata,
                StrictValidation = request.StrictValidation && !request.SkipValidation
            };

            // Perform upsert operation
            var result = await _objectValuesRepository.UpsertAsync(
                upsertRequest,
                tenantId,
                userId,
                cancellationToken);

            _logger.LogInformation("Successfully upserted ObjectValues for RefId {RefId}: Created={Created}, Updated={Updated}, MetadataCreated={MetadataCreated}", 
                request.RefId, result.CreatedCount, result.UpdatedCount, result.MetadataCreatedCount);

            return Result<UpsertObjectValuesResponseDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while upserting ObjectValues for RefId {RefId}", request.RefId);
            return Result<UpsertObjectValuesResponseDto>.Failure("An error occurred while upserting ObjectValues");
        }
    }

    /// <summary>
    /// Validate the upsert request
    /// </summary>
    /// <param name="request">Command request</param>
    /// <returns>Validation result</returns>
    private static Result<bool> ValidateRequest(UpsertObjectValuesCommand request)
    {
        if (request.RefId == Guid.Empty)
        {
            return Result<bool>.Failure("RefId cannot be empty");
        }

        if (request.ObjectId == Guid.Empty)
        {
            return Result<bool>.Failure("ObjectId cannot be empty");
        }

        if (request.Values == null || request.Values.Count == 0)
        {
            return Result<bool>.Failure("Values cannot be null or empty");
        }

        // Validate metadata keys
        foreach (var kvp in request.Values)
        {
            if (string.IsNullOrWhiteSpace(kvp.Key))
            {
                return Result<bool>.Failure("MetadataKey cannot be null or empty");
            }

            if (kvp.Key.Length > 100)
            {
                return Result<bool>.Failure($"MetadataKey '{kvp.Key}' is too long (max 100 characters)");
            }

            // Value can be null or empty (for clearing values)
            if (kvp.Value != null && kvp.Value.Length > 4000)
            {
                return Result<bool>.Failure($"Value for '{kvp.Key}' is too long (max 4000 characters)");
            }
        }

        return Result<bool>.Success(true);
    }
}
