using Ardalis.Specification;
using Domain.Entities;

namespace Application.ObjectValues.Specifications;

/// <summary>
/// Specification to get ObjectValues by RefId and ObjectId for upsert operations
/// </summary>
public class ObjectValuesByRefIdAndObjectIdSpec : Specification<ObjectValue>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ObjectValuesByRefIdAndObjectIdSpec(Guid refId, Guid objectId, string tenantId)
    {
        Query.Where(ov => ov.RefId == refId &&
                         ov.ObjectMetadata.ObjectId == objectId &&
                         !ov.IsDeleted);

        // Include metadata for updates
        Query.Include(ov => ov.ObjectMetadata)
             .ThenInclude(om => om.Metadata);

        Query.Include(ov => ov.ObjectMetadata)
             .ThenInclude(om => om.Object);

        // Active filters (tenant isolation handled by multi-tenant framework)
        Query.Where(ov => !ov.ObjectMetadata.Object.IsDeleted);

        // Order by metadata key for consistent processing
        Query.OrderBy(ov => ov.ObjectMetadata.Metadata.MetadataKey);
    }
}
