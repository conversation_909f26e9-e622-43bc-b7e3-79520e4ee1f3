using Ardalis.Specification;
using Domain.Entities;

namespace Application.ObjectValues.Specifications;

/// <summary>
/// Specification to get Metadata by MetadataKey
/// </summary>
public class MetadataByKeySpec : Specification<Metadata>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public MetadataByKeySpec(string metadataKey)
    {
        Query.Where(m => m.MetadataKey == metadataKey);

        // Include data type
        Query.Include(m => m.DataType);

        // Take only one record
        Query.Take(1);
    }
}
