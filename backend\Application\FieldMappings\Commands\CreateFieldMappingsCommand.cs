using Application.FieldMappings.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.FieldMappings.Commands;

/// <summary>
/// Create multiple field mappings command
/// </summary>
public class CreateFieldMappingsCommand : IRequest<Result<List<ViewFieldMappingDto>>>
{
    /// <summary>
    /// List of field mappings to create
    /// </summary>
    public List<CreateFieldMappingRequest> FieldMappings { get; set; } = new();
}

/// <summary>
/// Create field mapping request for bulk operation
/// </summary>
public class CreateFieldMappingRequest
{
    /// <summary>
    /// API name for this field mapping
    /// </summary>
    public string? ApiName { get; set; }

    /// <summary>
    /// Name of the field in the incoming JSON
    /// </summary>
    public string SourceField { get; set; } = string.Empty;

    /// <summary>
    /// Data type in the source
    /// </summary>
    public string SourceType { get; set; } = string.Empty;

    /// <summary>
    /// Object metadata ID this mapping targets (optional)
    /// </summary>
    public Guid? ObjectMetadataId { get; set; }

    /// <summary>
    /// User ID this mapping is associated with (optional)
    /// </summary>
    public Guid? UserId { get; set; }

    /// <summary>
    /// Role ID this mapping is associated with (optional)
    /// </summary>
    public Guid? RoleId { get; set; }

    /// <summary>
    /// Optional: which object this mapping is for
    /// </summary>
    public string? TargetObjectName { get; set; }

    /// <summary>
    /// Additional notes or mapping logic
    /// </summary>
    public string? Notes { get; set; }
}
