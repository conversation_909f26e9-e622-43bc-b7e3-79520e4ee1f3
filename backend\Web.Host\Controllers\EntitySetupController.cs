using Application.ComprehensiveEntityData.DTOs;
using Application.ComprehensiveEntityData.Queries;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;

namespace Web.Host.Controllers;

/// <summary>
/// Comprehensive entity data controller
/// </summary>
[Route("api/[controller]")]
public class EntitySetupController : BaseApiController
{
    /// <summary>
    /// Get comprehensive entity data with hierarchical structure
    /// </summary>
    /// <param name="query">Query parameters for filtering and pagination</param>
    /// <returns>Comprehensive entity data in nested JSON format</returns>
    /// <response code="200">Returns the comprehensive entity data</response>
    /// <response code="400">If the request is invalid</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet]
    [TenantIdHeader]
    [ProducesResponseType(typeof(Result<ComprehensiveEntityDataResponseDto>), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<Result<ComprehensiveEntityDataResponseDto>>> GetComprehensiveEntityData([FromQuery] GetComprehensiveEntityDataQuery query)
    {
        var result = await Mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Get comprehensive entity data for a specific product
    /// </summary>
    /// <param name="productId">Product ID</param>
    /// <param name="onlyVisibleMetadata">Include only visible metadata</param>
    /// <param name="onlyActiveMetadata">Include only active metadata</param>
    /// <returns>Comprehensive entity data for the specified product</returns>
    /// <response code="200">Returns the comprehensive entity data for the product</response>
    /// <response code="404">If the product is not found</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet("product/{productId}")]
    [TenantIdHeader]
    [ProducesResponseType(typeof(Result<ComprehensiveEntityDataResponseDto>), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<Result<ComprehensiveEntityDataResponseDto>>> GetComprehensiveEntityDataByProduct(
        Guid productId,
        [FromQuery] bool onlyVisibleMetadata = true,
        [FromQuery] bool onlyActiveMetadata = true)
    {
        var query = new GetComprehensiveEntityDataQuery
        {
            ProductId = productId,
            OnlyVisibleMetadata = onlyVisibleMetadata,
            OnlyActiveMetadata = onlyActiveMetadata,
            PageSize = 1000 // Large page size for single product
        };

        var result = await Mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Get comprehensive entity data for a specific feature
    /// </summary>
    /// <param name="featureId">Feature ID</param>
    /// <param name="onlyVisibleMetadata">Include only visible metadata</param>
    /// <param name="onlyActiveMetadata">Include only active metadata</param>
    /// <returns>Comprehensive entity data for the specified feature</returns>
    /// <response code="200">Returns the comprehensive entity data for the feature</response>
    /// <response code="404">If the feature is not found</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet("feature/{featureId}")]
    [TenantIdHeader]
    [ProducesResponseType(typeof(Result<ComprehensiveEntityDataResponseDto>), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<Result<ComprehensiveEntityDataResponseDto>>> GetComprehensiveEntityDataByFeature(
        Guid featureId,
        [FromQuery] bool onlyVisibleMetadata = true,
        [FromQuery] bool onlyActiveMetadata = true)
    {
        var query = new GetComprehensiveEntityDataQuery
        {
            FeatureId = featureId,
            OnlyVisibleMetadata = onlyVisibleMetadata,
            OnlyActiveMetadata = onlyActiveMetadata,
            PageSize = 1000 // Large page size for single feature
        };

        var result = await Mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Get comprehensive entity data for a specific object
    /// </summary>
    /// <param name="objectId">Object ID</param>
    /// <param name="onlyVisibleMetadata">Include only visible metadata</param>
    /// <param name="onlyActiveMetadata">Include only active metadata</param>
    /// <returns>Comprehensive entity data for the specified object</returns>
    /// <response code="200">Returns the comprehensive entity data for the object</response>
    /// <response code="404">If the object is not found</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet("object/{objectId}")]
    [TenantIdHeader]
    [ProducesResponseType(typeof(Result<ComprehensiveEntityDataResponseDto>), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<Result<ComprehensiveEntityDataResponseDto>>> GetComprehensiveEntityDataByObject(
        Guid objectId,
        [FromQuery] bool onlyVisibleMetadata = true,
        [FromQuery] bool onlyActiveMetadata = true)
    {
        var query = new GetComprehensiveEntityDataQuery
        {
            ObjectId = objectId,
            OnlyVisibleMetadata = onlyVisibleMetadata,
            OnlyActiveMetadata = onlyActiveMetadata,
            PageSize = 1000 // Large page size for single object
        };

        var result = await Mediator.Send(query);
        return Ok(result);
    }
}
