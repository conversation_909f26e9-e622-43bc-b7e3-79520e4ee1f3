using Application.Integrations.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Integrations.Commands;

/// <summary>
/// Create integration command
/// </summary>
public class CreateIntegrationCommand : IRequest<Result<IntegrationDto>>
{
    /// <summary>
    /// Product ID this integration belongs to
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Integration name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Authentication type
    /// </summary>
    public string AuthType { get; set; } = string.Empty;

    /// <summary>
    /// Authentication configuration stored as JSON
    /// </summary>
    public string AuthConfig { get; set; } = string.Empty;

    /// <summary>
    /// Whether the integration is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Sync frequency for automatic synchronization
    /// </summary>
    public TimeSpan? SyncFrequency { get; set; }
}
