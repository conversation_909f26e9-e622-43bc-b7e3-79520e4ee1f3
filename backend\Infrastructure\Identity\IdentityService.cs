using Abstraction.Identity;
using Abstraction.Identity.Dtos;
using Domain.Entities;
using Mapster;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Shared.Common.Response;

namespace Infrastructure.Identity;

/// <summary>
/// Implementation of identity service
/// </summary>
public class IdentityService : IIdentityService
{
    /// <summary>
    /// User manager
    /// </summary>
    private readonly UserManager<User> _userManager;

    /// <summary>
    /// Role manager
    /// </summary>
    private readonly RoleManager<Role> _roleManager;

    /// <summary>
    /// Constructor
    /// </summary>
    public IdentityService(UserManager<User> userManager, RoleManager<Role> roleManager)
    {
        _userManager = userManager;
        _roleManager = roleManager;
    }

    /// <summary>
    /// Get a user by ID
    /// </summary>
    public async Task<UserDetailsDto?> GetUserAsync(string userId)
    {
        var user = await _userManager.FindByIdAsync(userId);
        return user?.Adapt<UserDetailsDto>();
    }

    /// <summary>
    /// Get a list of users
    /// </summary>
    public async Task<List<UserDetailsDto>> GetUsersAsync()
    {
        var users = await _userManager.Users.ToListAsync();
        return users.Adapt<List<UserDetailsDto>>();
    }

    /// <summary>
    /// Get a list of users by role
    /// </summary>
    public async Task<List<UserDetailsDto>> GetUsersInRoleAsync(string roleName)
    {
        var users = await _userManager.GetUsersInRoleAsync(roleName);
        return users.Adapt<List<UserDetailsDto>>();
    }

    /// <summary>
    /// Check if a user is in a role
    /// </summary>
    public async Task<bool> IsInRoleAsync(string userId, string role)
    {
        var user = await _userManager.FindByIdAsync(userId);
        return user != null && await _userManager.IsInRoleAsync(user, role);
    }

    /// <summary>
    /// Get a user's roles
    /// </summary>
    public async Task<List<string>> GetUserRolesAsync(string userId)
    {
        var user = await _userManager.FindByIdAsync(userId);
        if (user == null)
        {
            return new List<string>();
        }

        var roles = await _userManager.GetRolesAsync(user);
        return roles.ToList();
    }

    /// <summary>
    /// Register a new user
    /// </summary>
    public async Task<string> RegisterUserAsync(RegisterUserRequest request, string origin)
    {
        var user = await _userManager.FindByEmailAsync(request.Email);
        if (user != null)
        {
            throw new Exception($"User with email {request.Email} already exists.");
        }

        user = await _userManager.FindByNameAsync(request.UserName);
        if (user != null)
        {
            throw new Exception($"User with username {request.UserName} already exists.");
        }

        user = new User
        {
            Email = request.Email,
            UserName = request.UserName,
            PhoneNumber = request.PhoneNumber,
            EmailConfirmed = true,
            PhoneNumberConfirmed = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };

        var result = await _userManager.CreateAsync(user, request.Password);
        if (!result.Succeeded)
        {
            throw new Exception($"Failed to create user: {string.Join(", ", result.Errors.Select(e => e.Description))}");
        }

        return $"User {request.UserName} registered.";
    }

    /// <summary>
    /// Confirm email
    /// </summary>
    public async Task<string> ConfirmEmailAsync(string userId, string code)
    {
        var user = await _userManager.FindByIdAsync(userId);
        if (user == null)
        {
            throw new Exception($"User with ID {userId} not found.");
        }

        var result = await _userManager.ConfirmEmailAsync(user, code);
        if (!result.Succeeded)
        {
            throw new Exception($"Failed to confirm email: {string.Join(", ", result.Errors.Select(e => e.Description))}");
        }

        return $"Email confirmed for user {user.UserName}.";
    }

    /// <summary>
    /// Confirm phone number
    /// </summary>
    public async Task<string> ConfirmPhoneNumberAsync(string userId, string code)
    {
        var user = await _userManager.FindByIdAsync(userId);
        if (user == null)
        {
            throw new Exception($"User with ID {userId} not found.");
        }

        var result = await _userManager.ChangePhoneNumberAsync(user, user.PhoneNumber, code);
        if (!result.Succeeded)
        {
            throw new Exception($"Failed to confirm phone number: {string.Join(", ", result.Errors.Select(e => e.Description))}");
        }

        return $"Phone number confirmed for user {user.UserName}.";
    }

    /// <summary>
    /// Forgot password
    /// </summary>
    public async Task<string> ForgotPasswordAsync(ForgotPasswordRequest request, string origin)
    {
        var user = await _userManager.FindByEmailAsync(request.Email);
        if (user == null)
        {
            throw new Exception($"User with email {request.Email} not found.");
        }

        // Generate password reset token
        var token = await _userManager.GeneratePasswordResetTokenAsync(user);

        // TODO: Send email with reset link

        return $"Password reset token generated for user {user.UserName}.";
    }

    /// <summary>
    /// Reset password
    /// </summary>
    public async Task<string> ResetPasswordAsync(ResetPasswordRequest request)
    {
        var user = await _userManager.FindByEmailAsync(request.Email);
        if (user == null)
        {
            throw new Exception($"User with email {request.Email} not found.");
        }

        var result = await _userManager.ResetPasswordAsync(user, request.Token, request.Password);
        if (!result.Succeeded)
        {
            throw new Exception($"Failed to reset password: {string.Join(", ", result.Errors.Select(e => e.Description))}");
        }

        return $"Password reset for user {user.UserName}.";
    }

    /// <summary>
    /// Update a user
    /// </summary>
    public async Task<string> UpdateUserAsync(UpdateUserRequest request, string userId)
    {
        var user = await _userManager.FindByIdAsync(userId);
        if (user == null)
        {
            throw new Exception($"User with ID {userId} not found.");
        }

        user.FirstName = request.FirstName;
        user.LastName = request.LastName;
        user.PhoneNumber = request.PhoneNumber;
        user.Email = request.Email;
        user.ModifiedAt = DateTime.UtcNow;

        var result = await _userManager.UpdateAsync(user);
        if (!result.Succeeded)
        {
            throw new Exception($"Failed to update user: {string.Join(", ", result.Errors.Select(e => e.Description))}");
        }

        return $"User {user.UserName} updated.";
    }

    /// <summary>
    /// Change password
    /// </summary>
    public async Task<string> ChangePasswordAsync(ChangePasswordRequest request, string userId)
    {
        var user = await _userManager.FindByIdAsync(userId);
        if (user == null)
        {
            throw new Exception($"User with ID {userId} not found.");
        }

        var result = await _userManager.ChangePasswordAsync(user, request.CurrentPassword, request.NewPassword);
        if (!result.Succeeded)
        {
            throw new Exception($"Failed to change password: {string.Join(", ", result.Errors.Select(e => e.Description))}");
        }

        return $"Password changed for user {user.UserName}.";
    }

    /// <summary>
    /// Get a user by ID (alternative method name for consistency)
    /// </summary>
    public async Task<UserDetailsDto?> GetUserByIdAsync(string userId)
    {
        return await GetUserAsync(userId);
    }

    /// <summary>
    /// Get a user by email
    /// </summary>
    public async Task<UserDetailsDto?> GetUserByEmailAsync(string email)
    {
        var user = await _userManager.FindByEmailAsync(email);
        if (user == null) return null;

        var userDto = user.Adapt<UserDetailsDto>();

        // Get user roles
        var roles = await _userManager.GetRolesAsync(user);
        userDto.Roles = roles.ToList();

        return userDto;
    }

    /// <summary>
    /// Get a user's roles with detailed information
    /// </summary>
    public async Task<List<UserRoleDetailsDto>> GetUserRolesDetailedAsync(string userId)
    {
        var user = await _userManager.FindByIdAsync(userId);
        if (user == null)
        {
            return new List<UserRoleDetailsDto>();
        }

        var userRoles = new List<UserRoleDetailsDto>();
        var roles = await _roleManager.Roles.Where(r => !r.IsDeleted).ToListAsync();

        foreach (var role in roles)
        {
            userRoles.Add(new UserRoleDetailsDto
            {
                RoleId = role.Id,
                RoleName = role.Name,
                Description = role.Description,
                Enabled = await _userManager.IsInRoleAsync(user, role.Name!)
            });
        }

        return userRoles;
    }

    /// <summary>
    /// Create a new user
    /// </summary>
    public async Task<string> CreateUserAsync(CreateUserRequestDto request)
    {
        // Check if user already exists
        var existingUser = await _userManager.FindByEmailAsync(request.Email);
        if (existingUser != null)
        {
            throw new Exception($"User with email {request.Email} already exists.");
        }

        existingUser = await _userManager.FindByNameAsync(request.UserName);
        if (existingUser != null)
        {
            throw new Exception($"User with username {request.UserName} already exists.");
        }

        // Set display name if not provided
        if (string.IsNullOrWhiteSpace(request.DisplayName))
        {
            request.DisplayName = $"{request.FirstName} {request.LastName}".Trim();
        }

        var user = new User
        {
            Email = request.Email,
            UserName = request.UserName,
            FirstName = request.FirstName,
            LastName = request.LastName,
            PhoneNumber = request.PhoneNumber,
            IsActive = request.IsActive,
            ExternalUserId = request.ExternalUserId,
            EmailConfirmed = true,
            PhoneNumberConfirmed = !string.IsNullOrEmpty(request.PhoneNumber),
            CreatedAt = DateTime.UtcNow
        };

        var result = await _userManager.CreateAsync(user, request.Password);
        if (!result.Succeeded)
        {
            throw new Exception($"Failed to create user: {string.Join(", ", result.Errors.Select(e => e.Description))}");
        }

        // Assign roles to user
        if (request.Roles != null && request.Roles.Any())
        {
            foreach (var roleName in request.Roles)
            {
                // Check if role exists, create if it doesn't
                if (!await _roleManager.RoleExistsAsync(roleName))
                {
                    var role = new Role
                    {
                        Name = roleName,
                        CreatedAt = DateTime.UtcNow
                    };
                    await _roleManager.CreateAsync(role);
                }

                await _userManager.AddToRoleAsync(user, roleName);
            }
        }
        return user.Id.ToString();
    }

    /// <summary>
    /// Check if user exists with email
    /// </summary>
    public async Task<bool> ExistsWithEmailAsync(string email, string? exceptId = null)
    {
        var user = await _userManager.FindByEmailAsync(email);

        if (user == null)
        {
            return false;
        }

        if (!string.IsNullOrEmpty(exceptId) && user.Id.ToString() == exceptId)
        {
            return false;
        }

        return true;
    }

    /// <summary>
    /// Check if user exists with username
    /// </summary>
    public async Task<bool> ExistsWithNameAsync(string userName, string? exceptId = null)
    {
        var user = await _userManager.FindByNameAsync(userName);

        if (user == null)
        {
            return false;
        }

        if (!string.IsNullOrEmpty(exceptId) && user.Id.ToString() == exceptId)
        {
            return false;
        }

        return true;
    }

    /// <summary>
    /// Assign roles to user
    /// </summary>
    public async Task<ApiResponse<string>> AssignRolesToUserAsync(string userId, List<string> roleNames)
    {
        try
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return new ApiResponse<string>(false, "User not found.", string.Empty);
            }

            foreach (var roleName in roleNames)
            {
                // Check if role exists, create if it doesn't
                if (!await _roleManager.RoleExistsAsync(roleName))
                {
                    var role = new Role
                    {
                        Name = roleName,
                        CreatedAt = DateTime.UtcNow
                    };
                    await _roleManager.CreateAsync(role);
                }

                // Add user to role if not already in it
                if (!await _userManager.IsInRoleAsync(user, roleName))
                {
                    await _userManager.AddToRoleAsync(user, roleName);
                }
            }

            return new ApiResponse<string>(true, "Roles assigned successfully.", userId);
        }
        catch (Exception ex)
        {
            return new ApiResponse<string>(false, $"Error assigning roles: {ex.Message}", string.Empty);
        }
    }

    /// <summary>
    /// Remove roles from user
    /// </summary>
    public async Task<ApiResponse<string>> RemoveRolesFromUserAsync(string userId, List<string> roleNames)
    {
        try
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return new ApiResponse<string>(false, "User not found.", string.Empty);
            }

            foreach (var roleName in roleNames)
            {
                // Remove user from role if they are in it
                if (await _userManager.IsInRoleAsync(user, roleName))
                {
                    await _userManager.RemoveFromRoleAsync(user, roleName);
                }
            }

            return new ApiResponse<string>(true, "Roles removed successfully.", userId);
        }
        catch (Exception ex)
        {
            return new ApiResponse<string>(false, $"Error removing roles: {ex.Message}", string.Empty);
        }
    }

    /// <summary>
    /// Update user roles
    /// </summary>
    public async Task<ApiResponse<string>> UpdateUserRolesAsync(string userId, List<UserRoleDetailsDto> userRoles)
    {
        try
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return new ApiResponse<string>(false, "User not found.", string.Empty);
            }

            // Get current user roles
            var currentRoles = await _userManager.GetRolesAsync(user);

            // Remove all current roles
            if (currentRoles.Any())
            {
                await _userManager.RemoveFromRolesAsync(user, currentRoles);
            }

            // Add new roles
            foreach (var userRole in userRoles.Where(ur => ur.Enabled && !string.IsNullOrEmpty(ur.RoleName)))
            {
                // Check if role exists, create if it doesn't
                if (!await _roleManager.RoleExistsAsync(userRole.RoleName!))
                {
                    var role = new Role
                    {
                        Name = userRole.RoleName!,
                        Description = userRole.Description,
                        CreatedAt = DateTime.UtcNow
                    };
                    await _roleManager.CreateAsync(role);
                }

                await _userManager.AddToRoleAsync(user, userRole.RoleName!);
            }

            return new ApiResponse<string>(true, "User roles updated successfully.", userId);
        }
        catch (Exception ex)
        {
            return new ApiResponse<string>(false, $"Error updating user roles: {ex.Message}", string.Empty);
        }
    }
}