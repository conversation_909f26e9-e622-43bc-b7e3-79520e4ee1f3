[2025-06-03 12:44:59.257 +05:30 INF] Executed DbCommand (79ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-03 12:45:00.647 +05:30 INF] Executed DbCommand (101ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-03 12:45:00.940 +05:30 INF] Executed DbCommand (56ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-03 12:45:02.414 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-03 12:45:02.976 +05:30 INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-03 12:45:03.431 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-03 12:45:03.860 +05:30 INF] Executed DbCommand (42ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-03 12:45:04.110 +05:30 INF] Connection to lrbnewqa's Database Succeeded.
[2025-06-03 12:45:04.612 +05:30 INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-03 12:45:04.918 +05:30 INF] Connection to black's Database Succeeded.
[2025-06-03 12:45:08.004 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-03 12:45:08.222 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-03 12:45:08.370 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger - null null
[2025-06-03 12:45:08.760 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-03 12:45:08.760 +05:30 INF] Hosting environment: dev
[2025-06-03 12:45:08.761 +05:30 INF] Content root path: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host
[2025-06-03 12:45:09.824 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:09.865 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-03 12:45:09.875 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-03 12:45:09.877 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:09.880 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:09.883 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-03 12:45:09.883 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-03 12:45:09.958 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/index.html - null null
[2025-06-03 12:45:09.971 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:09.972 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-03 12:45:09.975 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-03 12:45:09.976 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:09.976 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:09.976 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-03 12:45:10.022 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-03 12:45:10.049 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger - 301 null null 1705.24ms
[2025-06-03 12:45:10.257 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/index.html - 200 null text/html;charset=utf-8 299.0026ms
[2025-06-03 12:45:10.262 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/swagger-ui.css - null null
[2025-06-03 12:45:10.275 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/swagger-ui-standalone-preset.js - null null
[2025-06-03 12:45:10.309 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/index.js - null null
[2025-06-03 12:45:10.309 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/swagger-ui-bundle.js - null null
[2025-06-03 12:45:10.309 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:10.309 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:10.313 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-03 12:45:10.313 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-03 12:45:10.313 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-03 12:45:10.313 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-03 12:45:10.313 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:10.313 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:10.313 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:10.313 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:10.313 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-03 12:45:10.313 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-03 12:45:10.313 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-03 12:45:10.313 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:10.322 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-03 12:45:10.313 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-03 12:45:10.313 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:10.324 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/index.css - null null
[2025-06-03 12:45:10.324 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-03 12:45:10.325 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-03 12:45:10.330 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-03 12:45:10.330 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-03 12:45:10.330 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:10.330 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:10.330 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:10.330 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:10.330 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-03 12:45:10.331 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-03 12:45:10.331 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-03 12:45:10.331 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-03 12:45:10.331 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:10.331 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-03 12:45:10.331 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-03 12:45:10.331 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:10.331 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:10.331 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-03 12:45:10.331 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-03 12:45:10.335 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/index.js - 200 null application/javascript;charset=utf-8 37.9935ms
[2025-06-03 12:45:10.342 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - 200 16501 application/javascript; charset=utf-8 26.8719ms
[2025-06-03 12:45:10.370 +05:30 INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
[2025-06-03 12:45:10.370 +05:30 INF] Sending file. Request path: '/index.css'. Physical path: 'N/A'
[2025-06-03 12:45:10.370 +05:30 INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
[2025-06-03 12:45:10.370 +05:30 INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
[2025-06-03 12:45:10.370 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/index.css - 200 202 text/css 48.1502ms
[2025-06-03 12:45:10.380 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/swagger-ui-standalone-preset.js - 200 229223 text/javascript 105.0603ms
[2025-06-03 12:45:10.380 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/swagger-ui.css - 200 154949 text/css 118.3362ms
[2025-06-03 12:45:10.412 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/swagger-ui-bundle.js - 200 1466908 text/javascript 103.1878ms
[2025-06-03 12:45:10.641 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - null null
[2025-06-03 12:45:10.642 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:10.643 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-03 12:45:10.643 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-03 12:45:10.643 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:10.643 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:10.643 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-03 12:45:10.643 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-03 12:45:10.662 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/favicon-32x32.png - null null
[2025-06-03 12:45:10.663 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:10.663 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-03 12:45:10.663 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-03 12:45:10.664 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:10.664 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 12:45:10.664 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-03 12:45:10.664 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-03 12:45:10.666 +05:30 INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
[2025-06-03 12:45:10.667 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/favicon-32x32.png - 200 628 image/png 4.6015ms
[2025-06-03 12:45:10.823 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 182.1865ms
[2025-06-03 13:30:46.613 +05:30 INF] Executed DbCommand (47ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-03 13:30:47.269 +05:30 INF] Executed DbCommand (54ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
WHERE t."Id" = @__p_0
LIMIT 1
[2025-06-03 13:30:47.483 +05:30 INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AdminEmail", t."CompanyName", t."ConnectionString", t."CreatedBy", t."CreatedOn", t."Identifier", t."IsActive", t."Issuer", t."LastModifiedBy", t."LastModifiedOn", t."Name", t."ResolutionKeys", t."SubscriptionStatus", t."ValidUpto"
FROM "MultiTenancy"."Tenants" AS t
[2025-06-03 13:30:48.598 +05:30 WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
[2025-06-03 13:30:49.088 +05:30 INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-03 13:30:49.432 +05:30 INF] Connection to root's Database Succeeded.
[2025-06-03 13:30:49.717 +05:30 INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-03 13:30:49.907 +05:30 INF] Connection to lrbnewqa's Database Succeeded.
[2025-06-03 13:30:50.312 +05:30 INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-03 13:30:50.503 +05:30 INF] Connection to black's Database Succeeded.
[2025-06-03 13:30:51.079 +05:30 INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
[2025-06-03 13:30:51.283 +05:30 WRN] The WebRootPath was not found: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host\wwwroot. Static files may be unavailable.
[2025-06-03 13:30:51.385 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - null null
[2025-06-03 13:30:51.477 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/_framework/aspnetcore-browser-refresh.js - 200 16501 application/javascript; charset=utf-8 116.9694ms
[2025-06-03 13:30:51.657 +05:30 INF] Application started. Press Ctrl+C to shut down.
[2025-06-03 13:30:51.658 +05:30 INF] Hosting environment: dev
[2025-06-03 13:30:51.658 +05:30 INF] Content root path: C:\Users\<USER>\source\repos\this-applications\backend\Web.Host
[2025-06-03 13:30:51.798 +05:30 INF] Request starting HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - null null
[2025-06-03 13:30:52.302 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 13:30:52.307 +05:30 DBG] GetIdentifierAsync: Found identifier: ""
[2025-06-03 13:30:52.315 +05:30 DBG] TryGetByIdentifierAsync: Unable to find Tenant with identifier ""
[2025-06-03 13:30:52.317 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 13:30:52.319 +05:30 DBG] GetIdentifierAsync: No identifier found
[2025-06-03 13:30:52.320 +05:30 DBG] GetIdentifierAsync: Found identifier: "root"
[2025-06-03 13:30:52.320 +05:30 DBG] TryGetByIdentifierAsync: Tenant found with identifier "root"
[2025-06-03 13:30:52.483 +05:30 INF] Request finished HTTP/2 GET https://localhost:44391/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 685.428ms
