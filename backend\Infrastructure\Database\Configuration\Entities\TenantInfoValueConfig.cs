using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for TenantInfoValue entity
/// </summary>
public class TenantInfoValueConfig : IEntityTypeConfiguration<TenantInfoValue>
{
    public void Configure(EntityTypeBuilder<TenantInfoValue> builder)
    {
        builder.ToTable("TenantInfoValues", "Genp");

        // Primary key
        builder.HasKey(e => e.Id);

        // Properties
        builder.Property(e => e.TenantInfoMetadataId)
            .IsRequired();

        builder.Property(e => e.RefId);

        builder.Property(e => e.Value)
            .HasColumnType("TEXT");

        builder.Property(e => e.CreatedAt)
            .HasDefaultValueSql("NOW()")
            .IsRequired();

        builder.Property(e => e.CreatedBy);

        builder.Property(e => e.ModifiedAt)
            .HasDefaultValueSql("NOW()")
            .IsRequired();

        builder.Property(e => e.ModifiedBy);

        // Indexes
        builder.HasIndex(e => e.TenantInfoMetadataId)
            .HasDatabaseName("IX_TenantInfoValues_TenantInfoMetadataId");

        builder.HasIndex(e => e.RefId)
            .HasDatabaseName("IX_TenantInfoValues_RefId");

        builder.HasIndex(e => e.CreatedAt)
            .HasDatabaseName("IX_TenantInfoValues_CreatedAt");

        // Multi-tenancy
        builder.IsMultiTenant();

        // Relationships
        builder.HasOne(e => e.TenantInfoMetadata)
            .WithMany(e => e.TenantInfoValues)
            .HasForeignKey(e => e.TenantInfoMetadataId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
