using Application.DataTypes.DTOs;
using Application.DataTypes.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.DataTypes.Queries;

/// <summary>
/// Get DataTypes query handler
/// </summary>
public class GetDataTypesQueryHandler : IRequestHandler<GetDataTypesQuery, PaginatedResult<DataTypeDto>>
{
    private readonly IRepository<DataType> _repository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetDataTypesQueryHandler(IRepository<DataType> repository)
    {
        _repository = repository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<PaginatedResult<DataTypeDto>> Handle(GetDataTypesQuery request, CancellationToken cancellationToken)
    {
        var skip = (request.PageNumber - 1) * request.PageSize;
        var spec = new DataTypesWithFiltersSpec(request.SearchTerm, request.Category, request.IsActive, request.OrderBy, skip, request.PageSize);
        var countSpec = new DataTypesCountSpec(request.SearchTerm, request.Category, request.IsActive);

        var dataTypes = await _repository.ListAsync(spec, cancellationToken);
        var totalCount = await _repository.CountAsync(countSpec, cancellationToken);

        var dataTypeDtos = dataTypes.Select(dt => new DataTypeDto
        {
            Id = dt.Id,
            Name = dt.Name,
            DisplayName = dt.DisplayName,
            Category = dt.Category,
            UiComponent = dt.UiComponent,
            ValidationPattern = dt.ValidationPattern,
            MinLength = dt.MinLength,
            MaxLength = dt.MaxLength,
            MinValue = dt.MinValue,
            MaxValue = dt.MaxValue,
            DecimalPlaces = dt.DecimalPlaces,
            StepValue = dt.StepValue,
            IsRequired = dt.IsRequired,
            InputType = dt.InputType,
            InputMask = dt.InputMask,
            Placeholder = dt.Placeholder,
            HtmlAttributes = dt.HtmlAttributes,
            DefaultOptions = dt.DefaultOptions,
            AllowsMultiple = dt.AllowsMultiple,
            AllowsCustomOptions = dt.AllowsCustomOptions,
            MaxSelections = dt.MaxSelections,
            AllowedFileTypes = dt.AllowedFileTypes,
            MaxFileSizeBytes = dt.MaxFileSizeBytes,
            RequiredErrorMessage = dt.RequiredErrorMessage,
            PatternErrorMessage = dt.PatternErrorMessage,
            MinLengthErrorMessage = dt.MinLengthErrorMessage,
            MaxLengthErrorMessage = dt.MaxLengthErrorMessage,
            MinValueErrorMessage = dt.MinValueErrorMessage,
            MaxValueErrorMessage = dt.MaxValueErrorMessage,
            FileTypeErrorMessage = dt.FileTypeErrorMessage,
            FileSizeErrorMessage = dt.FileSizeErrorMessage,
            IsActive = dt.IsActive,
            CreatedAt = dt.CreatedAt,
            CreatedBy = dt.CreatedBy ?? Guid.Empty,
            ModifiedAt = dt.ModifiedAt,
            ModifiedBy = dt.ModifiedBy
        }).ToList();

        return new PaginatedResult<DataTypeDto>(dataTypeDtos, request.PageNumber, request.PageSize, totalCount);
    }
}
