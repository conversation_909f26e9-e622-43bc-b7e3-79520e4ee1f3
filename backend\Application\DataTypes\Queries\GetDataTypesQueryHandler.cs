using Abstraction.Repositories;
using Application.DataTypes.DTOs;
using Domain.Entities;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.DataTypes.Queries;

/// <summary>
/// Handler for getting paginated list of data types
/// </summary>
public class GetDataTypesQueryHandler : IRequestHandler<GetDataTypesQuery, PaginatedResult<DataTypeDto>>
{
    private readonly IDynamicRepository<DataType> _repository;
    private readonly ILogger<GetDataTypesQueryHandler> _logger;

    public GetDataTypesQueryHandler(
        IDynamicRepository<DataType> repository,
        ILogger<GetDataTypesQueryHandler> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    public async Task<PaginatedResult<DataTypeDto>> Handle(GetDataTypesQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting data types with filters - Page: {PageNumber}, Size: {PageSize}, Search: {SearchTerm}, Category: {Category}, IsActive: {IsActive}",
                request.PageNumber, request.PageSize, request.SearchTerm, request.Category, request.IsActive);

            // Get all data types from repository
            var allDataTypes = await _repository.GetAllAsync();

            // Apply filters
            var filteredDataTypes = allDataTypes.AsQueryable();

            if (!string.IsNullOrWhiteSpace(request.SearchTerm))
            {
                var searchTerm = request.SearchTerm.ToLower();
                filteredDataTypes = filteredDataTypes.Where(dt => 
                    (!string.IsNullOrEmpty(dt.Name) && dt.Name.ToLower().Contains(searchTerm)) ||
                    (!string.IsNullOrEmpty(dt.DisplayName) && dt.DisplayName.ToLower().Contains(searchTerm)) ||
                    (!string.IsNullOrEmpty(dt.Description) && dt.Description.ToLower().Contains(searchTerm)));
            }

            if (!string.IsNullOrWhiteSpace(request.Category))
            {
                filteredDataTypes = filteredDataTypes.Where(dt => 
                    !string.IsNullOrEmpty(dt.Category) && dt.Category.Equals(request.Category, StringComparison.OrdinalIgnoreCase));
            }

            if (request.IsActive.HasValue)
            {
                filteredDataTypes = filteredDataTypes.Where(dt => dt.IsActive == request.IsActive.Value);
            }

            // Apply ordering
            if (!string.IsNullOrWhiteSpace(request.OrderBy))
            {
                filteredDataTypes = request.OrderBy.ToLower() switch
                {
                    "name" => filteredDataTypes.OrderBy(dt => dt.Name),
                    "displayname" => filteredDataTypes.OrderBy(dt => dt.DisplayName),
                    "category" => filteredDataTypes.OrderBy(dt => dt.Category),
                    "createdat" => filteredDataTypes.OrderBy(dt => dt.CreatedAt),
                    "modifiedat" => filteredDataTypes.OrderBy(dt => dt.ModifiedAt),
                    "sortorder" => filteredDataTypes.OrderBy(dt => dt.SortOrder),
                    _ => filteredDataTypes.OrderBy(dt => dt.Name)
                };
            }
            else
            {
                filteredDataTypes = filteredDataTypes.OrderBy(dt => dt.Name);
            }

            // Get total count
            var totalCount = filteredDataTypes.Count();

            // Apply pagination
            var pagedDataTypes = filteredDataTypes
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToList();

            // Map to DTOs
            var dataTypeDtos = pagedDataTypes.Adapt<List<DataTypeDto>>();

            _logger.LogInformation("Retrieved {Count} data types out of {Total} total", dataTypeDtos.Count, totalCount);

            return PaginatedResult<DataTypeDto>.Success(
                dataTypeDtos,
                totalCount,
                request.PageNumber,
                request.PageSize);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting data types");
            return PaginatedResult<DataTypeDto>.Failure(new List<string> { ex.Message });
        }
    }
}
