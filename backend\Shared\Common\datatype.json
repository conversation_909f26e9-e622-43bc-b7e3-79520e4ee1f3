[{"Id": "012185b8-e944-4dc2-ad2d-b6e95f174a76", "Name": "file", "DisplayName": "File", "Category": "Upload", "UiComponent": "FileUpload", "ValidationPattern": null, "MinLength": null, "MaxLength": null, "MinValue": null, "MaxValue": null, "DecimalPlaces": null, "StepValue": null, "IsRequired": false, "InputType": "file", "InputMask": null, "Placeholder": "Choose file...", "HtmlAttributes": null, "DefaultOptions": null, "AllowsMultiple": false, "AllowsCustomOptions": false, "MaxSelections": null, "AllowedFileTypes": "*", "MaxFileSizeBytes": 10485760, "RequiredErrorMessage": "This field is required", "PatternErrorMessage": null, "MinLengthErrorMessage": null, "MaxLengthErrorMessage": null, "MinValueErrorMessage": null, "MaxValueErrorMessage": null, "FileTypeErrorMessage": "Invalid file type", "FileSizeErrorMessage": "File size exceeds limit", "IsActive": true, "IsDeleted": false, "CreatedAt": "2025-06-01 10:23:03.033271+00", "CreatedBy": null, "ModifiedAt": "2025-06-01 10:23:03.033271+00", "ModifiedBy": null}, {"Id": "05f0b9e7-7ce0-4d23-a9b5-1d3de9a8b4c0", "Name": "text", "DisplayName": "Text", "Category": "Input", "UiComponent": "TextInput", "ValidationPattern": null, "MinLength": 1, "MaxLength": 255, "MinValue": null, "MaxValue": null, "DecimalPlaces": null, "StepValue": null, "IsRequired": false, "InputType": "text", "InputMask": null, "Placeholder": "Enter text...", "HtmlAttributes": null, "DefaultOptions": null, "AllowsMultiple": false, "AllowsCustomOptions": false, "MaxSelections": null, "AllowedFileTypes": null, "MaxFileSizeBytes": null, "RequiredErrorMessage": "This field is required", "PatternErrorMessage": "Invalid format", "MinLengthErrorMessage": "Text is too short", "MaxLengthErrorMessage": "Text is too long", "MinValueErrorMessage": null, "MaxValueErrorMessage": null, "FileTypeErrorMessage": null, "FileSizeErrorMessage": null, "IsActive": true, "IsDeleted": false, "CreatedAt": "2025-06-01 10:23:03.033271+00", "CreatedBy": null, "ModifiedAt": "2025-06-01 10:23:03.033271+00", "ModifiedBy": null}, {"Id": "17c5a4e6-4a78-4a1b-85c0-e5e4e6f8c9d1", "Name": "number", "DisplayName": "Number", "Category": "Input", "UiComponent": "NumberInput", "ValidationPattern": "^[0-9]+(\\.[0-9]+)?$", "MinLength": null, "MaxLength": null, "MinValue": null, "MaxValue": null, "DecimalPlaces": 2, "StepValue": 1, "IsRequired": false, "InputType": "number", "InputMask": null, "Placeholder": "Enter number...", "HtmlAttributes": null, "DefaultOptions": null, "AllowsMultiple": false, "AllowsCustomOptions": false, "MaxSelections": null, "AllowedFileTypes": null, "MaxFileSizeBytes": null, "RequiredErrorMessage": "This field is required", "PatternErrorMessage": "Please enter a valid number", "MinLengthErrorMessage": null, "MaxLengthErrorMessage": null, "MinValueErrorMessage": "Value is too small", "MaxValueErrorMessage": "Value is too large", "FileTypeErrorMessage": null, "FileSizeErrorMessage": null, "IsActive": true, "IsDeleted": false, "CreatedAt": "2025-06-01 10:23:03.033271+00", "CreatedBy": null, "ModifiedAt": "2025-06-01 10:23:03.033271+00", "ModifiedBy": null}, {"Id": "2a1b3c4d-5e6f-7890-ab12-cd34ef567890", "Name": "date", "DisplayName": "Date", "Category": "Input", "UiComponent": "DatePicker", "ValidationPattern": null, "MinLength": null, "MaxLength": null, "MinValue": null, "MaxValue": null, "DecimalPlaces": null, "StepValue": null, "IsRequired": false, "InputType": "date", "InputMask": null, "Placeholder": "Select date...", "HtmlAttributes": null, "DefaultOptions": null, "AllowsMultiple": false, "AllowsCustomOptions": false, "MaxSelections": null, "AllowedFileTypes": null, "MaxFileSizeBytes": null, "RequiredErrorMessage": "This field is required", "PatternErrorMessage": "Invalid date format", "MinLengthErrorMessage": null, "MaxLengthErrorMessage": null, "MinValueErrorMessage": null, "MaxValueErrorMessage": null, "FileTypeErrorMessage": null, "FileSizeErrorMessage": null, "IsActive": true, "IsDeleted": false, "CreatedAt": "2025-06-01 10:23:03.033271+00", "CreatedBy": null, "ModifiedAt": "2025-06-01 10:23:03.033271+00", "ModifiedBy": null}, {"Id": "3b2c4d5e-6f78-90ab-cd12-ef34567890ab", "Name": "datetime", "DisplayName": "Date Time", "Category": "Input", "UiComponent": "DateTimePicker", "ValidationPattern": null, "MinLength": null, "MaxLength": null, "MinValue": null, "MaxValue": null, "DecimalPlaces": null, "StepValue": null, "IsRequired": false, "InputType": "datetime-local", "InputMask": null, "Placeholder": "Select date and time...", "HtmlAttributes": null, "DefaultOptions": null, "AllowsMultiple": false, "AllowsCustomOptions": false, "MaxSelections": null, "AllowedFileTypes": null, "MaxFileSizeBytes": null, "RequiredErrorMessage": "This field is required", "PatternErrorMessage": "Invalid datetime format", "MinLengthErrorMessage": null, "MaxLengthErrorMessage": null, "MinValueErrorMessage": null, "MaxValueErrorMessage": null, "FileTypeErrorMessage": null, "FileSizeErrorMessage": null, "IsActive": true, "IsDeleted": false, "CreatedAt": "2025-06-01 10:23:03.033271+00", "CreatedBy": null, "ModifiedAt": "2025-06-01 10:23:03.033271+00", "ModifiedBy": null}, {"Id": "4c3d5e6f-7890-ab12-cd34-ef567890abcd", "Name": "boolean", "DisplayName": "Boolean", "Category": "Input", "UiComponent": "Checkbox", "ValidationPattern": null, "MinLength": null, "MaxLength": null, "MinValue": null, "MaxValue": null, "DecimalPlaces": null, "StepValue": null, "IsRequired": false, "InputType": "checkbox", "InputMask": null, "Placeholder": null, "HtmlAttributes": null, "DefaultOptions": null, "AllowsMultiple": false, "AllowsCustomOptions": false, "MaxSelections": null, "AllowedFileTypes": null, "MaxFileSizeBytes": null, "RequiredErrorMessage": "This field is required", "PatternErrorMessage": null, "MinLengthErrorMessage": null, "MaxLengthErrorMessage": null, "MinValueErrorMessage": null, "MaxValueErrorMessage": null, "FileTypeErrorMessage": null, "FileSizeErrorMessage": null, "IsActive": true, "IsDeleted": false, "CreatedAt": "2025-06-01 10:23:03.033271+00", "CreatedBy": null, "ModifiedAt": "2025-06-01 10:23:03.033271+00", "ModifiedBy": null}, {"Id": "5d4e6f78-90ab-cd12-ef34-567890abcdef", "Name": "select", "DisplayName": "Select", "Category": "Selection", "UiComponent": "Select", "ValidationPattern": null, "MinLength": null, "MaxLength": null, "MinValue": null, "MaxValue": null, "DecimalPlaces": null, "StepValue": null, "IsRequired": false, "InputType": "select", "InputMask": null, "Placeholder": "Choose option...", "HtmlAttributes": null, "DefaultOptions": "[]", "AllowsMultiple": false, "AllowsCustomOptions": false, "MaxSelections": 1, "AllowedFileTypes": null, "MaxFileSizeBytes": null, "RequiredErrorMessage": "This field is required", "PatternErrorMessage": null, "MinLengthErrorMessage": null, "MaxLengthErrorMessage": null, "MinValueErrorMessage": null, "MaxValueErrorMessage": null, "FileTypeErrorMessage": null, "FileSizeErrorMessage": null, "IsActive": true, "IsDeleted": false, "CreatedAt": "2025-06-01 10:23:03.033271+00", "CreatedBy": null, "ModifiedAt": "2025-06-01 10:23:03.033271+00", "ModifiedBy": null}, {"Id": "6e5f7890-ab12-cd34-ef56-7890abcdef01", "Name": "multiselect", "DisplayName": "Multi Select", "Category": "Selection", "UiComponent": "MultiSelect", "ValidationPattern": null, "MinLength": null, "MaxLength": null, "MinValue": null, "MaxValue": null, "DecimalPlaces": null, "StepValue": null, "IsRequired": false, "InputType": "select", "InputMask": null, "Placeholder": "Choose options...", "HtmlAttributes": null, "DefaultOptions": "[]", "AllowsMultiple": true, "AllowsCustomOptions": false, "MaxSelections": null, "AllowedFileTypes": null, "MaxFileSizeBytes": null, "RequiredErrorMessage": "This field is required", "PatternErrorMessage": null, "MinLengthErrorMessage": null, "MaxLengthErrorMessage": null, "MinValueErrorMessage": null, "MaxValueErrorMessage": null, "FileTypeErrorMessage": null, "FileSizeErrorMessage": null, "IsActive": true, "IsDeleted": false, "CreatedAt": "2025-06-01 10:23:03.033271+00", "CreatedBy": null, "ModifiedAt": "2025-06-01 10:23:03.033271+00", "ModifiedBy": null}, {"Id": "7f6890ab-cd12-ef34-5678-90abcdef0123", "Name": "email", "DisplayName": "Email", "Category": "Input", "UiComponent": "EmailInput", "ValidationPattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", "MinLength": 5, "MaxLength": 320, "MinValue": null, "MaxValue": null, "DecimalPlaces": null, "StepValue": null, "IsRequired": false, "InputType": "email", "InputMask": null, "Placeholder": "Enter email address...", "HtmlAttributes": null, "DefaultOptions": null, "AllowsMultiple": false, "AllowsCustomOptions": false, "MaxSelections": null, "AllowedFileTypes": null, "MaxFileSizeBytes": null, "RequiredErrorMessage": "This field is required", "PatternErrorMessage": "Please enter a valid email address", "MinLengthErrorMessage": "Email is too short", "MaxLengthErrorMessage": "Email is too long", "MinValueErrorMessage": null, "MaxValueErrorMessage": null, "FileTypeErrorMessage": null, "FileSizeErrorMessage": null, "IsActive": true, "IsDeleted": false, "CreatedAt": "22025-06-01 10:23:03.033271+00", "CreatedBy": null, "ModifiedAt": "2025-06-01 10:23:03.033271+00", "ModifiedBy": null}, {"Id": "8901abcd-ef12-3456-7890-abcdef012345", "Name": "phone", "DisplayName": "Phone", "Category": "Input", "UiComponent": "PhoneInput", "ValidationPattern": "^[\\+]?[1-9][\\d]{0,15}$", "MinLength": 10, "MaxLength": 15, "MinValue": null, "MaxValue": null, "DecimalPlaces": null, "StepValue": null, "IsRequired": false, "InputType": "tel", "InputMask": null, "Placeholder": "Enter phone number...", "HtmlAttributes": null, "DefaultOptions": null, "AllowsMultiple": false, "AllowsCustomOptions": false, "MaxSelections": null, "AllowedFileTypes": null, "MaxFileSizeBytes": null, "RequiredErrorMessage": "This field is required", "PatternErrorMessage": "Please enter a valid phone number", "MinLengthErrorMessage": "Phone number is too short", "MaxLengthErrorMessage": "Phone number is too long", "MinValueErrorMessage": null, "MaxValueErrorMessage": null, "FileTypeErrorMessage": null, "FileSizeErrorMessage": null, "IsActive": true, "IsDeleted": false, "CreatedAt": "2025-06-01 10:23:03.033271+00", "CreatedBy": null, "ModifiedAt": "2025-06-01 10:23:03.033271+00", "ModifiedBy": null}, {"Id": "9a01bcde-f123-4567-890a-bcdef0123456", "Name": "url", "DisplayName": "URL", "Category": "Input", "UiComponent": "UrlInput", "ValidationPattern": "^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)$", "MinLength": 10, "MaxLength": 2083, "MinValue": null, "MaxValue": null, "DecimalPlaces": null, "StepValue": null, "IsRequired": false, "InputType": "url", "InputMask": null, "Placeholder": "Enter URL...", "HtmlAttributes": null, "DefaultOptions": null, "AllowsMultiple": false, "AllowsCustomOptions": false, "MaxSelections": null, "AllowedFileTypes": null, "MaxFileSizeBytes": null, "RequiredErrorMessage": "This field is required", "PatternErrorMessage": "Please enter a valid URL", "MinLengthErrorMessage": "URL is too short", "MaxLengthErrorMessage": "URL is too long", "MinValueErrorMessage": null, "MaxValueErrorMessage": null, "FileTypeErrorMessage": null, "FileSizeErrorMessage": null, "IsActive": true, "IsDeleted": false, "CreatedAt": "2025-06-01 10:23:03.033271+00", "CreatedBy": null, "ModifiedAt": "2025-06-01 10:23:03.033271+00", "ModifiedBy": null}, {"Id": "ab12cdef-2345-6789-0abc-def012345678", "Name": "image", "DisplayName": "Image", "Category": "Upload", "UiComponent": "ImageUpload", "ValidationPattern": null, "MinLength": null, "MaxLength": null, "MinValue": null, "MaxValue": null, "DecimalPlaces": null, "StepValue": null, "IsRequired": false, "InputType": "file", "InputMask": null, "Placeholder": "Choose image...", "HtmlAttributes": null, "DefaultOptions": null, "AllowsMultiple": false, "AllowsCustomOptions": false, "MaxSelections": null, "AllowedFileTypes": ".jpg,.jpeg,.png,.gif,.webp", "MaxFileSizeBytes": 5242880, "RequiredErrorMessage": "This field is required", "PatternErrorMessage": null, "MinLengthErrorMessage": null, "MaxLengthErrorMessage": null, "MinValueErrorMessage": null, "MaxValueErrorMessage": null, "FileTypeErrorMessage": "Please select a valid image file", "FileSizeErrorMessage": "Image size exceeds limit", "IsActive": true, "IsDeleted": false, "CreatedAt": "2025-06-01 10:23:03.033271+00", "CreatedBy": null, "ModifiedAt": "2025-06-01 10:23:03.033271+00", "ModifiedBy": null}, {"Id": "bc23def0-3456-789a-bcde-f01234567890", "Name": "richtext", "DisplayName": "Rich Text", "Category": "Input", "UiComponent": "RichTextEditor", "ValidationPattern": null, "MinLength": 10, "MaxLength": 50000, "MinValue": null, "MaxValue": null, "DecimalPlaces": null, "StepValue": null, "IsRequired": false, "InputType": "textarea", "InputMask": null, "Placeholder": "Enter rich text...", "HtmlAttributes": null, "DefaultOptions": null, "AllowsMultiple": false, "AllowsCustomOptions": false, "MaxSelections": null, "AllowedFileTypes": null, "MaxFileSizeBytes": null, "RequiredErrorMessage": "This field is required", "PatternErrorMessage": null, "MinLengthErrorMessage": "Content is too short", "MaxLengthErrorMessage": "Content is too long", "MinValueErrorMessage": null, "MaxValueErrorMessage": null, "FileTypeErrorMessage": null, "FileSizeErrorMessage": null, "IsActive": true, "IsDeleted": false, "CreatedAt": "2025-06-01 10:23:03.033271+00", "CreatedBy": null, "ModifiedAt": "2025-06-01 10:23:03.033271+00", "ModifiedBy": null}, {"Id": "cd34ef01-4567-89ab-cdef-012345678901", "Name": "address", "DisplayName": "Address", "Category": "Input", "UiComponent": "AddressInput", "ValidationPattern": null, "MinLength": 10, "MaxLength": 500, "MinValue": null, "MaxValue": null, "DecimalPlaces": null, "StepValue": null, "IsRequired": false, "InputType": "text", "InputMask": null, "Placeholder": "Enter address...", "HtmlAttributes": null, "DefaultOptions": null, "AllowsMultiple": false, "AllowsCustomOptions": false, "MaxSelections": null, "AllowedFileTypes": null, "MaxFileSizeBytes": null, "RequiredErrorMessage": "This field is required", "PatternErrorMessage": null, "MinLengthErrorMessage": "Address is too short", "MaxLengthErrorMessage": "Address is too long", "MinValueErrorMessage": null, "MaxValueErrorMessage": null, "FileTypeErrorMessage": null, "FileSizeErrorMessage": null, "IsActive": true, "IsDeleted": false, "CreatedAt": "2025-06-01 10:23:03.033271+00", "CreatedBy": null, "ModifiedAt": "2025-06-01 10:23:03.033271+00", "ModifiedBy": null}, {"Id": "de45f012-5678-9abc-def0-123456789012", "Name": "currency", "DisplayName": "<PERSON><PERSON><PERSON><PERSON>", "Category": "Input", "UiComponent": "CurrencyInput", "ValidationPattern": "^[0-9]+(\\.[0-9]{1,2})?$", "MinLength": null, "MaxLength": null, "MinValue": 0, "MaxValue": null, "DecimalPlaces": 2, "StepValue": 0.01, "IsRequired": false, "InputType": "number", "InputMask": null, "Placeholder": "Enter amount...", "HtmlAttributes": null, "DefaultOptions": null, "AllowsMultiple": false, "AllowsCustomOptions": false, "MaxSelections": null, "AllowedFileTypes": null, "MaxFileSizeBytes": null, "RequiredErrorMessage": "This field is required", "PatternErrorMessage": "Please enter a valid amount", "MinLengthErrorMessage": null, "MaxLengthErrorMessage": null, "MinValueErrorMessage": "Amount cannot be negative", "MaxValueErrorMessage": "Amount is too large", "FileTypeErrorMessage": null, "FileSizeErrorMessage": null, "IsActive": true, "IsDeleted": false, "CreatedAt": "2025-06-01 10:23:03.033271+00", "CreatedBy": null, "ModifiedAt": "2025-06-01 10:23:03.033271+00", "ModifiedBy": null}, {"Id": "ef561234-6789-abcd-ef01-234567890123", "Name": "percentage", "DisplayName": "Percentage", "Category": "Input", "UiComponent": "PercentageInput", "ValidationPattern": "^[0-9]+(\\.[0-9]+)?$", "MinLength": null, "MaxLength": null, "MinValue": 0, "MaxValue": 100, "DecimalPlaces": 2, "StepValue": 0.01, "IsRequired": false, "InputType": "number", "InputMask": null, "Placeholder": "Enter percentage...", "HtmlAttributes": null, "DefaultOptions": null, "AllowsMultiple": false, "AllowsCustomOptions": false, "MaxSelections": null, "AllowedFileTypes": null, "MaxFileSizeBytes": null, "RequiredErrorMessage": "This field is required", "PatternErrorMessage": "Please enter a valid percentage", "MinLengthErrorMessage": null, "MaxLengthErrorMessage": null, "MinValueErrorMessage": "Percentage cannot be negative", "MaxValueErrorMessage": "Percentage cannot exceed 100%", "FileTypeErrorMessage": null, "FileSizeErrorMessage": null, "IsActive": true, "IsDeleted": false, "CreatedAt": "2025-06-01 10:23:03.033271+00", "CreatedBy": null, "ModifiedAt": "2025-06-01 10:23:03.033271+00", "ModifiedBy": null}, {"Id": "f0672345-789a-bcde-f012-345678901234", "Name": "color", "DisplayName": "Color", "Category": "Input", "UiComponent": "ColorPicker", "ValidationPattern": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", "MinLength": 4, "MaxLength": 7, "MinValue": null, "MaxValue": null, "DecimalPlaces": null, "StepValue": null, "IsRequired": false, "InputType": "color", "InputMask": null, "Placeholder": "Choose color...", "HtmlAttributes": null, "DefaultOptions": null, "AllowsMultiple": false, "AllowsCustomOptions": false, "MaxSelections": null, "AllowedFileTypes": null, "MaxFileSizeBytes": null, "RequiredErrorMessage": "This field is required", "PatternErrorMessage": "Please enter a valid color", "MinLengthErrorMessage": null, "MaxLengthErrorMessage": null, "MinValueErrorMessage": null, "MaxValueErrorMessage": null, "FileTypeErrorMessage": null, "FileSizeErrorMessage": null, "IsActive": true, "IsDeleted": false, "CreatedAt": "2025-06-01 10:23:03.033271+00", "CreatedBy": null, "ModifiedAt": "2025-06-01 10:23:03.033271+00", "ModifiedBy": null}, {"Id": "01783456-89ab-cdef-0123-456789012345", "Name": "rating", "DisplayName": "Rating", "Category": "Input", "UiComponent": "StarRating", "ValidationPattern": null, "MinLength": null, "MaxLength": null, "MinValue": 1, "MaxValue": 5, "DecimalPlaces": 0, "StepValue": 1, "IsRequired": false, "InputType": "number", "InputMask": null, "Placeholder": "Select rating...", "HtmlAttributes": null, "DefaultOptions": null, "AllowsMultiple": false, "AllowsCustomOptions": false, "MaxSelections": null, "AllowedFileTypes": null, "MaxFileSizeBytes": null, "RequiredErrorMessage": "This field is required", "PatternErrorMessage": null, "MinLengthErrorMessage": null, "MaxLengthErrorMessage": null, "MinValueErrorMessage": "Rating must be at least 1", "MaxValueErrorMessage": "Rating cannot exceed 5", "FileTypeErrorMessage": null, "FileSizeErrorMessage": null, "IsActive": true, "IsDeleted": false, "CreatedAt": "2025-06-01 10:23:03.033271+00", "CreatedBy": null, "ModifiedAt": "2025-06-01 10:23:03.033271+00", "ModifiedBy": null}, {"Id": "12894567-9abc-def0-1234-567890123456", "Name": "slider", "DisplayName": "Slide<PERSON>", "Category": "Input", "UiComponent": "Slide<PERSON>", "ValidationPattern": null, "MinLength": null, "MaxLength": null, "MinValue": 0, "MaxValue": 100, "DecimalPlaces": 0, "StepValue": 1, "IsRequired": false, "InputType": "range", "InputMask": null, "Placeholder": null, "HtmlAttributes": null, "DefaultOptions": null, "AllowsMultiple": false, "AllowsCustomOptions": false, "MaxSelections": null, "AllowedFileTypes": null, "MaxFileSizeBytes": null, "RequiredErrorMessage": "This field is required", "PatternErrorMessage": null, "MinLengthErrorMessage": null, "MaxLengthErrorMessage": null, "MinValueErrorMessage": "Value is too small", "MaxValueErrorMessage": "Value is too large", "FileTypeErrorMessage": null, "FileSizeErrorMessage": null, "IsActive": true, "IsDeleted": false, "CreatedAt": "2025-06-01 10:23:03.033271+00", "CreatedBy": null, "ModifiedAt": "2025-06-01 10:23:03.033271+00", "ModifiedBy": null}, {"Id": "239a5678-abcd-ef01-2345-678901234567", "Name": "tag", "DisplayName": "Tag", "Category": "Selection", "UiComponent": "TagInput", "ValidationPattern": null, "MinLength": 1, "MaxLength": 50, "MinValue": null, "MaxValue": null, "DecimalPlaces": null, "StepValue": null, "IsRequired": false, "InputType": "text", "InputMask": null, "Placeholder": "Enter tags...", "HtmlAttributes": null, "DefaultOptions": "[]", "AllowsMultiple": true, "AllowsCustomOptions": true, "MaxSelections": null, "AllowedFileTypes": null, "MaxFileSizeBytes": null, "RequiredErrorMessage": "This field is required", "PatternErrorMessage": null, "MinLengthErrorMessage": "Tag is too short", "MaxLengthErrorMessage": "Tag is too long", "MinValueErrorMessage": null, "MaxValueErrorMessage": null, "FileTypeErrorMessage": null, "FileSizeErrorMessage": null, "IsActive": true, "IsDeleted": false, "CreatedAt": "2025-06-01 10:23:03.033271+00", "CreatedBy": null, "ModifiedAt": "2025-06-01 10:23:03.033271+00", "ModifiedBy": null}, {"Id": "34ab6789-bcde-f012-3456-789012345678", "Name": "textarea", "DisplayName": "Text Area", "Category": "Input", "UiComponent": "TextArea", "ValidationPattern": null, "MinLength": 10, "MaxLength": 5000, "MinValue": null, "MaxValue": null, "DecimalPlaces": null, "StepValue": null, "IsRequired": false, "InputType": "textarea", "InputMask": null, "Placeholder": "Enter text...", "HtmlAttributes": null, "DefaultOptions": null, "AllowsMultiple": false, "AllowsCustomOptions": false, "MaxSelections": null, "AllowedFileTypes": null, "MaxFileSizeBytes": null, "RequiredErrorMessage": "This field is required", "PatternErrorMessage": null, "MinLengthErrorMessage": "Text is too short", "MaxLengthErrorMessage": "Text is too long", "MinValueErrorMessage": null, "MaxValueErrorMessage": null, "FileTypeErrorMessage": null, "FileSizeErrorMessage": null, "IsActive": true, "IsDeleted": false, "CreatedAt": "2025-06-01 10:23:03.033271+00", "CreatedBy": null, "ModifiedAt": "2025-06-01 10:23:03.033271+00", "ModifiedBy": null}, {"Id": "45bc789a-cdef-0123-4567-890123456789", "Name": "checkbox", "DisplayName": "Checkbox", "Category": "Input", "UiComponent": "Checkbox", "ValidationPattern": null, "MinLength": null, "MaxLength": null, "MinValue": null, "MaxValue": null, "DecimalPlaces": null, "StepValue": null, "IsRequired": false, "InputType": "checkbox", "InputMask": null, "Placeholder": null, "HtmlAttributes": null, "DefaultOptions": null, "AllowsMultiple": false, "AllowsCustomOptions": false, "MaxSelections": null, "AllowedFileTypes": null, "MaxFileSizeBytes": null, "RequiredErrorMessage": "This field is required", "PatternErrorMessage": null, "MinLengthErrorMessage": null, "MaxLengthErrorMessage": null, "MinValueErrorMessage": null, "MaxValueErrorMessage": null, "FileTypeErrorMessage": null, "FileSizeErrorMessage": null, "IsActive": true, "IsDeleted": false, "CreatedAt": "2025-06-01 10:23:03.033271+00", "CreatedBy": null, "ModifiedAt": "2025-06-01 10:23:03.033271+00", "ModifiedBy": null}, {"Id": "56cd89ab-def0-1234-5678-90123456789a", "Name": "radio", "DisplayName": "Radio", "Category": "Selection", "UiComponent": "RadioGroup", "ValidationPattern": null, "MinLength": null, "MaxLength": null, "MinValue": null, "MaxValue": null, "DecimalPlaces": null, "StepValue": null, "IsRequired": false, "InputType": "radio", "InputMask": null, "Placeholder": "Choose option...", "HtmlAttributes": null, "DefaultOptions": "[]", "AllowsMultiple": false, "AllowsCustomOptions": false, "MaxSelections": 1, "AllowedFileTypes": null, "MaxFileSizeBytes": null, "RequiredErrorMessage": "This field is required", "PatternErrorMessage": null, "MinLengthErrorMessage": null, "MaxLengthErrorMessage": null, "MinValueErrorMessage": null, "MaxValueErrorMessage": null, "FileTypeErrorMessage": null, "FileSizeErrorMessage": null, "IsActive": true, "IsDeleted": false, "CreatedAt": "2025-06-01 10:23:03.033271+00", "CreatedBy": null, "ModifiedAt": "2025-06-01 10:23:03.033271+00", "ModifiedBy": null}]