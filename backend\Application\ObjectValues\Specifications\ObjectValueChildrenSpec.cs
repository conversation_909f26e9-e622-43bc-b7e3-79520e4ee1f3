using Ardalis.Specification;
using Domain.Entities;

namespace Application.ObjectValues.Specifications;

/// <summary>
/// Specification to get child ObjectValues for hierarchical structure
/// </summary>
public class ObjectValueChildrenSpec : Specification<ObjectValue>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ObjectValueChildrenSpec(Guid parentValueId)
    {
        Query.Where(ov => ov.ParentObjectValueId == parentValueId &&
                          !ov.IsDeleted);

        // Include all related data
        Query.Include(ov => ov.ObjectMetadata)
             .ThenInclude(om => om.Object);

        Query.Include(ov => ov.ObjectMetadata)
             .ThenInclude(om => om.Metadata)
             .ThenInclude(m => m.DataType);

        // Order by field order and metadata key
        Query.OrderBy(ov => ov.ObjectMetadata.Metadata.FieldOrder ?? 999)
             .ThenBy(ov => ov.ObjectMetadata.Metadata.MetadataKey);
    }
}
