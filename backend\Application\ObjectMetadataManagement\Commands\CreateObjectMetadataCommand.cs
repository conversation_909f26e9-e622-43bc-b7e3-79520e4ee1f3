using Application.ObjectMetadataManagement.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.ObjectMetadataManagement.Commands;

/// <summary>
/// Create ObjectMetadata command
/// </summary>
public class CreateObjectMetadataCommand : IRequest<Result<ObjectMetadataDto>>
{
    /// <summary>
    /// Object ID
    /// </summary>
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Metadata ID
    /// </summary>
    public Guid MetadataId { get; set; }

    /// <summary>
    /// Whether this metadata is unique for the object
    /// </summary>
    public bool IsUnique { get; set; } = false;

    /// <summary>
    /// Whether this metadata link is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
