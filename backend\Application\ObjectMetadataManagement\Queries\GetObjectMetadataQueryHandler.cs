using Application.ObjectMetadataManagement.DTOs;
using Application.ObjectMetadataManagement.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.ObjectMetadataManagement.Queries;

/// <summary>
/// Get ObjectMetadata query handler
/// </summary>
public class GetObjectMetadataQueryHandler : IRequestHandler<GetObjectMetadataQuery, PaginatedResult<ObjectMetadataDto>>
{
    private readonly IReadRepository<ObjectMetadata> _repository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetObjectMetadataQueryHandler(IReadRepository<ObjectMetadata> repository)
    {
        _repository = repository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<PaginatedResult<ObjectMetadataDto>> Handle(GetObjectMetadataQuery request, CancellationToken cancellationToken)
    {
        if (!request.ObjectId.HasValue)
        {
            return new PaginatedResult<ObjectMetadataDto>(new List<ObjectMetadataDto>(), request.PageNumber, request.PageSize, 0);
        }

        var skip = (request.PageNumber - 1) * request.PageSize;

        // Create specification for getting ObjectMetadata by ObjectId with pagination and filters
        var spec = new ObjectMetadataWithFiltersSpec(
            objectId: request.ObjectId.Value,
            searchTerm: request.SearchTerm,
            metadataId: request.MetadataId,
            isActive: request.IsActive,
            orderBy: request.OrderBy,
            skip: skip,
            take: request.PageSize);

        // Create count specification (without pagination)
        var countSpec = new ObjectMetadataCountSpec(
            objectId: request.ObjectId.Value,
            searchTerm: request.SearchTerm,
            metadataId: request.MetadataId,
            isActive: request.IsActive);

        // Get data and count
        var objectMetadata = await _repository.ListAsync(spec, cancellationToken);
        var totalCount = await _repository.CountAsync(countSpec, cancellationToken);

        var objectMetadataDtos = objectMetadata.Select(om => new ObjectMetadataDto
        {
            Id = om.Id,
            ObjectId = om.ObjectId,
            ObjectName = om.Object?.Name,
            MetadataId = om.MetadataId,
            MetadataKey = om.Metadata?.MetadataKey,
            MetadataDisplayLabel = om.Metadata?.DisplayLabel,
            IsUnique = om.IsUnique,
            IsActive = om.IsActive,
            ValuesCount = 0, // TODO: Calculate actual values count if needed
            ShouldVisibleInList = om.ShouldVisibleInList,
            ShouldVisibleInEdit = om.ShouldVisibleInEdit,
            ShouldVisibleInCreate = om.ShouldVisibleInCreate,
            ShouldVisibleInView = om.ShouldVisibleInView,
            IsCalculate = om.IsCalculate,
            DataTypeId = om.Metadata?.DataTypeId ?? Guid.Empty,
            DataTypeName = om.Metadata?.DataType?.Name,
            CreatedAt = om.CreatedAt,
            CreatedBy = om.CreatedBy ?? Guid.Empty,
            ModifiedAt = om.ModifiedAt,
            ModifiedBy = om.ModifiedBy
        }).ToList();

        return new PaginatedResult<ObjectMetadataDto>(objectMetadataDtos, request.PageNumber, request.PageSize, totalCount);
    }
}
