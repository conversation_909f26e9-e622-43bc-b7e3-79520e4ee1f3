using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Links Object types to their metadata with IsUnique support
/// </summary>
public class ObjectMetadata : AuditableEntity, IAggregateRoot
{

    /// <summary>
    /// Object ID
    /// </summary>
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Metadata ID
    /// </summary>
    public Guid MetadataId { get; set; }

    /// <summary>
    /// Whether this metadata is unique for the object
    /// </summary>
    public bool IsUnique { get; set; } = false;

    /// <summary>
    /// Whether this metadata link is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// ShouldVisibleInList
    /// </summary>
    public bool ShouldVisibleInList { get; set; } = true;

    /// <summary>
    /// ShouldVisibleInList
    /// </summary>
    public bool ShouldVisibleInEdit { get; set; } = true;

    /// <summary>
    /// ShouldVisibleInList
    /// </summary>
    public bool ShouldVisibleInCreate { get; set; } = true;

    /// <summary>
    /// ShouldVisibleInList
    /// </summary>
    public bool ShouldVisibleInView { get; set; } = true;

    /// <summary>
    /// ShouldVisibleInList
    /// </summary>
    public bool IsCalculate { get; set; } = true;

    // Navigation properties
    /// <summary>
    /// Object
    /// </summary>
    public virtual Object Object { get; set; } = null!;

    /// <summary>
    /// Metadata definition
    /// </summary>
    public virtual Metadata Metadata { get; set; } = null!;

    /// <summary>
    /// Object values
    /// </summary>
    public virtual ICollection<ObjectValue> ObjectValues { get; set; } = new List<ObjectValue>();

    /// <summary>
    /// Field mappings targeting this object metadata
    /// </summary>
    public virtual ICollection<FieldMapping> FieldMappings { get; set; } = new List<FieldMapping>();
}
