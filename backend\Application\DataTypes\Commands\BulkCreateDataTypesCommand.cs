using Application.DataTypes.DTOs;
using MediatR;
using Shared.Common.Response;
using System.ComponentModel.DataAnnotations;

namespace Application.DataTypes.Commands;

/// <summary>
/// Bulk create data types command
/// </summary>
public class BulkCreateDataTypesCommand : IRequest<Result<BulkCreateDataTypesResponse>>
{
    /// <summary>
    /// List of data types to create
    /// </summary>
    [Required]
    public List<CreateDataTypeCommand> DataTypes { get; set; } = new();

    /// <summary>
    /// Whether to continue processing if one item fails
    /// </summary>
    public bool ContinueOnError { get; set; } = true;

    /// <summary>
    /// Whether to validate all items before creating any
    /// </summary>
    public bool ValidateBeforeCreate { get; set; } = true;

    /// <summary>
    /// Whether to skip duplicate names (by Name field)
    /// </summary>
    public bool SkipDuplicates { get; set; } = false;
}

/// <summary>
/// Response for bulk create data types operation
/// </summary>
public class BulkCreateDataTypesResponse
{
    /// <summary>
    /// Total number of data types requested to be created
    /// </summary>
    public int TotalRequested { get; set; }

    /// <summary>
    /// Number of data types successfully created
    /// </summary>
    public int SuccessfullyCreated { get; set; }

    /// <summary>
    /// Number of data types that failed to create
    /// </summary>
    public int Failed { get; set; }

    /// <summary>
    /// Number of data types skipped due to duplicates
    /// </summary>
    public int Skipped { get; set; }

    /// <summary>
    /// List of successfully created data types
    /// </summary>
    public List<DataTypeDto> CreatedDataTypes { get; set; } = new();

    /// <summary>
    /// List of errors that occurred during creation
    /// </summary>
    public List<BulkDataTypeCreationError> Errors { get; set; } = new();

    /// <summary>
    /// Overall operation message
    /// </summary>
    public string Message { get; set; } = string.Empty;
}

/// <summary>
/// Error information for bulk data type creation
/// </summary>
public class BulkDataTypeCreationError
{
    /// <summary>
    /// Index of the data type in the original request
    /// </summary>
    public int Index { get; set; }

    /// <summary>
    /// Name of the data type that failed
    /// </summary>
    public string? DataTypeName { get; set; }

    /// <summary>
    /// Error message
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// Error type (Validation, Duplicate, Database, etc.)
    /// </summary>
    public string ErrorType { get; set; } = string.Empty;
}
