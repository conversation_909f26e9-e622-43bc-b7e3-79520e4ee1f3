using Application.Features.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Features.Commands;

/// <summary>
/// Create feature command
/// </summary>
public class CreateFeatureCommand : IRequest<Result<FeatureDto>>
{
    /// <summary>
    /// Product ID
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Feature name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Feature description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Whether this is a default feature
    /// </summary>
    public bool IsDefault { get; set; } = false;

    /// <summary>
    /// Whether the feature is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
