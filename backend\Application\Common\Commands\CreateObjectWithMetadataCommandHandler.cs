using Abstraction.Common;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Common.Commands;

/// <summary>
/// Handler for creating object with metadata and values
/// </summary>
public class CreateObjectWithMetadataCommandHandler : IRequestHandler<CreateObjectWithMetadataCommand, Result<DynamicOperationResponse>>
{
    private readonly IDynamicRepository<Domain.Entities.Object> _objectRepository;
    private readonly IDynamicRepository<Metadata> _metadataRepository;
    private readonly IDynamicRepository<ObjectMetadata> _objectMetadataRepository;
    private readonly IDynamicRepository<ObjectValue> _objectValueRepository;
    private readonly IDynamicRepository<DataType> _dataTypeRepository;
    private readonly ILogger<CreateObjectWithMetadataCommandHandler> _logger;

    public CreateObjectWithMetadataCommandHandler(
        IDynamicRepository<Domain.Entities.Object> objectRepository,
        IDynamicRepository<Metadata> metadataRepository,
        IDynamicRepository<ObjectMetadata> objectMetadataRepository,
        IDynamicRepository<ObjectValue> objectValueRepository,
        IDynamicRepository<DataType> dataTypeRepository,
        ILogger<CreateObjectWithMetadataCommandHandler> logger)
    {
        _objectRepository = objectRepository;
        _metadataRepository = metadataRepository;
        _objectMetadataRepository = objectMetadataRepository;
        _objectValueRepository = objectValueRepository;
        _dataTypeRepository = dataTypeRepository;
        _logger = logger;
    }

    public async Task<Result<DynamicOperationResponse>> Handle(CreateObjectWithMetadataCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating object with metadata: ObjectName={ObjectName}, ProductId={ProductId}, FeatureId={FeatureId}", 
            request.ObjectName, request.ProductId, request.FeatureId);

        try
        {
            var response = new DynamicOperationResponse
            {
                Success = true,
                Message = "Object with metadata created successfully"
            };

            // Step 1: Check if object exists, create if not
            var existingObject = await GetOrCreateObject(request.ProductId, request.FeatureId, request.ObjectName, response, cancellationToken);
            if (existingObject == null)
            {
                return Result<DynamicOperationResponse>.Failure("Failed to create or retrieve object");
            }

            // Step 2: Process metadata - create if not exists, get existing if exists
            var metadataMap = await ProcessMetadata(request.Metadata, response, cancellationToken);

            // Step 3: Create ObjectMetadata links
            await CreateObjectMetadataLinks(existingObject.Id, metadataMap, response, cancellationToken);

            // Step 4: Create ObjectValues
            await CreateObjectValues(existingObject.Id, metadataMap, request.Values, response, cancellationToken);

            _logger.LogInformation("Successfully created object with metadata. Total records: {TotalCount}", response.InsertedCount);
            return Result<DynamicOperationResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating object with metadata");
            return Result<DynamicOperationResponse>.Failure($"Error creating object with metadata: {ex.Message}");
        }
    }

    /// <summary>
    /// Get existing object or create new one
    /// </summary>
    private async Task<Domain.Entities.Object?> GetOrCreateObject(Guid productId, Guid featureId, string objectName, DynamicOperationResponse response, CancellationToken cancellationToken)
    {
        try
        {
            // Check if object exists
            var existingObjects = await _objectRepository.ListAsync(cancellationToken);
            var existingObject = existingObjects.FirstOrDefault(o => 
                o.FeatureId == featureId && 
                o.Name.Equals(objectName, StringComparison.OrdinalIgnoreCase));

            if (existingObject != null)
            {
                _logger.LogInformation("Found existing object: {ObjectId}", existingObject.Id);
                return existingObject;
            }

            // Create new object
            var objectData = new Dictionary<string, object?>
            {
                ["FeatureId"] = featureId,
                ["Name"] = objectName,
                ["Description"] = $"Object for {objectName}",
                ["IsActive"] = true
            };

            var newObject = await _objectRepository.DynamicUpsertAsync(objectData, cancellationToken);
            response.InsertedCount++;
            response.CreatedIds.Add(newObject.Id);
            
            _logger.LogInformation("Created new object: {ObjectId}", newObject.Id);
            return newObject;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting or creating object");
            return null;
        }
    }

    /// <summary>
    /// Process metadata - create if not exists, return existing if exists
    /// </summary>
    private async Task<Dictionary<string, Guid>> ProcessMetadata(List<Dictionary<string, object?>> metadataList, DynamicOperationResponse response, CancellationToken cancellationToken)
    {
        var metadataMap = new Dictionary<string, Guid>();

        try
        {
            var existingMetadata = await _metadataRepository.ListAsync(cancellationToken);
            var dataTypes = await _dataTypeRepository.ListAsync(cancellationToken);

            foreach (var metadataDict in metadataList)
            {
                if (!metadataDict.TryGetValue("MetadataKey", out var metadataKeyObj) || metadataKeyObj == null)
                {
                    _logger.LogWarning("Metadata entry missing MetadataKey, skipping");
                    continue;
                }

                var metadataKey = metadataKeyObj.ToString()!;
                
                // Check if metadata already exists
                var existing = existingMetadata.FirstOrDefault(m => m.MetadataKey.Equals(metadataKey, StringComparison.OrdinalIgnoreCase));
                if (existing != null)
                {
                    metadataMap[metadataKey] = existing.Id;
                    _logger.LogDebug("Using existing metadata: {MetadataKey}", metadataKey);
                    continue;
                }

                // Create new metadata
                var newMetadata = await CreateNewMetadata(metadataDict, dataTypes, cancellationToken);
                if (newMetadata != null)
                {
                    metadataMap[metadataKey] = newMetadata.Id;
                    response.InsertedCount++;
                    response.CreatedIds.Add(newMetadata.Id);
                    _logger.LogDebug("Created new metadata: {MetadataKey}", metadataKey);
                }
            }

            return metadataMap;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing metadata");
            return metadataMap;
        }
    }

    /// <summary>
    /// Create new metadata entry
    /// </summary>
    private async Task<Metadata?> CreateNewMetadata(Dictionary<string, object?> metadataDict, IEnumerable<DataType> dataTypes, CancellationToken cancellationToken)
    {
        try
        {
            // Get or determine DataTypeId
            Guid dataTypeId;
            if (metadataDict.TryGetValue("DataTypeId", out var dataTypeIdObj) && dataTypeIdObj != null)
            {
                if (Guid.TryParse(dataTypeIdObj.ToString(), out dataTypeId))
                {
                    // Use provided DataTypeId
                }
                else
                {
                    // Default to text if invalid GUID
                    var textDataType = dataTypes.FirstOrDefault(dt => dt.Name.Equals("text", StringComparison.OrdinalIgnoreCase));
                    dataTypeId = textDataType?.Id ?? Guid.NewGuid();
                }
            }
            else
            {
                // Default to text data type
                var textDataType = dataTypes.FirstOrDefault(dt => dt.Name.Equals("text", StringComparison.OrdinalIgnoreCase));
                dataTypeId = textDataType?.Id ?? Guid.NewGuid();
            }

            // Prepare metadata data
            var metadataData = new Dictionary<string, object?>
            {
                ["MetadataKey"] = metadataDict["MetadataKey"],
                ["DataTypeId"] = dataTypeId,
                ["DisplayLabel"] = metadataDict.GetValueOrDefault("DisplayLabel", metadataDict["MetadataKey"]),
                ["IsVisible"] = metadataDict.GetValueOrDefault("IsVisible", true),
                ["IsReadonly"] = metadataDict.GetValueOrDefault("IsReadonly", false)
            };

            // Add optional properties if provided
            if (metadataDict.ContainsKey("CustomValidationPattern"))
                metadataData["CustomValidationPattern"] = metadataDict["CustomValidationPattern"];
            if (metadataDict.ContainsKey("CustomMinLength"))
                metadataData["CustomMinLength"] = metadataDict["CustomMinLength"];
            if (metadataDict.ContainsKey("CustomMaxLength"))
                metadataData["CustomMaxLength"] = metadataDict["CustomMaxLength"];

            return await _metadataRepository.DynamicUpsertAsync(metadataData, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating new metadata");
            return null;
        }
    }

    /// <summary>
    /// Create ObjectMetadata links
    /// </summary>
    private async Task CreateObjectMetadataLinks(Guid objectId, Dictionary<string, Guid> metadataMap, DynamicOperationResponse response, CancellationToken cancellationToken)
    {
        try
        {
            var existingObjectMetadata = await _objectMetadataRepository.ListAsync(cancellationToken);

            foreach (var kvp in metadataMap)
            {
                // Check if ObjectMetadata link already exists
                var existing = existingObjectMetadata.FirstOrDefault(om => om.ObjectId == objectId && om.MetadataId == kvp.Value);
                if (existing != null)
                {
                    _logger.LogDebug("ObjectMetadata link already exists for Object {ObjectId} and Metadata {MetadataId}", objectId, kvp.Value);
                    continue;
                }

                // Create new ObjectMetadata link
                var objectMetadataData = new Dictionary<string, object?>
                {
                    ["ObjectId"] = objectId,
                    ["MetadataId"] = kvp.Value,
                    ["IsUnique"] = false,
                    ["IsActive"] = true,
                    ["ShouldVisibleInList"] = true,
                    ["ShouldVisibleInEdit"] = true,
                    ["ShouldVisibleInCreate"] = true,
                    ["ShouldVisibleInView"] = true,
                    ["IsCalculate"] = true
                };

                var objectMetadata = await _objectMetadataRepository.DynamicUpsertAsync(objectMetadataData, cancellationToken);
                response.InsertedCount++;
                response.CreatedIds.Add(objectMetadata.Id);
                _logger.LogDebug("Created ObjectMetadata link for Object {ObjectId} and Metadata {MetadataId}", objectId, kvp.Value);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating ObjectMetadata links");
        }
    }

    /// <summary>
    /// Create ObjectValues
    /// </summary>
    private async Task CreateObjectValues(Guid objectId, Dictionary<string, Guid> metadataMap, List<Dictionary<string, object?>> valuesList, DynamicOperationResponse response, CancellationToken cancellationToken)
    {
        try
        {
            var objectMetadataList = await _objectMetadataRepository.ListAsync(cancellationToken);

            foreach (var valuesDict in valuesList)
            {
                foreach (var kvp in valuesDict)
                {
                    var metadataKey = kvp.Key;
                    var value = kvp.Value?.ToString() ?? "";

                    // Find the metadata ID for this key
                    if (!metadataMap.TryGetValue(metadataKey, out var metadataId))
                    {
                        _logger.LogWarning("Metadata not found for key: {MetadataKey}", metadataKey);
                        continue;
                    }

                    // Find the ObjectMetadata for this object and metadata
                    var objectMetadata = objectMetadataList.FirstOrDefault(om => om.ObjectId == objectId && om.MetadataId == metadataId);
                    if (objectMetadata == null)
                    {
                        _logger.LogWarning("ObjectMetadata not found for Object {ObjectId} and Metadata {MetadataId}", objectId, metadataId);
                        continue;
                    }

                    // Create ObjectValue
                    var objectValueData = new Dictionary<string, object?>
                    {
                        ["ObjectMetadataId"] = objectMetadata.Id,
                        ["Value"] = value,
                        ["RefId"] = null,
                        ["ParentObjectValueId"] = null
                    };

                    var objectValue = await _objectValueRepository.DynamicUpsertAsync(objectValueData, cancellationToken);
                    response.InsertedCount++;
                    response.CreatedIds.Add(objectValue.Id);
                    _logger.LogDebug("Created ObjectValue for MetadataKey {MetadataKey} with value {Value}", metadataKey, value);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating ObjectValues");
        }
    }
}
