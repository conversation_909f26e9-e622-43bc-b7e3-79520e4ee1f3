using Application.ProductMetadata.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.ProductMetadata.Queries;

/// <summary>
/// Get ProductMetadata by ID query
/// </summary>
public class GetProductMetadataByIdQuery : IRequest<Result<ProductMetadataDto>>
{
    /// <summary>
    /// ProductMetadata ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public GetProductMetadataByIdQuery(Guid id)
    {
        Id = id;
    }
}
