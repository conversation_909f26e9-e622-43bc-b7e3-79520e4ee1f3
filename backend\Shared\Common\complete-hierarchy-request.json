{"operationType": 5, "includeFeatures": true, "includeObjects": true, "includeMetadata": true, "productData": [{"name": "Real Estate Management System", "description": "Complete real estate project management system with hierarchical structure", "version": "1.0.0", "isActive": true, "features": [{"name": "Property Management", "description": "Comprehensive property management with full hierarchy support", "isDefault": true, "isActive": true, "objects": [{"name": "Project", "description": "Top-level real estate project", "isActive": true, "parentObjectId": null}, {"name": "Phase", "description": "Project development phase", "isActive": true, "parentObjectId": "Project"}, {"name": "Block", "description": "Construction block within a phase", "isActive": true, "parentObjectId": "Phase"}, {"name": "Property", "description": "Individual property within a block", "isActive": true, "parentObjectId": "Block"}, {"name": "Tower", "description": "Building tower within a property", "isActive": true, "parentObjectId": "Property"}, {"name": "Floor", "description": "Floor within a tower", "isActive": true, "parentObjectId": "Tower"}, {"name": "Unit", "description": "Individual unit within a floor", "isActive": true, "parentObjectId": "Floor"}]}], "metadata": [{"metadataId": "project-name", "isUnique": true, "isActive": true}, {"metadataId": "project-code", "isUnique": true, "isActive": true}, {"metadataId": "project-location", "isUnique": false, "isActive": true}, {"metadataId": "project-budget", "isUnique": false, "isActive": true}, {"metadataId": "project-status", "isUnique": false, "isActive": true}, {"metadataId": "project-start-date", "isUnique": false, "isActive": true}, {"metadataId": "project-end-date", "isUnique": false, "isActive": true}, {"metadataId": "project-manager", "isUnique": false, "isActive": true}, {"metadataId": "project-developer", "isUnique": false, "isActive": true}, {"metadataId": "project-architect", "isUnique": false, "isActive": true}, {"metadataId": "phase-name", "isUnique": false, "isActive": true}, {"metadataId": "phase-number", "isUnique": false, "isActive": true}, {"metadataId": "phase-budget", "isUnique": false, "isActive": true}, {"metadataId": "phase-unit-count", "isUnique": false, "isActive": true}, {"metadataId": "phase-building-count", "isUnique": false, "isActive": true}, {"metadataId": "block-name", "isUnique": false, "isActive": true}, {"metadataId": "block-number", "isUnique": false, "isActive": true}, {"metadataId": "block-area", "isUnique": false, "isActive": true}, {"metadataId": "block-completion-percentage", "isUnique": false, "isActive": true}, {"metadataId": "property-name", "isUnique": false, "isActive": true}, {"metadataId": "property-code", "isUnique": true, "isActive": true}, {"metadataId": "property-type", "isUnique": false, "isActive": true}, {"metadataId": "property-address", "isUnique": false, "isActive": true}, {"metadataId": "property-plot-area", "isUnique": false, "isActive": true}, {"metadataId": "property-base-price", "isUnique": false, "isActive": true}, {"metadataId": "tower-name", "isUnique": false, "isActive": true}, {"metadataId": "tower-code", "isUnique": true, "isActive": true}, {"metadataId": "tower-floor-count", "isUnique": false, "isActive": true}, {"metadataId": "tower-basement-count", "isUnique": false, "isActive": true}, {"metadataId": "tower-unit-count", "isUnique": false, "isActive": true}, {"metadataId": "tower-height", "isUnique": false, "isActive": true}, {"metadataId": "tower-elevator-count", "isUnique": false, "isActive": true}, {"metadataId": "floor-number", "isUnique": false, "isActive": true}, {"metadataId": "floor-name", "isUnique": false, "isActive": true}, {"metadataId": "floor-is-basement", "isUnique": false, "isActive": true}, {"metadataId": "floor-is-penthouse", "isUnique": false, "isActive": true}, {"metadataId": "floor-total-area", "isUnique": false, "isActive": true}, {"metadataId": "floor-unit-count", "isUnique": false, "isActive": true}, {"metadataId": "floor-height", "isUnique": false, "isActive": true}, {"metadataId": "unit-number", "isUnique": true, "isActive": true}, {"metadataId": "unit-name", "isUnique": false, "isActive": true}, {"metadataId": "unit-type", "isUnique": false, "isActive": true}, {"metadataId": "unit-category", "isUnique": false, "isActive": true}, {"metadataId": "unit-bedrooms", "isUnique": false, "isActive": true}, {"metadataId": "unit-bathrooms", "isUnique": false, "isActive": true}, {"metadataId": "unit-carpet-area", "isUnique": false, "isActive": true}, {"metadataId": "unit-built-up-area", "isUnique": false, "isActive": true}, {"metadataId": "unit-balcony-area", "isUnique": false, "isActive": true}, {"metadataId": "unit-facing-direction", "isUnique": false, "isActive": true}, {"metadataId": "unit-view", "isUnique": false, "isActive": true}, {"metadataId": "unit-base-price", "isUnique": false, "isActive": true}, {"metadataId": "unit-total-price", "isUnique": false, "isActive": true}, {"metadataId": "unit-price-per-sqft", "isUnique": false, "isActive": true}, {"metadataId": "unit-maintenance-charges", "isUnique": false, "isActive": true}, {"metadataId": "unit-furnishing-status", "isUnique": false, "isActive": true}, {"metadataId": "unit-flooring-type", "isUnique": false, "isActive": true}, {"metadataId": "unit-kitchen-type", "isUnique": false, "isActive": true}, {"metadataId": "unit-inventory-status", "isUnique": false, "isActive": true}, {"metadataId": "unit-construction-status", "isUnique": false, "isActive": true}, {"metadataId": "unit-completion-percentage", "isUnique": false, "isActive": true}, {"metadataId": "unit-possession-date", "isUnique": false, "isActive": true}, {"metadataId": "unit-parking-allotment", "isUnique": false, "isActive": true}]}]}