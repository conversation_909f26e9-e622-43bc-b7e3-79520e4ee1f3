using Application.Features.DTOs;
using Application.Features.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.Features.Commands;

/// <summary>
/// Update feature command handler
/// </summary>
public class UpdateFeatureCommandHandler : IRequestHandler<UpdateFeatureCommand, Result<FeatureDto>>
{
    private readonly IRepository<Feature> _featureRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpdateFeatureCommandHandler(IRepository<Feature> featureRepository)
    {
        _featureRepository = featureRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<FeatureDto>> Handle(UpdateFeatureCommand request, CancellationToken cancellationToken)
    {
        // Get feature using specification (tenant isolation handled by Finbuckle.MultiTenant)
        var featureSpec = new FeatureByIdSpec(request.Id);
        var feature = await _featureRepository.GetBySpecAsync(featureSpec, cancellationToken);

        if (feature == null)
        {
            return Result<FeatureDto>.Failure("Feature not found.");
        }

        // Update feature properties
        feature.Name = request.Name;
        feature.Description = request.Description;
        feature.IsDefault = request.IsDefault;
        feature.IsActive = request.IsActive;

        await _featureRepository.UpdateAsync(feature, cancellationToken);

        var dto = feature.Adapt<FeatureDto>();
        return Result<FeatureDto>.Success(dto);
    }
}
