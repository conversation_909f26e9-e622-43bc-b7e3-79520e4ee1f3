using Application.FieldMappings.Commands;
using Application.FieldMappings.DTOs;
using Application.FieldMappings.Queries;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;

namespace Web.Host.Controllers;

/// <summary>
/// Field Mappings controller
/// </summary>
[Route("api/[controller]")]
public class FieldMappingsController : BaseApiController
{
    /// <summary>
    /// Get all field mappings with pagination
    /// </summary>
    [HttpGet]
    [TenantIdHeader]
    public async Task<ActionResult<PaginatedResult<ViewFieldMappingDto>>> GetFieldMappings([FromQuery] GetFieldMappingsQuery query)
    {
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get field mapping by ID
    /// </summary>
    [HttpGet("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ViewFieldMappingDto>>> GetFieldMappingById(Guid id)
    {
        return Ok(await Mediator.Send(new GetFieldMappingByIdQuery(id)));
    }

    /// <summary>
    /// Create a new field mapping
    /// </summary>
    [HttpPost]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ViewFieldMappingDto>>> CreateFieldMapping(CreateFieldMappingCommand command)
    {
        return Ok(await Mediator.Send(command));
    }

    /// <summary>
    /// Update an existing field mapping
    /// </summary>
    [HttpPut("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ViewFieldMappingDto>>> UpdateFieldMapping(Guid id, UpdateFieldMappingCommand command)
    {
        command.Id = id;
        return Ok(await Mediator.Send(command));
    }

    /// <summary>
    /// Delete a field mapping
    /// </summary>
    [HttpDelete("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<bool>>> DeleteFieldMapping(Guid id)
    {
        return Ok(await Mediator.Send(new DeleteFieldMappingCommand(id)));
    }

    /// <summary>
    /// Create multiple field mappings in bulk
    /// </summary>
    [HttpPost("bulk")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<List<ViewFieldMappingDto>>>> CreateFieldMappings(CreateFieldMappingsCommand command)
    {
        return Ok(await Mediator.Send(command));
    }
}
