using Application.ObjectValues.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.ObjectValues.Queries.GetObjectValue;

/// <summary>
/// Get ObjectValues with advanced filtering and pagination
/// </summary>
public class GetObjectValuesWithFiltersQuery : IRequest<PaginatedResult<ObjectValueResponseDto>>
{
    /// <summary>
    /// Page number
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Feature ID filter
    /// </summary>
    public Guid? FeatureId { get; set; }

    /// <summary>
    /// Object ID filter
    /// </summary>
    public Guid? ObjectId { get; set; }

    /// <summary>
    /// Metadata ID filter
    /// </summary>
    public Guid? MetadataId { get; set; }

    /// <summary>
    /// Reference ID filter
    /// </summary>
    public Guid? RefId { get; set; }

    /// <summary>
    /// Search term for value, metadata key, or object name
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Include child values in hierarchical structure
    /// </summary>
    public bool IncludeChildValues { get; set; } = true;

    /// <summary>
    /// Only return visible fields
    /// </summary>
    public bool OnlyVisibleFields { get; set; } = false;

    /// <summary>
    /// Only return parent values (no hierarchical children)
    /// </summary>
    public bool OnlyParentValues { get; set; } = false;

    /// <summary>
    /// Order by
    /// </summary>
    public string? OrderBy { get; set; }
}
