using Application.ConflictResolutions.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.ConflictResolutions.Commands;

/// <summary>
/// Create multiple conflict resolutions command
/// </summary>
public class CreateConflictResolutionsCommand : IRequest<Result<List<ViewConflictResolutionDto>>>
{
    /// <summary>
    /// List of conflict resolutions to create
    /// </summary>
    public List<CreateConflictResolutionRequest> ConflictResolutions { get; set; } = new();
}

/// <summary>
/// Create conflict resolution request for bulk operation
/// </summary>
public class CreateConflictResolutionRequest
{
    /// <summary>
    /// Integration ID this conflict resolution applies to
    /// </summary>
    public Guid IntegrationId { get; set; }

    /// <summary>
    /// Object ID this conflict resolution applies to
    /// </summary>
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Resolution strategy (e.g., "SourceWins", "TargetWins", "Manual", "Merge")
    /// </summary>
    public string ResolutionStrategy { get; set; } = string.Empty;

    /// <summary>
    /// Merge rules configuration stored as JSON
    /// </summary>
    public string? MergeRules { get; set; }
}
