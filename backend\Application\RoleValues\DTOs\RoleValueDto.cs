namespace Application.RoleValues.DTOs;

/// <summary>
/// RoleValue DTO
/// </summary>
public class RoleValueDto
{
    /// <summary>
    /// RoleValue ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Role metadata ID
    /// </summary>
    public Guid RoleMetadataId { get; set; }

    /// <summary>
    /// Role name
    /// </summary>
    public string? RoleName { get; set; }

    /// <summary>
    /// Metadata key
    /// </summary>
    public string? MetadataKey { get; set; }

    /// <summary>
    /// Reference ID for grouping related values
    /// </summary>
    public Guid? RefId { get; set; }

    /// <summary>
    /// Parent role value ID for hierarchical structure
    /// </summary>
    public Guid? ParentRoleValueId { get; set; }

    /// <summary>
    /// The actual value as text
    /// </summary>
    public string? Value { get; set; }

    /// <summary>
    /// Created at
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by
    /// </summary>
    public Guid CreatedBy { get; set; }

    /// <summary>
    /// Modified at
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Modified by
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}
