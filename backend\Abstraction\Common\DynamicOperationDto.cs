namespace Abstraction.Common;

/// <summary>
/// Base DTO for dynamic operations
/// </summary>
public abstract class DynamicOperationDto
{
    /// <summary>
    /// Entity type name
    /// </summary>
    public string EntityType { get; set; } = string.Empty;

    /// <summary>
    /// Operation type
    /// </summary>
    public DynamicOperationType OperationType { get; set; }

    /// <summary>
    /// Tenant ID for multi-tenant operations
    /// </summary>
    public string? TenantId { get; set; }
}

/// <summary>
/// DTO for single dynamic operation
/// </summary>
public class DynamicOperationRequest : DynamicOperationDto
{
    /// <summary>
    /// Property values as key-value pairs
    /// </summary>
    public Dictionary<string, object?> PropertyValues { get; set; } = new();

    /// <summary>
    /// Key properties for upsert operations
    /// </summary>
    public List<string> KeyProperties { get; set; } = new();
}

/// <summary>
/// DTO for bulk dynamic operations
/// </summary>
public class BulkDynamicOperationRequest : DynamicOperationDto
{
    /// <summary>
    /// List of property values for bulk operations
    /// </summary>
    public List<Dictionary<string, object?>> PropertyValuesList { get; set; } = new();

    /// <summary>
    /// Key properties for upsert operations
    /// </summary>
    public List<string> KeyProperties { get; set; } = new();

    /// <summary>
    /// Batch size for processing
    /// </summary>
    public int BatchSize { get; set; } = 1000;
}

/// <summary>
/// Response DTO for dynamic operations
/// </summary>
public class DynamicOperationResponse
{
    /// <summary>
    /// Whether the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Operation message
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Number of entities processed
    /// </summary>
    public int ProcessedCount { get; set; }

    /// <summary>
    /// Number of entities inserted
    /// </summary>
    public int InsertedCount { get; set; }

    /// <summary>
    /// Number of entities updated
    /// </summary>
    public int UpdatedCount { get; set; }

    /// <summary>
    /// Number of entities that failed processing
    /// </summary>
    public int FailedCount { get; set; }

    /// <summary>
    /// List of errors encountered
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Execution time in milliseconds
    /// </summary>
    public long ExecutionTimeMs { get; set; }

    /// <summary>
    /// Created entity IDs (for insert operations)
    /// </summary>
    public List<Guid> CreatedIds { get; set; } = new();
}

/// <summary>
/// Dynamic operation types
/// </summary>
public enum DynamicOperationType
{
    /// <summary>
    /// Insert operation
    /// </summary>
    Insert,

    /// <summary>
    /// Update operation
    /// </summary>
    Update,

    /// <summary>
    /// Upsert operation (insert or update)
    /// </summary>
    Upsert,

    /// <summary>
    /// Bulk insert operation
    /// </summary>
    BulkInsert,

    /// <summary>
    /// Bulk update operation
    /// </summary>
    BulkUpdate,

    /// <summary>
    /// Bulk upsert operation
    /// </summary>
    BulkUpsert
}

/// <summary>
/// Dynamic operation configuration
/// </summary>
public class DynamicOperationConfig
{
    /// <summary>
    /// Maximum batch size for bulk operations
    /// </summary>
    public int MaxBatchSize { get; set; } = 5000;

    /// <summary>
    /// Default batch size for bulk operations
    /// </summary>
    public int DefaultBatchSize { get; set; } = 1000;

    /// <summary>
    /// Whether to validate entities before operations
    /// </summary>
    public bool ValidateEntities { get; set; } = true;

    /// <summary>
    /// Whether to use transactions for bulk operations
    /// </summary>
    public bool UseTransactions { get; set; } = true;

    /// <summary>
    /// Timeout for operations in seconds
    /// </summary>
    public int TimeoutSeconds { get; set; } = 300;
}
