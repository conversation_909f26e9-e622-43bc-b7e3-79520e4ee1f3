﻿using Domain.Common.Contracts;
using Domain.Entities;
using Finbuckle.MultiTenant;
using Infrastructure.Database.Extensions;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Database
{
    public abstract class BaseDbContext : MultiTenantIdentityDbContext<User, Role, Guid>
    {
        protected BaseDbContext(ITenantInfo currentTenant, DbContextOptions options)
            : base(currentTenant, options)
        {
        }

        //public DbSet<Trail> AuditTrails => Set<Trail>();

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // QueryFilters need to be applied before base.OnModelCreating
            modelBuilder.AppendGlobalQueryFilter<ISoftDelete>(s => s.IsDeleted == false);

            base.OnModelCreating(modelBuilder);

            modelBuilder.ApplyConfigurationsFromAssembly(GetType().Assembly);
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            // TODO: We want this only for development probably... maybe better make it configurable in logger.json config?
            optionsBuilder.EnableSensitiveDataLogging();

            // If you want to see the sql queries that efcore executes:

            // Uncomment the next line to see them in the output window of visual studio
            // optionsBuilder.LogTo(m => System.Diagnostics.Debug.WriteLine(m), Microsoft.Extensions.Logging.LogLevel.Information);

            // Or uncomment the next line if you want to see them in the console
            // optionsBuilder.LogTo(Console.WriteLine, Microsoft.Extensions.Logging.LogLevel.Information);

            if (!string.IsNullOrWhiteSpace(TenantInfo?.ConnectionString))
            {
                optionsBuilder.UseDatabase("PostgreSQL", TenantInfo.ConnectionString);
            }

            optionsBuilder.EnableSensitiveDataLogging(true);
        }
    }
}
