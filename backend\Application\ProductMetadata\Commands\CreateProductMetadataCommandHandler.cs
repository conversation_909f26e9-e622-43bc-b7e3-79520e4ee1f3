using Application.ProductMetadata.DTOs;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.ProductMetadata.Commands;

/// <summary>
/// Create ProductMetadata command handler
/// </summary>
public class CreateProductMetadataCommandHandler : IRequestHandler<CreateProductMetadataCommand, Result<ProductMetadataDto>>
{
    private readonly IRepository<Domain.Entities.ProductMetadata> _repository;
    private readonly IRepository<Product> _productRepository;
    private readonly IRepository<Domain.Entities.Metadata> _metadataRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateProductMetadataCommandHandler(
        IRepository<Domain.Entities.ProductMetadata> repository,
        IRepository<Product> productRepository,
        IRepository<Domain.Entities.Metadata> metadataRepository)
    {
        _repository = repository;
        _productRepository = productRepository;
        _metadataRepository = metadataRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<ProductMetadataDto>> Handle(CreateProductMetadataCommand request, CancellationToken cancellationToken)
    {
        // Validate Product exists
        var product = await _productRepository.GetByIdAsync(request.ProductId, cancellationToken);
        if (product == null)
        {
            return Result<ProductMetadataDto>.Failure($"Product with ID '{request.ProductId}' not found.");
        }

        // Validate Metadata exists
        var metadata = await _metadataRepository.GetByIdAsync(request.MetadataId, cancellationToken);
        if (metadata == null)
        {
            return Result<ProductMetadataDto>.Failure($"Metadata with ID '{request.MetadataId}' not found.");
        }

        // Create ProductMetadata
        var productMetadata = new Domain.Entities.ProductMetadata
        {
            ProductId = request.ProductId,
            MetadataId = request.MetadataId,
            IsUnique = request.IsUnique,
            IsActive = request.IsActive
        };

        await _repository.AddAsync(productMetadata, cancellationToken);

        var dto = new ProductMetadataDto
        {
            Id = productMetadata.Id,
            ProductId = productMetadata.ProductId,
            ProductName = product.Name,
            MetadataId = productMetadata.MetadataId,
            MetadataKey = metadata.MetadataKey,
            MetadataDisplayLabel = metadata.DisplayLabel,
            IsUnique = productMetadata.IsUnique,
            IsActive = productMetadata.IsActive,
            ValuesCount = 0,
            CreatedAt = productMetadata.CreatedAt,
            CreatedBy = productMetadata.CreatedBy ?? Guid.Empty,
            ModifiedAt = productMetadata.ModifiedAt,
            ModifiedBy = productMetadata.ModifiedBy
        };

        return Result<ProductMetadataDto>.Success(dto);
    }
}
