using Application.FieldMappings.DTOs;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.FieldMappings.Commands;

/// <summary>
/// Create field mapping command handler
/// </summary>
public class CreateFieldMappingCommandHandler : IRequestHandler<CreateFieldMappingCommand, Result<ViewFieldMappingDto>>
{
    private readonly IRepository<FieldMapping> _fieldMappingRepository;
    private readonly IReadRepository<Domain.Entities.ObjectMetadata> _objectMetadataRepository;
    private readonly IReadRepository<User> _userRepository;
    private readonly IReadRepository<Role> _roleRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateFieldMappingCommandHandler(
        IRepository<FieldMapping> fieldMappingRepository,
        IReadRepository<Domain.Entities.ObjectMetadata> objectMetadataRepository,
        IReadRepository<User> userRepository,
        IReadRepository<Role> roleRepository)
    {
        _fieldMappingRepository = fieldMappingRepository;
        _objectMetadataRepository = objectMetadataRepository;
        _userRepository = userRepository;
        _roleRepository = roleRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<ViewFieldMappingDto>> Handle(CreateFieldMappingCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Validate optional foreign keys
            Domain.Entities.ObjectMetadata? objectMetadata = null;
            if (request.ObjectMetadataId.HasValue)
            {
                objectMetadata = await _objectMetadataRepository.GetByIdAsync(request.ObjectMetadataId.Value, cancellationToken);
                if (objectMetadata == null)
                {
                    return Result<ViewFieldMappingDto>.Failure("Object metadata not found.");
                }
            }

            User? user = null;
            if (request.UserId.HasValue)
            {
                user = await _userRepository.GetByIdAsync(request.UserId.Value, cancellationToken);
                if (user == null)
                {
                    return Result<ViewFieldMappingDto>.Failure("User not found.");
                }
            }

            Role? role = null;
            if (request.RoleId.HasValue)
            {
                role = await _roleRepository.GetByIdAsync(request.RoleId.Value, cancellationToken);
                if (role == null)
                {
                    return Result<ViewFieldMappingDto>.Failure("Role not found.");
                }
            }

            // Create new field mapping
            var fieldMapping = new FieldMapping
            {
                ApiName = request.ApiName,
                SourceField = request.SourceField,
                SourceType = request.SourceType,
                ObjectMetadataId = request.ObjectMetadataId,
                UserId = request.UserId,
                RoleId = request.RoleId,
                TargetObjectName = request.TargetObjectName,
                Notes = request.Notes
            };

            var createdFieldMapping = await _fieldMappingRepository.AddAsync(fieldMapping, cancellationToken);

            // Create view DTO
            var viewDto = new ViewFieldMappingDto
            {
                Id = createdFieldMapping.Id,
                ApiName = createdFieldMapping.ApiName,
                SourceField = createdFieldMapping.SourceField,
                SourceType = createdFieldMapping.SourceType,
                ObjectMetadataId = createdFieldMapping.ObjectMetadataId,
                ObjectMetadataKey = objectMetadata?.Metadata?.MetadataKey,
                UserId = createdFieldMapping.UserId,
                UserName = user?.UserName,
                RoleId = createdFieldMapping.RoleId,
                RoleName = role?.Name,
                TargetObjectName = createdFieldMapping.TargetObjectName,
                Notes = createdFieldMapping.Notes,
                CreatedAt = createdFieldMapping.CreatedAt,
                CreatedBy = createdFieldMapping.CreatedBy,
                ModifiedAt = createdFieldMapping.ModifiedAt,
                ModifiedBy = createdFieldMapping.ModifiedBy
            };

            return Result<ViewFieldMappingDto>.Success(viewDto);
        }
        catch (Exception ex)
        {
            return Result<ViewFieldMappingDto>.Failure($"Failed to create field mapping: {ex.Message}");
        }
    }
}
