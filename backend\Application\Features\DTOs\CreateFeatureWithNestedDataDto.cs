using System.ComponentModel.DataAnnotations;
using System.Text.Json;

namespace Application.Features.DTOs;

/// <summary>
/// Request DTO for creating a Feature with deeply nested objects and metadata
/// </summary>
public class CreateFeatureWithNestedDataRequestDto
{
    /// <summary>
    /// Feature information
    /// </summary>
    [Required]
    public FeatureCreationDto Feature { get; set; } = new();
}

/// <summary>
/// Feature creation DTO with nested objects
/// </summary>
public class FeatureCreationDto
{
    /// <summary>
    /// Feature ID (if null, a new feature will be created)
    /// </summary>
    public Guid? FeatureId { get; set; }

    /// <summary>
    /// Product ID this feature belongs to
    /// </summary>
    [Required]
    public Guid ProductId { get; set; }

    /// <summary>
    /// Feature name
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Feature description
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Whether this is the default feature
    /// </summary>
    public bool IsDefault { get; set; } = false;

    /// <summary>
    /// Whether the feature is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Feature metadata as JSON (dynamic structure)
    /// </summary>
    public JsonElement? MetaJson { get; set; }

    /// <summary>
    /// Feature values (key-value pairs)
    /// </summary>
    public Dictionary<string, object> Values { get; set; } = new();

    /// <summary>
    /// Objects belonging to this feature
    /// </summary>
    public List<ObjectCreationDto> Objects { get; set; } = new();
}

/// <summary>
/// Object creation DTO with nested metadata and child objects
/// </summary>
public class ObjectCreationDto
{
    /// <summary>
    /// Object name
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Object description
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Whether the object is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Object metadata as JSON (dynamic structure)
    /// </summary>
    public JsonElement? MetaJson { get; set; }

    /// <summary>
    /// Object metadata values (array of value instances)
    /// </summary>
    public List<Dictionary<string, object>> MetaValues { get; set; } = new();

    /// <summary>
    /// Child objects with their own metadata and values
    /// </summary>
    public List<ChildObjectCreationDto> ChildObjects { get; set; } = new();
}

/// <summary>
/// Child object creation DTO
/// </summary>
public class ChildObjectCreationDto
{
    /// <summary>
    /// Child object name
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Child object description
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Whether the child object is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Child object metadata as JSON (dynamic structure)
    /// </summary>
    public JsonElement? MetaJson { get; set; }

    /// <summary>
    /// Child object metadata values (array of value instances)
    /// </summary>
    public List<Dictionary<string, object>> MetaValues { get; set; } = new();
}

/// <summary>
/// Response DTO for feature creation with nested data
/// </summary>
public class CreateFeatureWithNestedDataResponseDto
{
    /// <summary>
    /// Created feature information
    /// </summary>
    public FeatureCreationResultDto Feature { get; set; } = new();

    /// <summary>
    /// Created objects with their IDs
    /// </summary>
    public List<ObjectCreationResultDto> Objects { get; set; } = new();

    /// <summary>
    /// Total number of metadata entries created
    /// </summary>
    public int MetadataCreated { get; set; }

    /// <summary>
    /// Total number of object values created
    /// </summary>
    public int ObjectValuesCreated { get; set; }

    /// <summary>
    /// Total number of child objects created
    /// </summary>
    public int ChildObjectsCreated { get; set; }

    /// <summary>
    /// Total number of child object values created
    /// </summary>
    public int ChildObjectValuesCreated { get; set; }

    /// <summary>
    /// Processing summary
    /// </summary>
    public ProcessingSummaryDto ProcessingSummary { get; set; } = new();
}

/// <summary>
/// Feature creation result DTO
/// </summary>
public class FeatureCreationResultDto
{
    /// <summary>
    /// Feature ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Feature name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Product ID
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Whether this was a new feature or existing
    /// </summary>
    public bool IsNewFeature { get; set; }

    /// <summary>
    /// Number of feature metadata entries created
    /// </summary>
    public int FeatureMetadataCreated { get; set; }

    /// <summary>
    /// Number of feature values created
    /// </summary>
    public int FeatureValuesCreated { get; set; }
}

/// <summary>
/// Object creation result DTO
/// </summary>
public class ObjectCreationResultDto
{
    /// <summary>
    /// Object ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Object name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Feature ID this object belongs to
    /// </summary>
    public Guid FeatureId { get; set; }

    /// <summary>
    /// Number of metadata entries created for this object
    /// </summary>
    public int MetadataCreated { get; set; }

    /// <summary>
    /// Number of object values created
    /// </summary>
    public int ObjectValuesCreated { get; set; }

    /// <summary>
    /// Child objects created
    /// </summary>
    public List<ChildObjectCreationResultDto> ChildObjects { get; set; } = new();
}

/// <summary>
/// Child object creation result DTO
/// </summary>
public class ChildObjectCreationResultDto
{
    /// <summary>
    /// Child object ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Child object name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Parent object ID
    /// </summary>
    public Guid ParentObjectId { get; set; }

    /// <summary>
    /// Number of metadata entries created for this child object
    /// </summary>
    public int MetadataCreated { get; set; }

    /// <summary>
    /// Number of child object values created
    /// </summary>
    public int ObjectValuesCreated { get; set; }
}

/// <summary>
/// Processing summary DTO
/// </summary>
public class ProcessingSummaryDto
{
    /// <summary>
    /// Total processing time in milliseconds
    /// </summary>
    public long ProcessingTimeMs { get; set; }

    /// <summary>
    /// Number of database transactions executed
    /// </summary>
    public int TransactionsExecuted { get; set; }

    /// <summary>
    /// Any warnings encountered during processing
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// Data type mappings used
    /// </summary>
    public Dictionary<string, Guid> DataTypeMappings { get; set; } = new();
}
