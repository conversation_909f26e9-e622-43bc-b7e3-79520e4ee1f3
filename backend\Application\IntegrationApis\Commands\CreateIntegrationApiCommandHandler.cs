using Application.IntegrationApis.DTOs;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.IntegrationApis.Commands;

/// <summary>
/// Create integration API command handler
/// </summary>
public class CreateIntegrationApiCommandHandler : IRequestHandler<CreateIntegrationApiCommand, Result<ViewIntegrationApiDto>>
{
    private readonly IRepositoryWithEvents<IntegrationApi> _integrationApiRepository;
    private readonly IReadRepository<Product> _productRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateIntegrationApiCommandHandler(
        IRepositoryWithEvents<IntegrationApi> integrationApiRepository,
        IReadRepository<Product> productRepository)
    {
        _integrationApiRepository = integrationApiRepository;
        _productRepository = productRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<ViewIntegrationApiDto>> Handle(CreateIntegrationApiCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Validate product exists
            var product = await _productRepository.GetByIdAsync(request.ProductId, cancellationToken);
            if (product == null)
            {
                return Result<ViewIntegrationApiDto>.Failure($"Product with ID {request.ProductId} not found.");
            }

            // Check if integration API with same name already exists for this product
            var existingApis = await _integrationApiRepository.ListAsync(cancellationToken);
            var existingApi = existingApis.FirstOrDefault(ia =>
                ia.ProductId == request.ProductId &&
                ia.Name == request.Name &&
                !ia.IsDeleted);

            if (existingApi != null)
            {
                return Result<ViewIntegrationApiDto>.Failure($"Integration API with name '{request.Name}' already exists for this product.");
            }

            // Create integration API
            var integrationApi = new IntegrationApi
            {
                Id = Guid.NewGuid(),
                ProductId = request.ProductId,
                Name = request.Name,
                EndpointUrl = request.EndpointUrl,
                Schema = request.Schema,
                IsActive = request.IsActive,
                CreatedAt = DateTime.UtcNow,
                ModifiedAt = DateTime.UtcNow
            };

            var savedIntegrationApi = await _integrationApiRepository.AddAsync(integrationApi, cancellationToken);

            // Load with product for DTO mapping
            var integrationApiWithProduct = await _integrationApiRepository.GetByIdAsync(savedIntegrationApi.Id, cancellationToken);
            var productForDto = await _productRepository.GetByIdAsync(integrationApiWithProduct.ProductId, cancellationToken);

            var integrationApiDto = integrationApiWithProduct.Adapt<ViewIntegrationApiDto>();
            integrationApiDto.ProductName = productForDto?.Name ?? string.Empty;

            return Result<ViewIntegrationApiDto>.Success(integrationApiDto);
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
            return Result<ViewIntegrationApiDto>.Failure($"Failed to create integration API: {ex.Message}");
        }
    }
}
